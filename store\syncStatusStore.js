'use client';

// SyncStatusStore - 同步状态管理
// 使用Zustand管理跨模块数据同步状态

import { create } from 'zustand';
import { nameSyncManager } from '../lib/NameSyncManager';

export const useSyncStatusStore = create((set, get) => ({
  // 同步状态
  syncStatus: {
    isSync: false,
    lastSyncTime: null,
    pendingSync: [],     // 待同步的字段 [{ fieldId, newName, moduleId, timestamp }]
    syncErrors: [],      // 同步错误 [{ fieldId, error, timestamp }]
    syncHistory: [],     // 同步历史 [{ fieldId, status, timestamp, duration }]
    totalSynced: 0,      // 总同步次数
    successRate: 100     // 成功率
  },
  
  // 自定义字段名称缓存
  customNames: {
    // moduleId -> { fieldId -> customName }
  },
  
  // 开始同步
  startSync: (fieldId, newName, moduleId) => {
    const timestamp = Date.now();
    
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        isSync: true,
        pendingSync: [
          ...state.syncStatus.pendingSync.filter(item => item.fieldId !== fieldId),
          { fieldId, newName, moduleId, timestamp }
        ]
      }
    }));
    
    console.log(`开始同步: ${fieldId} -> ${newName}`);
  },
  
  // 完成同步
  completeSync: (fieldId, result) => {
    const timestamp = Date.now();
    const state = get();
    const pendingItem = state.syncStatus.pendingSync.find(item => item.fieldId === fieldId);
    
    if (!pendingItem) {
      console.warn(`未找到待同步项: ${fieldId}`);
      return;
    }
    
    const duration = timestamp - pendingItem.timestamp;
    const isSuccess = result.success;
    
    set(state => {
      const newHistory = [
        ...state.syncStatus.syncHistory,
        {
          fieldId,
          newName: pendingItem.newName,
          moduleId: pendingItem.moduleId,
          status: isSuccess ? 'success' : 'failed',
          timestamp,
          duration,
          affectedModules: result.affectedModules || [],
          successCount: result.successCount || 0,
          failedCount: result.failedCount || 0
        }
      ];
      
      // 保持历史记录在合理范围内
      const maxHistorySize = 100;
      const trimmedHistory = newHistory.slice(-maxHistorySize);
      
      // 计算成功率
      const recentHistory = trimmedHistory.slice(-20); // 最近20次
      const successCount = recentHistory.filter(h => h.status === 'success').length;
      const successRate = recentHistory.length > 0 
        ? Math.round((successCount / recentHistory.length) * 100) 
        : 100;
      
      return {
        syncStatus: {
          ...state.syncStatus,
          isSync: false,
          lastSyncTime: timestamp,
          pendingSync: state.syncStatus.pendingSync.filter(item => item.fieldId !== fieldId),
          syncHistory: trimmedHistory,
          totalSynced: state.syncStatus.totalSynced + 1,
          successRate
        }
      };
    });
    
    console.log(`同步完成: ${fieldId}, 成功: ${isSuccess}, 耗时: ${duration}ms`);
  },
  
  // 同步失败
  syncError: (fieldId, error) => {
    const timestamp = Date.now();
    
    set(state => {
      const newErrors = [
        ...state.syncStatus.syncErrors,
        { fieldId, error: error.message || error, timestamp }
      ];
      
      // 保持错误记录在合理范围内
      const maxErrorSize = 50;
      const trimmedErrors = newErrors.slice(-maxErrorSize);
      
      return {
        syncStatus: {
          ...state.syncStatus,
          isSync: false,
          syncErrors: trimmedErrors,
          pendingSync: state.syncStatus.pendingSync.filter(item => item.fieldId !== fieldId)
        }
      };
    });
    
    console.error(`同步失败: ${fieldId}`, error);
  },
  
  // 更新自定义名称缓存
  updateCustomName: (moduleId, fieldId, customName) => {
    set(state => ({
      customNames: {
        ...state.customNames,
        [moduleId]: {
          ...state.customNames[moduleId],
          [fieldId]: customName
        }
      }
    }));
  },
  
  // 获取自定义名称
  getCustomName: (moduleId, fieldId) => {
    const state = get();
    return state.customNames[moduleId]?.[fieldId] || null;
  },
  
  // 批量更新自定义名称
  batchUpdateCustomNames: (moduleId, nameMap) => {
    set(state => ({
      customNames: {
        ...state.customNames,
        [moduleId]: {
          ...state.customNames[moduleId],
          ...nameMap
        }
      }
    }));
  },
  
  // 执行字段名称同步
  syncFieldName: async (fieldId, newName, moduleId) => {
    const { startSync, completeSync, syncError, updateCustomName } = get();
    
    try {
      // 开始同步
      startSync(fieldId, newName, moduleId);
      
      // 执行同步
      const result = await nameSyncManager.syncFieldName(fieldId, newName, moduleId);
      
      // 更新本地缓存
      updateCustomName(moduleId, fieldId, newName);
      
      // 完成同步
      completeSync(fieldId, result);
      
      return result;
    } catch (error) {
      // 同步失败
      syncError(fieldId, error);
      throw error;
    }
  },
  
  // 清除同步历史
  clearSyncHistory: () => {
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        syncHistory: [],
        syncErrors: [],
        totalSynced: 0,
        successRate: 100
      }
    }));
  },
  
  // 清除错误记录
  clearSyncErrors: () => {
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        syncErrors: []
      }
    }));
  },
  
  // 获取同步统计信息
  getSyncStats: () => {
    const state = get();
    const { syncHistory, syncErrors, totalSynced, successRate } = state.syncStatus;
    
    // 计算平均同步时间
    const recentHistory = syncHistory.slice(-10);
    const avgDuration = recentHistory.length > 0
      ? Math.round(recentHistory.reduce((sum, h) => sum + h.duration, 0) / recentHistory.length)
      : 0;
    
    // 统计最近的错误
    const recentErrors = syncErrors.filter(e => 
      Date.now() - e.timestamp < 24 * 60 * 60 * 1000 // 24小时内
    );
    
    return {
      totalSynced,
      successRate,
      avgDuration,
      recentErrorCount: recentErrors.length,
      lastSyncTime: state.syncStatus.lastSyncTime,
      isSync: state.syncStatus.isSync,
      pendingCount: state.syncStatus.pendingSync.length
    };
  },
  
  // 重试失败的同步
  retryFailedSync: async (fieldId) => {
    const state = get();
    const failedItem = state.syncStatus.syncHistory
      .filter(h => h.status === 'failed' && h.fieldId === fieldId)
      .pop();
    
    if (!failedItem) {
      throw new Error(`未找到失败的同步记录: ${fieldId}`);
    }
    
    return await get().syncFieldName(fieldId, failedItem.newName, failedItem.moduleId);
  },
  
  // 初始化同步状态
  initSyncStatus: () => {
    // 从NameSyncManager获取状态
    const managerStatus = nameSyncManager.getSyncStatus();
    const history = nameSyncManager.getSyncHistory(50);
    const errors = nameSyncManager.getSyncErrors(20);
    
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        isSync: managerStatus.isSync,
        lastSyncTime: managerStatus.lastSyncTime,
        syncHistory: history.map(h => ({
          fieldId: h.fieldId,
          newName: h.newName,
          moduleId: h.sourceModuleId,
          status: h.status,
          timestamp: h.timestamp.getTime(),
          duration: h.duration,
          affectedModules: h.affectedModules,
          successCount: h.successCount,
          failedCount: h.failedCount
        })),
        syncErrors: errors.map(e => ({
          fieldId: e.fieldId,
          error: e.error || (e.errors && e.errors.join(', ')),
          timestamp: e.timestamp.getTime()
        })),
        totalSynced: history.length
      }
    }));
  }
}));

// 初始化同步状态
if (typeof window !== 'undefined') {
  // 延迟初始化，确保组件已挂载
  setTimeout(() => {
    useSyncStatusStore.getState().initSyncStatus();
  }, 100);
}

export default useSyncStatusStore;
