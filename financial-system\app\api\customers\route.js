// 客户API路由 - JavaScript版本
// 避免TypeScript版本冲突

import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';

// 获取客户列表
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    
    const whereClause = agentId ? { agentId: parseInt(agentId) } : {};
    
    const customers = await prisma.customer.findMany({
      where: whereClause,
      include: {
        agent: true,
        financialData: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(customers);
  } catch (error) {
    console.error('获取客户列表失败:', error);
    return NextResponse.json(
      { error: '获取客户列表失败' }, 
      { status: 500 }
    );
  }
}

// 创建新客户
export async function POST(request) {
  try {
    const data = await request.json();
    
    // 数据验证
    if (!data.name || data.name.trim() === '') {
      return NextResponse.json(
        { error: '客户名称不能为空' }, 
        { status: 400 }
      );
    }
    
    if (!data.agentId) {
      return NextResponse.json(
        { error: '代理商ID不能为空' }, 
        { status: 400 }
      );
    }
    
    // 检查代理商是否存在
    const agent = await prisma.agent.findUnique({
      where: { id: parseInt(data.agentId) }
    });
    
    if (!agent) {
      return NextResponse.json(
        { error: '代理商不存在' }, 
        { status: 404 }
      );
    }
    
    // 创建客户
    const customer = await prisma.customer.create({
      data: {
        name: data.name.trim(),
        userName: data.userName?.trim() || '',
        agentId: parseInt(data.agentId),
        data1: data.data1 ? parseFloat(data.data1) : null,
        data2: data.data2 ? parseFloat(data.data2) : null,
        data3: data.data3 ? parseFloat(data.data3) : null,
        data4: data.data4 ? parseFloat(data.data4) : null,
        data5: data.data5 ? parseFloat(data.data5) : null,
      },
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    // 初始化计算结果为0
    const updatedCustomer = await prisma.customer.update({
      where: { id: customer.id },
      data: {
        data6: 0,
        data7: 0,
        data8: 0,
      },
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    return NextResponse.json(updatedCustomer, { status: 201 });
  } catch (error) {
    console.error('创建客户失败:', error);
    
    // 处理唯一约束错误
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: '客户名称已存在' }, 
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: '创建客户失败' }, 
      { status: 500 }
    );
  }
}

// 批量更新客户 - 彻底修复版本
export async function PATCH(request) {
  try {
    console.log('批量更新API调用开始');
    const { updates } = await request.json();
    console.log('接收到的更新数据:', updates);

    if (!Array.isArray(updates)) {
      console.error('更新数据格式错误: 不是数组');
      return NextResponse.json(
        { error: '更新数据格式错误: 需要数组格式' },
        { status: 400 }
      );
    }

    if (updates.length === 0) {
      console.log('没有需要更新的数据');
      return NextResponse.json([]);
    }

    const updatedCustomers = [];

    // 使用事务批量更新，增强错误处理
    await prisma.$transaction(async (tx) => {
      for (const update of updates) {
        try {
          if (!update.id) {
            console.warn('跳过无ID的更新项:', update);
            continue;
          }

          console.log(`更新客户 ${update.id}:`, update);

          // 准备更新数据，过滤undefined值
          const updateData = {};
          if (update.name !== undefined) updateData.name = update.name?.trim() || '';
          if (update.userName !== undefined) updateData.userName = update.userName?.trim() || '';
          if (update.data1 !== undefined) updateData.data1 = update.data1 ? parseFloat(update.data1) : 0;
          if (update.data2 !== undefined) updateData.data2 = update.data2 ? parseFloat(update.data2) : 0;
          if (update.data3 !== undefined) updateData.data3 = update.data3 ? parseFloat(update.data3) : 0;
          if (update.data4 !== undefined) updateData.data4 = update.data4 ? parseFloat(update.data4) : 0;
          if (update.data5 !== undefined) updateData.data5 = update.data5 ? parseFloat(update.data5) : 0;
          if (update.data6 !== undefined) updateData.data6 = update.data6 ? parseFloat(update.data6) : 0;
          if (update.data7 !== undefined) updateData.data7 = update.data7 ? parseFloat(update.data7) : 0;
          if (update.data8 !== undefined) updateData.data8 = update.data8 ? parseFloat(update.data8) : 0;

          console.log(`客户 ${update.id} 最终更新数据:`, updateData);

          // 检查客户是否存在
          const existingCustomer = await tx.customer.findUnique({
            where: { id: update.id }
          });

          if (!existingCustomer) {
            console.warn(`客户 ${update.id} 不存在，跳过更新`);
            continue;
          }

          // 更新客户数据
          const updatedCustomer = await tx.customer.update({
            where: { id: update.id },
            data: updateData,
            include: {
              agent: true,
              financialData: true,
            }
          });

          console.log(`客户 ${update.id} 更新成功:`, updatedCustomer);
          updatedCustomers.push(updatedCustomer);

        } catch (updateError) {
          console.error(`更新客户 ${update.id} 失败:`, updateError);
          // 继续处理其他客户，不中断整个事务
          throw updateError; // 但仍然抛出错误以回滚事务
        }
      }
    }, {
      timeout: 10000, // 10秒超时
    });

    console.log('批量更新完成，成功更新客户数量:', updatedCustomers.length);
    return NextResponse.json({
      success: true,
      updatedCount: updatedCustomers.length,
      customers: updatedCustomers
    });

  } catch (error) {
    console.error('批量更新客户失败:', error);

    // 详细的错误信息
    let errorMessage = '批量更新客户失败';
    let statusCode = 500;

    if (error.code === 'P2025') {
      errorMessage = '要更新的客户不存在';
      statusCode = 404;
    } else if (error.code === 'P2002') {
      errorMessage = '数据冲突，可能存在重复的客户名称';
      statusCode = 409;
    } else if (error.message.includes('timeout')) {
      errorMessage = '更新操作超时，请稍后重试';
      statusCode = 408;
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: error.message,
        code: error.code || 'UNKNOWN'
      },
      { status: statusCode }
    );
  }
}
