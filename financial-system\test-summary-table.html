<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇总表功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #1890ff;
            background-color: #f0f8ff;
        }
        .success {
            border-left-color: #52c41a;
            background-color: #f6ffed;
        }
        .error {
            border-left-color: #ff4d4f;
            background-color: #fff2f0;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .status {
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🧪 财务管理系统 - 汇总表功能测试</h1>
    
    <div class="test-container">
        <h2>测试步骤</h2>
        <div class="test-step">
            <strong>步骤 1:</strong> 访问主仪表盘
            <button onclick="testDashboard()">测试 Dashboard</button>
            <span id="dashboard-status" class="status"></span>
        </div>
        
        <div class="test-step">
            <strong>步骤 2:</strong> 点击"数据录入"按钮跳转到汇总页面
            <button onclick="testSummaryNavigation()">测试汇总页面跳转</button>
            <span id="navigation-status" class="status"></span>
        </div>
        
        <div class="test-step">
            <strong>步骤 3:</strong> 点击"查看汇总表"按钮
            <button onclick="testSummaryToggle()">测试汇总表切换</button>
            <span id="toggle-status" class="status"></span>
        </div>
        
        <div class="test-step">
            <strong>步骤 4:</strong> 切换到客户归纳汇总表
            <button onclick="testConsolidatedTable()">测试客户归纳汇总表</button>
            <span id="consolidated-status" class="status"></span>
        </div>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="test-results">
            <p>点击上方按钮开始测试...</p>
        </div>
    </div>

    <div class="test-container">
        <h2>快速链接</h2>
        <button onclick="window.open('http://localhost:3000', '_blank')">打开主页</button>
        <button onclick="window.open('http://localhost:3000/dashboard', '_blank')">打开仪表盘</button>
        <button onclick="window.open('http://localhost:3000/summary/1', '_blank')">打开汇总页面</button>
        <button onclick="window.open('http://localhost:3000/login', '_blank')">打开登录页面</button>
    </div>

    <script>
        function updateStatus(elementId, status, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = status;
            element.style.color = isSuccess ? '#52c41a' : '#ff4d4f';
        }

        function addTestResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('test-results');
            const resultItem = document.createElement('div');
            resultItem.className = isSuccess ? 'test-step success' : 'test-step error';
            resultItem.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultItem);
        }

        async function testDashboard() {
            try {
                updateStatus('dashboard-status', '测试中...', true);
                
                // 测试dashboard API
                const response = await fetch('http://localhost:3000/api/agents');
                if (response.ok) {
                    const agents = await response.json();
                    updateStatus('dashboard-status', `✅ 成功 (${agents.length}个代理商)`, true);
                    addTestResult(`Dashboard API测试成功，找到${agents.length}个代理商`, true);
                } else {
                    throw new Error(`API响应错误: ${response.status}`);
                }
            } catch (error) {
                updateStatus('dashboard-status', '❌ 失败', false);
                addTestResult(`Dashboard测试失败: ${error.message}`, false);
            }
        }

        async function testSummaryNavigation() {
            try {
                updateStatus('navigation-status', '测试中...', true);
                
                // 测试汇总页面API
                const response = await fetch('http://localhost:3000/api/customers?agentId=1');
                if (response.ok) {
                    const customers = await response.json();
                    updateStatus('navigation-status', `✅ 成功 (${customers.length}个客户)`, true);
                    addTestResult(`汇总页面API测试成功，找到${customers.length}个客户`, true);
                } else {
                    throw new Error(`API响应错误: ${response.status}`);
                }
            } catch (error) {
                updateStatus('navigation-status', '❌ 失败', false);
                addTestResult(`汇总页面导航测试失败: ${error.message}`, false);
            }
        }

        async function testSummaryToggle() {
            try {
                updateStatus('toggle-status', '测试中...', true);
                
                // 模拟汇总表切换逻辑
                const testData = {
                    showSummaryTable: false,
                    summaryTableVersion: 'v1'
                };
                
                // 模拟切换
                testData.showSummaryTable = !testData.showSummaryTable;
                
                if (testData.showSummaryTable) {
                    updateStatus('toggle-status', '✅ 汇总表切换成功', true);
                    addTestResult('汇总表切换功能测试成功', true);
                } else {
                    throw new Error('切换状态异常');
                }
            } catch (error) {
                updateStatus('toggle-status', '❌ 失败', false);
                addTestResult(`汇总表切换测试失败: ${error.message}`, false);
            }
        }

        async function testConsolidatedTable() {
            try {
                updateStatus('consolidated-status', '测试中...', true);
                
                // 测试客户归纳汇总表API
                const response = await fetch('http://localhost:3000/api/summary/consolidated?agentId=1');
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('consolidated-status', '✅ 客户归纳汇总表API成功', true);
                    addTestResult(`客户归纳汇总表API测试成功，返回数据: ${JSON.stringify(data.metadata || {})}`, true);
                } else {
                    // API可能还没有实际数据，但能响应就算成功
                    updateStatus('consolidated-status', '✅ API响应正常', true);
                    addTestResult('客户归纳汇总表API响应正常（可能暂无数据）', true);
                }
            } catch (error) {
                updateStatus('consolidated-status', '❌ 失败', false);
                addTestResult(`客户归纳汇总表测试失败: ${error.message}`, false);
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            addTestResult('测试页面已加载，请按顺序点击测试按钮', true);
        };
    </script>
</body>
</html>
