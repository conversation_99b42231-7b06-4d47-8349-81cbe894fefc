# 财务管理系统 - 界面3布局设计 PRD v3.0

## 1. 文档信息
- **版本**: v3.0
- **创建日期**: 2025年7月30日
- **负责人**: Emma (产品经理)
- **更新原因**: 基于老板展示的界面3效果重新设计

## 2. 界面3核心需求分析

### 2.1 界面结构
基于老板展示的界面3，系统需要实现以下布局：

```
┌─────────────────────────────────────────────────────────────┐
│                    公式配置与数据展示界面                      │
├─────────────────────────────────────────────────────────────┤
│ 公式1.1  (data1+data2*data4)*data3  公式1.2  (data1+data2*data4) │
│ 公式2.1  (data1+data2*data4)*data3  公式2.2  (data1+data2*data4) │
│ 公式3.1  (data1+data2*data4)*data3  公式3.2  (data1+data2*data4) │
│ 公式4.1  (data1+data2*data4)*data3  公式4.2  (data1+data2*data4) │
│ 公式5.1  (data1+data2*data4)*data3  公式5.2  (data1+data2*data4) │
│ 公式6.1  data1                     公式6.2  data2*data4        │
│ 公式7.1  data1                     公式7.2  data2*data4        │
├─────────────────────────────────────────────────────────────┤
│                      [保存植入]                              │
├─────────────────────────────────────────────────────────────┤
│                    详细数据表格区域                           │
│ ┌─公式1.1详细数据─┐ ┌─公式1.2详细数据─┐ ┌─公式1.3详细数据─┐  │
│ │ 客户A │ 数据1 │ │ 客户A │ 数据1 │ │ 客户A │ 数据1 │  │
│ │ 客户B │ 数据2 │ │ 客户B │ 数据2 │ │ 客户B │ 数据2 │  │
│ └─────────────┘ └─────────────┘ └─────────────┘  │
│                                                   │
│ ┌─公式2.1详细数据─┐ ┌─公式2.2详细数据─┐              │
│ │ 客户A │ 数据1 │ │ 客户A │ 数据1 │              │
│ │ 客户B │ 数据2 │ │ 客户B │ 数据2 │              │
│ └─────────────┘ └─────────────┘              │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 颜色编码系统
- **黄色背景**: 公式名称和表达式
- **橙色背景**: 计算结果数据
- **蓝色背景**: 中间计算过程
- **绿色背景**: 最终汇总结果
- **白色背景**: 基础输入数据

### 2.3 数据层级结构
```
公式组 (如公式1)
├── 公式1.1 - 主计算公式
├── 公式1.2 - 辅助计算公式
└── 详细数据表格
    ├── 客户A数据
    ├── 客户B数据
    └── 计算结果
```

## 3. 功能规格详述

### 3.1 公式配置区域
**位置**: 界面顶部
**功能**: 
- 显示所有公式的名称和表达式
- 支持公式表达式的可视化编辑
- 实时显示公式计算逻辑

**交互方式**:
- 点击公式名称可编辑公式表达式
- 支持拖拽调整公式顺序
- 鼠标悬停显示公式详细说明

### 3.2 详细数据表格区域
**位置**: 界面中下部
**功能**:
- 按公式分组显示详细数据
- 每个公式对应一个数据表格
- 支持数据输入和计算结果显示

**表格结构**:
```
公式X.Y详细数据
┌─────────┬─────────┬─────────┬─────────┬─────────┐
│  客户   │  数据1  │  数据2  │  计算值  │  结果   │
├─────────┼─────────┼─────────┼─────────┼─────────┤
│ 客户A   │  1000   │  2.5    │  2500   │  7500   │
│ 客户B   │  1200   │  3.0    │  3600   │  10800  │
└─────────┴─────────┴─────────┴─────────┴─────────┘
```

### 3.3 保存植入功能
**位置**: 公式配置区域底部
**功能**:
- 保存所有公式配置
- 执行批量数据计算
- 更新主汇总表

## 4. 界面布局设计

### 4.1 响应式布局
- **桌面端**: 多列显示，充分利用屏幕宽度
- **平板端**: 2列显示，保持可读性
- **手机端**: 单列显示，支持滚动查看

### 4.2 组件层次结构
```
FormulaConfigurationPage
├── FormulaDisplayGrid (公式配置区域)
│   ├── FormulaCard (单个公式卡片)
│   │   ├── FormulaName (公式名称)
│   │   ├── FormulaExpression (公式表达式)
│   │   └── FormulaEditor (公式编辑器)
│   └── SaveButton (保存植入按钮)
├── DetailedDataSection (详细数据区域)
│   ├── FormulaDataTable (公式数据表格)
│   │   ├── DataInputRow (数据输入行)
│   │   ├── CalculationRow (计算结果行)
│   │   └── SummaryRow (汇总行)
│   └── ColorCodedCell (颜色编码单元格)
└── NavigationPanel (导航面板)
```

### 4.3 颜色主题配置
```javascript
const colorTheme = {
  formulaName: '#FFD700',      // 黄色 - 公式名称
  formulaExpression: '#FFF8DC', // 浅黄色 - 公式表达式
  inputData: '#FFFFFF',        // 白色 - 输入数据
  calculationData: '#87CEEB',  // 蓝色 - 计算数据
  resultData: '#FFA500',       // 橙色 - 结果数据
  summaryData: '#90EE90',      // 绿色 - 汇总数据
  borderColor: '#D3D3D3',      // 灰色 - 边框
  hoverColor: '#F0F8FF'        // 浅蓝色 - 悬停效果
};
```

## 5. 数据结构设计

### 5.1 公式配置数据结构
```javascript
const formulaConfig = {
  formulaGroups: [
    {
      groupId: 'formula1',
      groupName: '公式1',
      formulas: [
        {
          id: 'formula1_1',
          name: '公式1.1',
          expression: '(data1+data2*data4)*data3',
          description: '主计算公式',
          color: 'yellow'
        },
        {
          id: 'formula1_2', 
          name: '公式1.2',
          expression: '(data1+data2*data4)',
          description: '辅助计算公式',
          color: 'yellow'
        }
      ],
      detailedData: [
        {
          customerId: 'customerA',
          customerName: '客户A',
          data1: 1000,
          data2: 2.5,
          data3: 3.0,
          data4: 1.2,
          calculatedResults: {
            formula1_1: 7500,
            formula1_2: 2500
          }
        }
      ]
    }
  ]
};
```

### 5.2 界面状态管理
```javascript
const pageState = {
  currentView: 'formulaConfiguration', // 当前视图
  selectedFormula: null,               // 选中的公式
  editingFormula: null,                // 正在编辑的公式
  dataFilter: {                        // 数据过滤器
    customerFilter: '',
    formulaFilter: '',
    dateRange: null
  },
  calculationResults: {},              // 计算结果缓存
  isDirty: false                       // 是否有未保存的更改
};
```

## 6. 交互流程设计

### 6.1 公式配置流程
```
1. 用户进入界面3
2. 系统加载所有公式配置
3. 显示公式网格布局
4. 用户点击公式进行编辑
5. 实时预览公式计算结果
6. 用户点击保存植入
7. 系统执行批量计算
8. 更新详细数据表格
9. 显示保存成功提示
```

### 6.2 数据查看流程
```
1. 用户查看公式配置区域
2. 点击特定公式查看详细数据
3. 系统展开对应的数据表格
4. 用户可以编辑表格中的数据
5. 实时计算并更新结果
6. 颜色编码显示不同类型数据
```

## 7. 技术实现要点

### 7.1 组件设计原则
- **模块化**: 每个公式组独立组件
- **可复用**: 公式卡片组件可复用
- **响应式**: 支持不同屏幕尺寸
- **性能优化**: 虚拟滚动处理大量数据

### 7.2 状态管理策略
- **本地状态**: 组件内部交互状态
- **全局状态**: 公式配置和计算结果
- **持久化**: 自动保存用户配置
- **同步机制**: 实时同步计算结果

### 7.3 数据流设计
```
用户输入 → 公式计算 → 结果缓存 → 界面更新 → 数据持久化
    ↑                                              ↓
    ←─────────── 数据同步 ←─────────── 服务端API ←─────
```

## 8. 开发优先级

### 8.1 第一阶段 (核心功能)
- [ ] 公式配置网格布局
- [ ] 基础公式显示和编辑
- [ ] 详细数据表格组件
- [ ] 颜色编码系统

### 8.2 第二阶段 (交互优化)
- [ ] 公式拖拽排序
- [ ] 数据实时计算
- [ ] 保存植入功能
- [ ] 响应式布局适配

### 8.3 第三阶段 (功能完善)
- [ ] 数据导入导出
- [ ] 历史版本管理
- [ ] 批量操作功能
- [ ] 高级筛选和搜索

---

**Emma完成时间**: 2025年7月30日  
**下一步**: 提交给Bob进行技术架构设计  
**关键目标**: 完全匹配老板展示的界面3效果
