// 根布局 - JavaScript版本
// 避免TypeScript版本冲突

import { ConfigProvider, App } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import "./globals.css";

export const metadata = {
  title: "财务管理系统",
  description: "代理商盈亏跟踪器 - JavaScript版本",
};

export default function RootLayout({ children }) {
  return (
    <html lang="zh-CN">
      <body style={{ margin: 0, padding: 0, fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' }}>
        <ConfigProvider
          locale={zhCN}
          theme={{
            token: {
              colorPrimary: '#1890ff',
              borderRadius: 6,
            },
          }}
        >
          <App>
            {children}
          </App>
        </ConfigProvider>
      </body>
    </html>
  );
}
