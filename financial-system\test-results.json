{"config": {"configFile": "E:\\财务管理系统4\\financial-system\\playwright.config.js", "rootDir": "E:/财务管理系统4/financial-system", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", null], ["list", null], ["json", {"outputFile": "test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/财务管理系统4/financial-system/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "E:/财务管理系统4/financial-system", "testIgnore": [], "testMatch": ["playwright-test.js"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 1, "webServer": {"command": "npm run dev", "port": 3000, "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "playwright-test.js", "file": "playwright-test.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "财务管理系统修复验证", "file": "playwright-test.js", "line": 7, "column": 6, "specs": [{"title": "1. 系统基本功能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 21519, "errors": [], "stdout": [{"text": "🧪 测试1: 系统基本功能\n"}, {"text": "   ✅ 页面标题正常\n"}, {"text": "   ✅ 添加客户按钮存在\n"}, {"text": "   ✅ 数据表格存在\n"}, {"text": "   ✅ 添加客户成功，表格数量: 11\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-05T02:33:15.928Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9b8abd56f3360353b17-b86636c7d89ef3e4758a", "file": "playwright-test.js", "line": 18, "column": 3}, {"title": "2. 负数处理修复验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 27729, "errors": [], "stdout": [{"text": "🧪 测试2: 负数处理修复验证\n"}, {"text": "   📊 找到输入框: 77个\n"}, {"text": "   📝 输入负数测试数据: -100, -50, -25, -10\n"}, {"text": "   ✅ 输入框1: -100 输入成功\n"}, {"text": "   ✅ 输入框2: -50 输入成功\n"}, {"text": "   ✅ 输入框3: -25 输入成功\n"}, {"text": "   ✅ 输入框4: -10 输入成功\n"}, {"text": "   📊 计算结果数量: 25\n"}, {"text": "   ✅ 负数处理修复成功：计算结果包含负数\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-05T02:33:37.619Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9b8abd56f3360353b17-d8cd808f59506718124f", "file": "playwright-test.js", "line": 43, "column": 3}, {"title": "3. 股东公式映射修复验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 30228, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🧪 测试3: 股东公式映射修复验证\n"}, {"text": "   ✅ 添加第1个股东\n"}, {"text": "   ✅ 添加第2个股东\n"}, {"text": "   ✅ 添加第3个股东\n"}, {"text": "   📊 股东数量: 11\n"}, {"text": "   ✅ 股东1公式映射正确: 1.31&1.32\n"}, {"text": "   ✅ 股东2公式映射正确: 1.41&1.42\n"}, {"text": "   ✅ 股东3公式映射正确: 1.51&1.52\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-05T02:34:05.353Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium\\error-context.md"}]}, {"workerIndex": 1, "parallelIndex": 0, "status": "timedOut", "duration": 30570, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 20, "line": 150}, "message": "Error: page.waitForTimeout: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 148 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mlet\u001b[39m i \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m i \u001b[33m<\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mmin(\u001b[35m4\u001b[39m\u001b[33m,\u001b[39m table2Inputs\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m i\u001b[33m++\u001b[39m) {\n \u001b[90m 149 |\u001b[39m         \u001b[36mawait\u001b[39m table2Inputs[i]\u001b[33m.\u001b[39mfill(data2[i]\u001b[33m.\u001b[39mtoString())\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 150 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 151 |\u001b[39m       }\n \u001b[90m 152 |\u001b[39m       \n \u001b[90m 153 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'   📝 输入不同股东数据验证计算差异'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:150:20\u001b[22m"}], "stdout": [{"text": "🧪 测试3: 股东公式映射修复验证\n"}, {"text": "   ✅ 添加第1个股东\n"}, {"text": "   ✅ 添加第2个股东\n"}, {"text": "   ✅ 添加第3个股东\n"}, {"text": "   📊 股东数量: 11\n"}, {"text": "   ✅ 股东1公式映射正确: 1.31&1.32\n"}, {"text": "   ✅ 股东2公式映射正确: 1.41&1.42\n"}, {"text": "   ✅ 股东3公式映射正确: 1.51&1.52\n"}], "stderr": [], "retry": 1, "startTime": "2025-08-05T02:34:36.365Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry1\\trace.zip"}]}, {"workerIndex": 2, "parallelIndex": 0, "status": "timedOut", "duration": 30198, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}], "stdout": [{"text": "🧪 测试3: 股东公式映射修复验证\n"}, {"text": "   ✅ 添加第1个股东\n"}, {"text": "   ✅ 添加第2个股东\n"}, {"text": "   ✅ 添加第3个股东\n"}, {"text": "   📊 股东数量: 11\n"}, {"text": "   ✅ 股东1公式映射正确: 1.31&1.32\n"}, {"text": "   ✅ 股东2公式映射正确: 1.41&1.42\n"}, {"text": "   ✅ 股东3公式映射正确: 1.51&1.52\n"}], "stderr": [], "retry": 2, "startTime": "2025-08-05T02:35:08.330Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry2\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry2\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-3-股东公式映射修复验证-chromium-retry2\\error-context.md"}]}], "status": "unexpected"}], "id": "f9b8abd56f3360353b17-28404e6cd4870d03b1ec", "file": "playwright-test.js", "line": 103, "column": 3}, {"title": "4. 计算精度修复验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "passed", "duration": 26202, "errors": [], "stdout": [{"text": "🧪 测试4: 计算精度修复验证\n"}, {"text": "   📝 输入小数精度测试数据: 10.5, 3.33, 2.25, 1.67\n"}, {"text": "   ✅ 小数输入1: 10.5 输入成功\n"}, {"text": "   ✅ 小数输入2: 3.33 输入成功\n"}, {"text": "   ✅ 小数输入3: 2.25 输入成功\n"}, {"text": "   ✅ 小数输入4: 1.67 输入成功\n"}, {"text": "   ✅ 计算精度修复成功：结果包含小数\n"}, {"text": "   📈 小数结果示例: 54.92, 54.92, 53.92\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-05T02:35:39.492Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9b8abd56f3360353b17-f0ac9d398de006cd858e", "file": "playwright-test.js", "line": 178, "column": 3}, {"title": "5. 真实业务场景验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 26748, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at E:\\财务管理系统4\\financial-system\\playwright-test.js:274:32", "location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}, "snippet": "\u001b[0m \u001b[90m 272 |\u001b[39m     \u001b[90m// 验证包含负数（亏损）\u001b[39m\n \u001b[90m 273 |\u001b[39m     \u001b[36mconst\u001b[39m hasNegativeResults \u001b[33m=\u001b[39m results\u001b[33m.\u001b[39msome(r \u001b[33m=>\u001b[39m r \u001b[33m<\u001b[39m \u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 274 |\u001b[39m     expect(hasNegativeResults)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 275 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'   ✅ 亏损企业场景：正确显示负数亏损'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 276 |\u001b[39m     \n \u001b[90m 277 |\u001b[39m     \u001b[90m// 验证预期亏损金额\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 272 |\u001b[39m     \u001b[90m// 验证包含负数（亏损）\u001b[39m\n \u001b[90m 273 |\u001b[39m     \u001b[36mconst\u001b[39m hasNegativeResults \u001b[33m=\u001b[39m results\u001b[33m.\u001b[39msome(r \u001b[33m=>\u001b[39m r \u001b[33m<\u001b[39m \u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 274 |\u001b[39m     expect(hasNegativeResults)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 275 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'   ✅ 亏损企业场景：正确显示负数亏损'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 276 |\u001b[39m     \n \u001b[90m 277 |\u001b[39m     \u001b[90m// 验证预期亏损金额\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:274:32\u001b[22m"}], "stdout": [{"text": "🧪 测试5: 真实业务场景验证\n"}, {"text": "   💼 场景1: 亏损企业测试\n"}, {"text": "   📝 输入亏损企业数据: 收入30万, 成本40万, 费用10万, 税费减免1万\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-05T02:36:05.852Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}}, {"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 27667, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at E:\\财务管理系统4\\financial-system\\playwright-test.js:274:32", "location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}, "snippet": "\u001b[0m \u001b[90m 272 |\u001b[39m     \u001b[90m// 验证包含负数（亏损）\u001b[39m\n \u001b[90m 273 |\u001b[39m     \u001b[36mconst\u001b[39m hasNegativeResults \u001b[33m=\u001b[39m results\u001b[33m.\u001b[39msome(r \u001b[33m=>\u001b[39m r \u001b[33m<\u001b[39m \u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 274 |\u001b[39m     expect(hasNegativeResults)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 275 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'   ✅ 亏损企业场景：正确显示负数亏损'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 276 |\u001b[39m     \n \u001b[90m 277 |\u001b[39m     \u001b[90m// 验证预期亏损金额\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 272 |\u001b[39m     \u001b[90m// 验证包含负数（亏损）\u001b[39m\n \u001b[90m 273 |\u001b[39m     \u001b[36mconst\u001b[39m hasNegativeResults \u001b[33m=\u001b[39m results\u001b[33m.\u001b[39msome(r \u001b[33m=>\u001b[39m r \u001b[33m<\u001b[39m \u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 274 |\u001b[39m     expect(hasNegativeResults)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 275 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'   ✅ 亏损企业场景：正确显示负数亏损'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 276 |\u001b[39m     \n \u001b[90m 277 |\u001b[39m     \u001b[90m// 验证预期亏损金额\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:274:32\u001b[22m"}], "stdout": [{"text": "🧪 测试5: 真实业务场景验证\n"}, {"text": "   💼 场景1: 亏损企业测试\n"}, {"text": "   📝 输入亏损企业数据: 收入30万, 成本40万, 费用10万, 税费减免1万\n"}], "stderr": [], "retry": 1, "startTime": "2025-08-05T02:36:33.586Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}}, {"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 26276, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at E:\\财务管理系统4\\financial-system\\playwright-test.js:274:32", "location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}, "snippet": "\u001b[0m \u001b[90m 272 |\u001b[39m     \u001b[90m// 验证包含负数（亏损）\u001b[39m\n \u001b[90m 273 |\u001b[39m     \u001b[36mconst\u001b[39m hasNegativeResults \u001b[33m=\u001b[39m results\u001b[33m.\u001b[39msome(r \u001b[33m=>\u001b[39m r \u001b[33m<\u001b[39m \u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 274 |\u001b[39m     expect(hasNegativeResults)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 275 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'   ✅ 亏损企业场景：正确显示负数亏损'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 276 |\u001b[39m     \n \u001b[90m 277 |\u001b[39m     \u001b[90m// 验证预期亏损金额\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 272 |\u001b[39m     \u001b[90m// 验证包含负数（亏损）\u001b[39m\n \u001b[90m 273 |\u001b[39m     \u001b[36mconst\u001b[39m hasNegativeResults \u001b[33m=\u001b[39m results\u001b[33m.\u001b[39msome(r \u001b[33m=>\u001b[39m r \u001b[33m<\u001b[39m \u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 274 |\u001b[39m     expect(hasNegativeResults)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 275 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'   ✅ 亏损企业场景：正确显示负数亏损'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 276 |\u001b[39m     \n \u001b[90m 277 |\u001b[39m     \u001b[90m// 验证预期亏损金额\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:274:32\u001b[22m"}], "stdout": [{"text": "🧪 测试5: 真实业务场景验证\n"}, {"text": "   💼 场景1: 亏损企业测试\n"}, {"text": "   📝 输入亏损企业数据: 收入30万, 成本40万, 费用10万, 税费减免1万\n"}], "stderr": [], "retry": 2, "startTime": "2025-08-05T02:37:02.666Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry2\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry2\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-5-真实业务场景验证-chromium-retry2\\error-context.md"}], "errorLocation": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 32, "line": 274}}], "status": "unexpected"}], "id": "f9b8abd56f3360353b17-2e38441cbc83dc0f394d", "file": "playwright-test.js", "line": 235, "column": 3}, {"title": "6. 综合功能稳定性测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "timedOut", "duration": 30192, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 20, "line": 312}, "message": "Error: page.waitForTimeout: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 310 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mlet\u001b[39m i \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m i \u001b[33m<\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mmin(\u001b[35m4\u001b[39m\u001b[33m,\u001b[39m inputs\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m i\u001b[33m++\u001b[39m) {\n \u001b[90m 311 |\u001b[39m         \u001b[36mawait\u001b[39m inputs[i]\u001b[33m.\u001b[39mfill(testCase\u001b[33m.\u001b[39mdata[i]\u001b[33m.\u001b[39mtoString())\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 312 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 313 |\u001b[39m       }\n \u001b[90m 314 |\u001b[39m       \n \u001b[90m 315 |\u001b[39m       \u001b[90m// 触发计算\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:312:20\u001b[22m"}], "stdout": [{"text": "🧪 测试6: 综合功能稳定性测试\n"}, {"text": "   📝 测试正数数据: 1000, 500, 250, 100\n"}, {"text": "   ✅ 正数数据计算正常，结果数量: 25\n"}, {"text": "   📝 测试负数数据: -1000, -500, -250, -100\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-05T02:37:29.963Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium\\error-context.md"}]}, {"workerIndex": 7, "parallelIndex": 0, "status": "timedOut", "duration": 30514, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 20, "line": 312}, "message": "Error: page.waitForTimeout: Target page, context or browser has been closed\n\n\u001b[0m \u001b[90m 310 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mlet\u001b[39m i \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m i \u001b[33m<\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mmin(\u001b[35m4\u001b[39m\u001b[33m,\u001b[39m inputs\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m i\u001b[33m++\u001b[39m) {\n \u001b[90m 311 |\u001b[39m         \u001b[36mawait\u001b[39m inputs[i]\u001b[33m.\u001b[39mfill(testCase\u001b[33m.\u001b[39mdata[i]\u001b[33m.\u001b[39mtoString())\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 312 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 313 |\u001b[39m       }\n \u001b[90m 314 |\u001b[39m       \n \u001b[90m 315 |\u001b[39m       \u001b[90m// 触发计算\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:312:20\u001b[22m"}], "stdout": [{"text": "🧪 测试6: 综合功能稳定性测试\n"}, {"text": "   📝 测试正数数据: 1000, 500, 250, 100\n"}, {"text": "   ✅ 正数数据计算正常，结果数量: 25\n"}, {"text": "   📝 测试负数数据: -1000, -500, -250, -100\n"}], "stderr": [], "retry": 1, "startTime": "2025-08-05T02:38:01.153Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry1\\trace.zip"}]}, {"workerIndex": 8, "parallelIndex": 0, "status": "timedOut", "duration": 30173, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "E:\\财务管理系统4\\financial-system\\playwright-test.js", "column": 20, "line": 312}, "message": "Error: page.waitForTimeout: Test timeout of 30000ms exceeded.\n\n\u001b[0m \u001b[90m 310 |\u001b[39m       \u001b[36mfor\u001b[39m (\u001b[36mlet\u001b[39m i \u001b[33m=\u001b[39m \u001b[35m0\u001b[39m\u001b[33m;\u001b[39m i \u001b[33m<\u001b[39m \u001b[33mMath\u001b[39m\u001b[33m.\u001b[39mmin(\u001b[35m4\u001b[39m\u001b[33m,\u001b[39m inputs\u001b[33m.\u001b[39mlength)\u001b[33m;\u001b[39m i\u001b[33m++\u001b[39m) {\n \u001b[90m 311 |\u001b[39m         \u001b[36mawait\u001b[39m inputs[i]\u001b[33m.\u001b[39mfill(testCase\u001b[33m.\u001b[39mdata[i]\u001b[33m.\u001b[39mtoString())\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 312 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m200\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 313 |\u001b[39m       }\n \u001b[90m 314 |\u001b[39m       \n \u001b[90m 315 |\u001b[39m       \u001b[90m// 触发计算\u001b[39m\u001b[0m\n\u001b[2m    at E:\\财务管理系统4\\financial-system\\playwright-test.js:312:20\u001b[22m"}], "stdout": [{"text": "🧪 测试6: 综合功能稳定性测试\n"}, {"text": "   📝 测试正数数据: 1000, 500, 250, 100\n"}, {"text": "   ✅ 正数数据计算正常，结果数量: 25\n"}, {"text": "   📝 测试负数数据: -1000, -500, -250, -100\n"}], "stderr": [], "retry": 2, "startTime": "2025-08-05T02:38:33.052Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry2\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry2\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\财务管理系统4\\financial-system\\test-results\\playwright-test.js-财务管理系统修复验证-6-综合功能稳定性测试-chromium-retry2\\error-context.md"}]}], "status": "unexpected"}], "id": "f9b8abd56f3360353b17-1f541a2ba7c6c05fe12d", "file": "playwright-test.js", "line": 287, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-08-05T02:33:14.309Z", "duration": 349399.21499999997, "expected": 3, "skipped": 0, "unexpected": 3, "flaky": 0}}