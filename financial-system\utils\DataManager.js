// 数据管理器 - 处理公式和客户数据的持久化存储
import { FormulaEngine } from './FormulaEngine';

export class DataManager {
  // 存储键名
  static STORAGE_KEYS = {
    FORMULAS: 'dynamic_formulas',
    CUSTOMERS: 'dynamic_customers',
    SETTINGS: 'dynamic_settings'
  };

  // 保存公式配置
  static saveFormulas(formulas) {
    try {
      const formulaData = {
        formulas: formulas,
        timestamp: Date.now(),
        version: '4.0'
      };
      
      localStorage.setItem(
        this.STORAGE_KEYS.FORMULAS, 
        JSON.stringify(formulaData)
      );
      
      console.log('✅ 公式配置已保存', Object.keys(formulas).length, '个公式');
      return true;
    } catch (error) {
      console.error('❌ 保存公式配置失败:', error);
      return false;
    }
  }

  // 加载公式配置
  static loadFormulas() {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEYS.FORMULAS);
      
      if (saved) {
        const formulaData = JSON.parse(saved);
        console.log('✅ 已加载公式配置', Object.keys(formulaData.formulas || {}).length, '个公式');
        return formulaData.formulas || {};
      } else {
        // 返回示例公式
        const exampleFormulas = FormulaEngine.getExampleFormulas();
        console.log('📝 使用示例公式配置');
        return exampleFormulas;
      }
    } catch (error) {
      console.error('❌ 加载公式配置失败:', error);
      return FormulaEngine.getExampleFormulas();
    }
  }

  // 保存客户数据
  static saveCustomers(customers) {
    try {
      const customerData = {
        customers: customers,
        timestamp: Date.now(),
        version: '4.0'
      };
      
      localStorage.setItem(
        this.STORAGE_KEYS.CUSTOMERS, 
        JSON.stringify(customerData)
      );
      
      console.log('✅ 客户数据已保存', customers.length, '个客户');
      return true;
    } catch (error) {
      console.error('❌ 保存客户数据失败:', error);
      return false;
    }
  }

  // 加载客户数据
  static loadCustomers() {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEYS.CUSTOMERS);
      
      if (saved) {
        const customerData = JSON.parse(saved);
        console.log('✅ 已加载客户数据', customerData.customers?.length || 0, '个客户');
        return customerData.customers || [];
      } else {
        // 返回示例客户
        const exampleCustomers = this.getExampleCustomers();
        console.log('📝 使用示例客户数据');
        return exampleCustomers;
      }
    } catch (error) {
      console.error('❌ 加载客户数据失败:', error);
      return this.getExampleCustomers();
    }
  }

  // 获取示例客户数据
  static getExampleCustomers() {
    return [
      {
        id: 'customer_1',
        name: '客户A',
        data: {
          data1: 25656,
          data2: 3.22,
          data3: 1200,
          data4: 900,
          data5: 1100,
          data6: 5444,
          data7: 5.6
        }
      },
      {
        id: 'customer_2',
        name: '客户B',
        data: {
          data1: 15000,
          data2: 2.8,
          data3: 800,
          data4: 1200,
          data5: 950,
          data6: 3200,
          data7: 4.2
        }
      }
    ];
  }

  // 创建新客户
  static createNewCustomer(name) {
    return {
      id: `customer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: name || '新客户',
      data: {
        data1: 0,
        data2: 0,
        data3: 0,
        data4: 0,
        data5: 0,
        data6: 0,
        data7: 0
      },
      createdAt: new Date().toISOString()
    };
  }

  // 验证客户数据
  static validateCustomer(customer) {
    const errors = [];

    if (!customer.id) {
      errors.push('客户ID不能为空');
    }

    if (!customer.name || customer.name.trim() === '') {
      errors.push('客户名称不能为空');
    }

    if (!customer.data) {
      errors.push('客户数据不能为空');
    } else {
      const dataValidation = FormulaEngine.validateData(customer.data);
      if (!dataValidation.isValid) {
        errors.push(...dataValidation.errors);
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  // 导出数据
  static exportData() {
    try {
      const formulas = this.loadFormulas();
      const customers = this.loadCustomers();
      
      const exportData = {
        formulas: formulas,
        customers: customers,
        exportTime: new Date().toISOString(),
        version: '4.0'
      };

      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `formula_system_backup_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      
      console.log('✅ 数据导出成功');
      return true;
    } catch (error) {
      console.error('❌ 数据导出失败:', error);
      return false;
    }
  }

  // 导入数据
  static importData(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const importData = JSON.parse(e.target.result);
          
          // 验证数据格式
          if (!importData.formulas || !importData.customers) {
            throw new Error('导入文件格式不正确');
          }

          // 验证公式
          Object.values(importData.formulas).forEach(formula => {
            if (formula.expression) {
              const validation = FormulaEngine.validateExpression(formula.expression);
              formula.isValid = validation.isValid;
            }
          });

          // 验证客户数据
          importData.customers.forEach(customer => {
            const validation = this.validateCustomer(customer);
            if (!validation.isValid) {
              console.warn('客户数据验证失败:', customer.name, validation.errors);
            }
          });

          // 保存导入的数据
          this.saveFormulas(importData.formulas);
          this.saveCustomers(importData.customers);
          
          console.log('✅ 数据导入成功');
          resolve({
            success: true,
            formulas: Object.keys(importData.formulas).length,
            customers: importData.customers.length
          });
        } catch (error) {
          console.error('❌ 数据导入失败:', error);
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      
      reader.readAsText(file);
    });
  }

  // 清空所有数据
  static clearAllData() {
    try {
      localStorage.removeItem(this.STORAGE_KEYS.FORMULAS);
      localStorage.removeItem(this.STORAGE_KEYS.CUSTOMERS);
      localStorage.removeItem(this.STORAGE_KEYS.SETTINGS);
      
      console.log('✅ 所有数据已清空');
      return true;
    } catch (error) {
      console.error('❌ 清空数据失败:', error);
      return false;
    }
  }

  // 获取存储统计信息
  static getStorageStats() {
    try {
      const formulas = this.loadFormulas();
      const customers = this.loadCustomers();
      
      const formulaSize = JSON.stringify(formulas).length;
      const customerSize = JSON.stringify(customers).length;
      const totalSize = formulaSize + customerSize;
      
      return {
        formulas: {
          count: Object.keys(formulas).length,
          size: formulaSize,
          sizeFormatted: this.formatBytes(formulaSize)
        },
        customers: {
          count: customers.length,
          size: customerSize,
          sizeFormatted: this.formatBytes(customerSize)
        },
        total: {
          size: totalSize,
          sizeFormatted: this.formatBytes(totalSize)
        }
      };
    } catch (error) {
      console.error('❌ 获取存储统计失败:', error);
      return null;
    }
  }

  // 格式化字节大小
  static formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 备份数据到云端（预留接口）
  static async backupToCloud(data) {
    // 这里可以集成云存储服务
    console.log('🔄 云端备份功能待实现');
    return Promise.resolve(false);
  }

  // 从云端恢复数据（预留接口）
  static async restoreFromCloud() {
    // 这里可以集成云存储服务
    console.log('🔄 云端恢复功能待实现');
    return Promise.resolve(null);
  }
}

// 默认导出
export default DataManager;
