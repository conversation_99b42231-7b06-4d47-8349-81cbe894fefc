'use client';

// 7模块综合财务管理系统
// 集成所有7个模块在单一界面中

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { 
  Layout, 
  Button, 
  Space, 
  Card, 
  message,
  Divider,
  Row,
  Col,
  Statistic,
  Spin
} from 'antd';
import { 
  PlusOutlined, 
  SaveOutlined, 
  ReloadOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';

import { useCustomerStore } from '../../../store/customerStore';
import { FormulaEngine } from '../../../lib/calculations';
import ModuleSelector, { ModuleInfoCard } from '../../../components/ModuleSelector';
import DynamicTable from '../../../components/DynamicTable';
import DynamicFormulaConfig from '../../../components/DynamicFormulaConfig';
import { MODULE_CONFIGS, getModuleConfig, getAllModules } from '../../../config/moduleConfigs';

const { Header, Content } = Layout;

export default function SevenModulesFinancialSystem() {
  const params = useParams();
  const router = useRouter();
  const agentId = parseInt(params.id);

  // Store状态
  const { 
    customers, 
    loading, 
    fetchCustomers, 
    addCustomer, 
    updateCustomer, 
    deleteCustomer 
  } = useCustomerStore();

  // 组件状态
  const [currentModule, setCurrentModule] = useState('module1_1'); // 默认模块1.1
  const [editingState, setEditingState] = useState({}); // 编辑状态 {customerId: fieldName}
  const [formulas, setFormulas] = useState({}); // 当前模块的公式配置
  const [customerCounts, setCustomerCounts] = useState({}); // 各模块客户数量
  const [moduleCustomers, setModuleCustomers] = useState([]); // 当前模块的客户数据
  const [statistics, setStatistics] = useState({
    totalCustomers: 0,
    totalRevenue: 0,
    totalExpense: 0,
    netProfit: 0,
    profitRate: 0,
    weeklyAvgProfit: 0
  });

  // 初始化数据
  useEffect(() => {
    if (agentId) {
      loadAllModulesData();
    }
  }, [agentId]);

  // 模块切换时更新数据
  useEffect(() => {
    if (currentModule) {
      loadModuleData(currentModule);
      loadModuleFormulas(currentModule);
    }
  }, [currentModule, customers]);

  // 加载所有模块数据
  const loadAllModulesData = async () => {
    try {
      await fetchCustomers(agentId);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    }
  };

  // 加载指定模块的数据
  const loadModuleData = useCallback((moduleId) => {
    // 过滤出当前模块的客户数据
    const moduleData = customers.filter(customer => 
      customer.moduleId === moduleId || (!customer.moduleId && moduleId === 'module1_1')
    );
    
    setModuleCustomers(moduleData);
    
    // 计算各模块客户数量
    const counts = {};
    getAllModules().forEach(module => {
      counts[module.id] = customers.filter(customer => 
        customer.moduleId === module.id || (!customer.moduleId && module.id === 'module1_1')
      ).length;
    });
    setCustomerCounts(counts);
    
    // 计算统计数据
    calculateStatistics(moduleData);
  }, [customers]);

  // 加载模块公式配置
  const loadModuleFormulas = (moduleId) => {
    const moduleConfig = getModuleConfig(moduleId);
    if (moduleConfig) {
      // 从localStorage加载保存的公式，如果没有则使用默认公式
      const savedFormulas = localStorage.getItem(`formulas_${moduleId}`);
      const moduleFormulas = savedFormulas 
        ? JSON.parse(savedFormulas) 
        : moduleConfig.defaultFormulas;
      
      setFormulas(moduleFormulas);
    }
  };

  // 计算统计数据
  const calculateStatistics = (data) => {
    if (!data || data.length === 0) {
      setStatistics({
        totalCustomers: 0,
        totalRevenue: 0,
        totalExpense: 0,
        netProfit: 0,
        profitRate: 0,
        weeklyAvgProfit: 0
      });
      return;
    }

    const moduleConfig = getModuleConfig(currentModule);
    if (!moduleConfig) return;

    // 根据模块配置计算统计数据
    const totalCustomers = data.length;
    let totalRevenue = 0;
    let totalExpense = 0;

    data.forEach(customer => {
      // 根据模块类型计算收入和支出
      if (moduleConfig.fields.data1) {
        totalRevenue += parseFloat(customer.data1 || 0);
      }
      if (moduleConfig.fields.data2) {
        totalExpense += parseFloat(customer.data2 || 0);
      }
    });

    const netProfit = totalRevenue - totalExpense;
    const profitRate = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;
    const weeklyAvgProfit = totalCustomers > 0 ? netProfit / totalCustomers : 0;

    setStatistics({
      totalCustomers,
      totalRevenue,
      totalExpense,
      netProfit,
      profitRate,
      weeklyAvgProfit
    });
  };

  // 模块切换处理
  const handleModuleChange = (moduleId) => {
    console.log('切换到模块:', moduleId);
    setCurrentModule(moduleId);
    setEditingState({}); // 清除编辑状态
  };

  // 添加客户
  const handleAddCustomer = async () => {
    try {
      const moduleConfig = getModuleConfig(currentModule);
      if (!moduleConfig) {
        message.error('模块配置未找到');
        return;
      }

      const newCustomer = {
        name: '新客户',
        userName: '新用户',
        agentId: agentId,
        moduleId: currentModule, // 标记所属模块
        // 初始化所有数据字段为0
        data1: 0,
        data2: 0,
        data3: 0,
        data4: 0,
        data5: 0,
        data6: 0,
        data7: 0
      };

      await addCustomer(newCustomer);
      message.success('添加客户成功！');
      
      // 刷新当前模块数据
      await fetchCustomers(agentId);
    } catch (error) {
      console.error('添加客户失败:', error);
      message.error('添加客户失败');
    }
  };

  // 单元格点击处理
  const handleCellClick = (record, field) => {
    const moduleConfig = getModuleConfig(currentModule);
    if (!moduleConfig || !moduleConfig.fields[field]?.editable) {
      return; // 不可编辑字段不响应点击
    }

    console.log('单击编辑单元格:', { recordId: record.id, field });
    setEditingState({ [record.id]: field });
  };

  // 取消编辑
  const handleCellEditCancel = () => {
    setEditingState({});
  };

  // 数据变更处理
  const handleDataChange = async (id, field, value) => {
    console.log('数据变更:', { id, field, value });

    // ID验证 - 防止undefined ID导致API失败
    if (!id || id === undefined || id === null) {
      console.error('❌ 客户ID无效:', { id, field, value });
      message.error('客户ID无效，无法更新数据');
      return;
    }

    // 验证客户是否存在
    const customer = moduleCustomers.find(c => c.id === id);
    if (!customer) {
      console.error('❌ 客户不存在:', { id, field, value });
      message.error('客户不存在，无法更新数据');
      return;
    }

    try {
      // 准备更新数据
      const updateData = { [field]: value };

      // 如果是数据字段，需要重新计算结果字段
      if (field.startsWith('data')) {
        const calculatedResults = FormulaEngine.calculateCustomerResults(
          { ...customer, [field]: value }, 
          formulas
        );
        Object.assign(updateData, calculatedResults);
      }

      // 调用store更新
      await updateCustomer(id, updateData);
      console.log('✅ 客户数据更新成功:', updateData);
      
      // 强制刷新客户数据以确保页面显示同步
      await fetchCustomers(agentId);
      console.log('🔄 强制刷新客户数据完成');

      message.success('数据保存成功');
    } catch (error) {
      console.error('更新客户失败:', error);
      message.error('更新客户失败: ' + error.message);
    }
  };

  // 键盘事件处理
  const handleKeyDown = (e, record, field) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleCellEditCancel();
    } else if (e.key === 'Escape') {
      handleCellEditCancel();
    }
  };

  // 删除客户
  const handleDeleteCustomer = async (customerId) => {
    try {
      await deleteCustomer(customerId);
      message.success('删除客户成功');
      await fetchCustomers(agentId);
    } catch (error) {
      console.error('删除客户失败:', error);
      message.error('删除客户失败');
    }
  };

  // 公式变更处理
  const handleFormulaChange = (formulaKey, formulaValue) => {
    setFormulas(prev => ({
      ...prev,
      [formulaKey]: formulaValue
    }));
  };

  // 公式失焦保存
  const handleFormulaBlur = (formulaKey, formulaValue) => {
    // 保存到localStorage
    const updatedFormulas = { ...formulas, [formulaKey]: formulaValue };
    localStorage.setItem(`formulas_${currentModule}`, JSON.stringify(updatedFormulas));
    
    // 重新计算所有客户的结果
    recalculateAllCustomers(updatedFormulas);
  };

  // 重置公式
  const handleResetFormulas = (defaultFormulas) => {
    setFormulas(defaultFormulas);
    localStorage.setItem(`formulas_${currentModule}`, JSON.stringify(defaultFormulas));
    recalculateAllCustomers(defaultFormulas);
    message.success('公式已重置为默认值');
  };

  // 重新计算所有客户数据
  const recalculateAllCustomers = async (newFormulas) => {
    try {
      for (const customer of moduleCustomers) {
        const calculatedResults = FormulaEngine.calculateCustomerResults(customer, newFormulas);
        if (Object.keys(calculatedResults).length > 0) {
          await updateCustomer(customer.id, calculatedResults);
        }
      }
      await fetchCustomers(agentId);
      message.success('所有客户数据已重新计算');
    } catch (error) {
      console.error('重新计算失败:', error);
      message.error('重新计算失败');
    }
  };

  // 刷新数据
  const handleRefreshData = () => {
    loadAllModulesData();
    message.success('数据已刷新');
  };

  // 返回上级页面
  const handleGoBack = () => {
    router.push('/');
  };

  const currentModuleConfig = getModuleConfig(currentModule);

  return (
    <Layout style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      <Header style={{ 
        backgroundColor: '#fff', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={handleGoBack}
          >
            返回
          </Button>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            🏢 7模块综合财务管理系统
          </h1>
          <div style={{ fontSize: '14px', color: '#666' }}>
            Agent ID: {agentId}
          </div>
        </div>
        
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddCustomer}
            loading={loading}
          >
            添加客户行
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefreshData}
            loading={loading}
          >
            刷新数据
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '24px' }}>
        {/* 模块选择器 */}
        <ModuleSelector
          currentModule={currentModule}
          onModuleChange={handleModuleChange}
          customerCounts={customerCounts}
        />

        {/* 当前模块信息卡片 */}
        <ModuleInfoCard moduleId={currentModule} />

        {/* 公式配置区域 */}
        <DynamicFormulaConfig
          moduleId={currentModule}
          formulas={formulas}
          onFormulaChange={handleFormulaChange}
          onFormulaBlur={handleFormulaBlur}
          onResetFormulas={handleResetFormulas}
        />

        <Divider />

        {/* 统计数据 */}
        <Card 
          title={`📊 ${currentModuleConfig?.displayName} - 数据统计`}
          style={{ marginBottom: '24px' }}
        >
          <Row gutter={16}>
            <Col span={4}>
              <Statistic 
                title="客户总数" 
                value={statistics.totalCustomers} 
                suffix="个"
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总收入" 
                value={statistics.totalRevenue} 
                precision={2}
                suffix="元"
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总支出" 
                value={statistics.totalExpense} 
                precision={2}
                suffix="元"
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="净利润" 
                value={statistics.netProfit} 
                precision={2}
                suffix="元"
                valueStyle={{ color: statistics.netProfit >= 0 ? '#3f8600' : '#cf1322' }}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="利润率" 
                value={statistics.profitRate} 
                precision={2}
                suffix="%"
                valueStyle={{ color: statistics.profitRate >= 0 ? '#3f8600' : '#cf1322' }}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="平均利润" 
                value={statistics.weeklyAvgProfit} 
                precision={2}
                suffix="元"
              />
            </Col>
          </Row>
        </Card>

        {/* 动态数据表格 */}
        <DynamicTable
          moduleId={currentModule}
          customers={moduleCustomers}
          loading={loading}
          editingState={editingState}
          onCellClick={handleCellClick}
          onCellEditCancel={handleCellEditCancel}
          onDataChange={handleDataChange}
          onKeyDown={handleKeyDown}
          onDeleteCustomer={handleDeleteCustomer}
        />
      </Content>
    </Layout>
  );
}
