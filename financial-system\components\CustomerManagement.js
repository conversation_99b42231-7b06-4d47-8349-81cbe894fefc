'use client';

import React, { useState } from 'react';
import { Card, Button, Space, Modal, Input, Form, message, Popconfirm } from 'antd';
import { PlusOutlined, DeleteOutlined, UserOutlined } from '@ant-design/icons';
import { useFormula } from '../contexts/FormulaContext';

// 客户管理组件
const CustomerManagement = () => {
  const { customers, addCustomer, deleteCustomer } = useFormula();
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 处理添加客户
  const handleAddCustomer = () => {
    form.validateFields().then(values => {
      const newCustomer = addCustomer(values.customerName);
      if (newCustomer) {
        setIsAddModalVisible(false);
        form.resetFields();
      }
    });
  };

  // 处理删除客户
  const handleDeleteCustomer = (customerId) => {
    deleteCustomer(customerId);
  };

  return (
    <Card 
      title="客户管理" 
      className="customer-management"
      style={{ marginBottom: '24px' }}
      bodyStyle={{ padding: '16px' }}
    >
      {/* 客户管理操作区域 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <span style={{ fontWeight: 'bold', color: '#666' }}>
            当前客户数量: {customers.length}
          </span>
          
          {/* 客户列表预览 */}
          <Space wrap>
            {customers.slice(0, 3).map(customer => (
              <span 
                key={customer.id}
                style={{
                  background: '#f0f8ff',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  color: '#1890ff'
                }}
              >
                <UserOutlined style={{ marginRight: '4px' }} />
                {customer.name}
              </span>
            ))}
            {customers.length > 3 && (
              <span style={{ color: '#999', fontSize: '12px' }}>
                +{customers.length - 3} 更多...
              </span>
            )}
          </Space>
        </div>

        {/* 操作按钮 */}
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setIsAddModalVisible(true)}
            style={{
              backgroundColor: '#52c41a',
              borderColor: '#52c41a'
            }}
          >
            添加客户
          </Button>
        </Space>
      </div>

      {/* 客户详细列表 */}
      {customers.length > 0 && (
        <div style={{ 
          background: '#fafafa', 
          padding: '12px', 
          borderRadius: '6px',
          border: '1px solid #f0f0f0'
        }}>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', 
            gap: '8px' 
          }}>
            {customers.map(customer => (
              <div 
                key={customer.id}
                style={{
                  background: '#fff',
                  padding: '8px 12px',
                  borderRadius: '4px',
                  border: '1px solid #e8e8e8',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <div>
                  <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                    <UserOutlined style={{ marginRight: '6px', color: '#1890ff' }} />
                    {customer.name}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                    数据完整性: {Object.values(customer.data).filter(v => v !== 0).length}/7
                  </div>
                </div>
                
                <Popconfirm
                  title="确认删除"
                  description={`确定要删除客户 "${customer.name}" 吗？`}
                  onConfirm={() => handleDeleteCustomer(customer.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button 
                    type="text" 
                    size="small"
                    icon={<DeleteOutlined />}
                    style={{ color: '#ff4d4f' }}
                  />
                </Popconfirm>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 空状态 */}
      {customers.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '40px 20px',
          color: '#999',
          background: '#fafafa',
          borderRadius: '6px',
          border: '2px dashed #d9d9d9'
        }}>
          <UserOutlined style={{ fontSize: '32px', marginBottom: '16px' }} />
          <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无客户数据</div>
          <div style={{ fontSize: '14px' }}>点击"添加客户"按钮开始添加客户信息</div>
        </div>
      )}

      {/* 添加客户弹窗 */}
      <Modal
        title="添加新客户"
        open={isAddModalVisible}
        onOk={handleAddCustomer}
        onCancel={() => {
          setIsAddModalVisible(false);
          form.resetFields();
        }}
        okText="添加"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            label="客户名称"
            name="customerName"
            rules={[
              { required: true, message: '请输入客户名称' },
              { min: 2, message: '客户名称至少2个字符' },
              { max: 20, message: '客户名称最多20个字符' }
            ]}
          >
            <Input 
              placeholder="请输入客户名称" 
              prefix={<UserOutlined />}
            />
          </Form.Item>
          
          <div style={{ 
            background: '#f6ffed', 
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
            padding: '12px',
            marginTop: '16px'
          }}>
            <div style={{ fontSize: '12px', color: '#52c41a', fontWeight: 'bold' }}>
              提示：
            </div>
            <ul style={{ 
              margin: '8px 0 0 0', 
              paddingLeft: '16px', 
              fontSize: '12px', 
              color: '#666' 
            }}>
              <li>新客户的所有数据字段将初始化为0</li>
              <li>添加后可以在详细数据表格中编辑客户数据</li>
              <li>系统将自动为新客户计算所有公式结果</li>
            </ul>
          </div>
        </Form>
      </Modal>
    </Card>
  );
};

export default CustomerManagement;
