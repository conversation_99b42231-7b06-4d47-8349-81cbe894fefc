// 公式计算引擎 - 纯JavaScript实现
// 避免TypeScript版本冲突

/**
 * 简化的公式计算引擎 - 纯JavaScript实现
 */
export class FormulaEngine {
  /**
   * 安全的公式解析器
   * @param {string} expression - 公式表达式
   * @param {Object} data - 数据对象
   * @returns {number} 计算结果
   */
  static parseFormula(expression, data) {
    if (!expression || typeof expression !== 'string') {
      return 0;
    }

    // 替换变量名为实际值
    let formula = expression;
    Object.entries(data).forEach(([key, value]) => {
      const numValue = parseFloat(value) || 0;
      formula = formula.replace(new RegExp(key, 'g'), numValue.toString());
    });
    
    // 安全执行数学表达式
    try {
      // 只允许基本数学运算和数字
      const safeFormula = formula.replace(/[^0-9+\-*/.() ]/g, '');
      
      // 检查是否为空或无效
      if (!safeFormula || safeFormula.trim() === '') {
        return 0;
      }
      
      // 使用Function构造器安全执行
      const result = Function('"use strict"; return (' + safeFormula + ')')();
      
      // 检查结果是否为有效数字
      return isNaN(result) ? 0 : result;
    } catch (error) {
      console.error('公式计算错误:', error);
      return 0;
    }
  }
  
  /**
   * 批量计算客户数据
   * @param {Object} customer - 客户数据
   * @returns {Object} 计算结果
   */
  static calculateCustomerData(customer) {
    const results = {};

    try {
      // 使用全局公式配置进行计算
      const data1 = customer.data1 || 0;
      const data2 = customer.data2 || 0;
      const data3 = customer.data3 || 0;
      const data4 = customer.data4 || 0;
      const data5 = customer.data5 || 0;

      // 公式1.1: data1 * data3 / 100 -> 存储到data6
      // 确保计算精度和逻辑正确
      if (data1 !== 0 && data3 !== 0) {
        results.data6 = Number((data1 * data3 / 100).toFixed(8));
      } else {
        results.data6 = 0;
      }

      // 公式1.2: data2 * 0.1 -> 存储到data7
      results.data7 = Number((data2 * 0.1).toFixed(8));

      // 公式1.5: data1 * data5 / 100 -> 存储到data8
      if (data1 !== 0 && data5 !== 0) {
        results.data8 = Number((data1 * data5 / 100).toFixed(8));
      } else {
        results.data8 = 0;
      }

      // 其他公式结果（可以扩展）
      results.formula1_3 = Number(((data1 + data2) * data3).toFixed(8)); // 公式1.3
      results.formula1_4 = Number((data4 - data5).toFixed(8)); // 公式1.4

      // 新增计算结果字段
      results.result1 = results.data6; // 公式1.1结果
      results.result2 = results.data7; // 公式1.2结果
      results.result3 = results.formula1_3; // 公式1.3结果
      results.result4 = results.formula1_4; // 公式1.4结果
      results.result5 = results.data8; // 公式1.5结果

    } catch (error) {
      console.error('客户数据计算错误:', error);
    }

    return results;
  }
  
  /**
   * 验证公式表达式
   * @param {string} expression - 公式表达式
   * @returns {Object} 验证结果
   */
  static validateFormula(expression) {
    if (!expression || typeof expression !== 'string') {
      return { valid: false, error: '公式不能为空' };
    }
    
    // 检查危险函数调用
    const dangerousPatterns = [
      /eval\s*\(/i,
      /Function\s*\(/i,
      /setTimeout\s*\(/i,
      /setInterval\s*\(/i,
      /require\s*\(/i,
      /import\s*\(/i,
      /console\s*\./i,
      /process\s*\./i,
      /global\s*\./i,
      /window\s*\./i,
    ];
    
    for (const pattern of dangerousPatterns) {
      if (pattern.test(expression)) {
        return { valid: false, error: '公式包含不安全的函数调用' };
      }
    }
    
    // 检查基本语法
    const allowedChars = /^[0-9+\-*/.() a-zA-Z_]+$/;
    if (!allowedChars.test(expression)) {
      return { valid: false, error: '公式包含不允许的字符' };
    }
    
    return { valid: true };
  }
  
  /**
   * 数据格式化工具 - 支持多模块格式
   */
  static formatData = {
    // 格式化千万整数
    formatData1: (value) => {
      if (value === null || value === undefined || isNaN(value)) return '';
      return Math.round(value).toLocaleString();
    },

    // 格式化十亿整数
    formatData2: (value) => {
      if (value === null || value === undefined || isNaN(value)) return '';
      return Math.round(value).toLocaleString();
    },

    // 格式化百分比 - 统一为小数格式（方案A）
    formatData3: (value) => {
      if (value === null || value === undefined || isNaN(value)) return '';
      // 统一显示为小数格式，如0.12代表12%
      return parseFloat(value).toFixed(4);
    },

    // 格式化高精度数字
    formatData4: (value) => {
      if (value === null || value === undefined || isNaN(value)) return '';
      return parseFloat(value).toFixed(8);
    },

    // 通用格式化方法 - 根据字段类型格式化
    formatByType: (value, format) => {
      if (value === null || value === undefined || isNaN(value)) return '';

      switch (format) {
        case 'integer':
          return Math.round(value).toLocaleString();
        case 'decimal':
          return parseFloat(value).toFixed(4);
        case 'precision':
          return parseFloat(value).toFixed(8);
        case 'percentage':
          return (parseFloat(value) * 100).toFixed(2) + '%';
        case 'result':
          return parseFloat(value).toLocaleString(undefined, {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          });
        default:
          return parseFloat(value).toFixed(2);
      }
    },
    
    // 格式化百分比
    formatData5: (value) => {
      if (value === null || value === undefined || isNaN(value)) return '';
      return parseFloat(value).toFixed(2) + '%';
    },
    
    // 格式化计算结果
    formatResult: (value) => {
      if (value === null || value === undefined || isNaN(value)) return '';
      return Math.round(value).toLocaleString();
    }
  };
  
  /**
   * 计算汇总数据
   * @param {Array} customers - 客户数据数组
   * @returns {Object} 汇总结果
   */
  static calculateSummary(customers) {
    const summary = {
      totalData1: 0,
      totalData2: 0,
      totalData3: 0,
      totalData4: 0,
      totalData5: 0,
      totalData6: 0,
      totalData7: 0,
      totalData8: 0,
      count: customers.length
    };
    
    customers.forEach(customer => {
      summary.totalData1 += customer.data1 || 0;
      summary.totalData2 += customer.data2 || 0;
      summary.totalData3 += customer.data3 || 0;
      summary.totalData4 += customer.data4 || 0;
      summary.totalData5 += customer.data5 || 0;
      summary.totalData6 += customer.data6 || 0;
      summary.totalData7 += customer.data7 || 0;
      summary.totalData8 += customer.data8 || 0;
    });
    
    return summary;
  }
}

/**
 * 全局公式管理器
 */
export class GlobalFormulaManager {
  static defaultFormulas = {
    formula1_1: 'data1 * data3 / 100',
    formula1_2: 'data2 * 0.1',
    formula1_3: '(data1 + data2) * data3',
    formula1_4: 'data4 - data5',
    formula1_5: 'data1 * data5 / 100'
  };
  
  /**
   * 获取默认公式
   * @param {string} formulaName - 公式名称
   * @returns {string} 公式表达式
   */
  static getDefaultFormula(formulaName) {
    return this.defaultFormulas[formulaName] || '';
  }
  
  /**
   * 应用全局公式到客户数据
   * @param {Object} customer - 客户数据
   * @param {Object} formulas - 公式配置
   * @returns {Object} 计算结果
   */
  static applyFormulas(customer, formulas = {}) {
    const results = {};
    const data = {
      data1: customer.data1 || 0,
      data2: customer.data2 || 0,
      data3: customer.data3 || 0,
      data4: customer.data4 || 0,
      data5: customer.data5 || 0
    };
    
    // 应用每个公式
    Object.entries(formulas).forEach(([key, expression]) => {
      if (expression) {
        const validation = FormulaEngine.validateFormula(expression);
        if (validation.valid) {
          results[key] = FormulaEngine.parseFormula(expression, data);
        } else {
          console.warn(`公式 ${key} 验证失败:`, validation.error);
          results[key] = 0;
        }
      }
    });
    
    return results;
  }
}
