'use client';

// 模块选择器组件
// 用于在7个模块之间切换

import { useState } from 'react';
import { Tabs, Badge, Tooltip } from 'antd';
import { MODULE_CONFIGS, getAllModules } from '../config/moduleConfigs';

export default function ModuleSelector({ 
  currentModule, 
  onModuleChange, 
  customerCounts = {} 
}) {
  const modules = getAllModules();

  const handleTabChange = (moduleId) => {
    if (moduleId !== currentModule) {
      onModuleChange(moduleId);
    }
  };

  return (
    <div className="module-selector" style={{ 
      marginBottom: '24px',
      padding: '16px',
      backgroundColor: '#fafafa',
      borderRadius: '8px',
      border: '1px solid #d9d9d9'
    }}>
      <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '16px' }}>
        📊 模块选择器
      </div>
      
      <Tabs
        activeKey={currentModule}
        onChange={handleTabChange}
        type="card"
        size="large"
        tabBarStyle={{
          marginBottom: 0
        }}
        items={modules.map(module => ({
          key: module.id,
          label: (
              <Tooltip 
                title={`${module.description} | 输入字段: ${module.inputFields}个 | 结果字段: ${module.resultFields}个`}
                placement="top"
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span>{module.displayName}</span>
                  <Badge 
                    count={customerCounts[module.id] || 0} 
                    style={{ 
                      backgroundColor: currentModule === module.id ? '#1890ff' : '#52c41a',
                      fontSize: '12px'
                    }}
                  />
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#666',
                    marginLeft: '4px'
                  }}>
                    ({module.inputFields}+{module.resultFields})
                  </div>
                </div>
              </Tooltip>
          ),
          children: null // Tab内容为空，实际内容在父组件中渲染
        }))}
      />
      
      <div style={{ 
        marginTop: '12px', 
        fontSize: '14px', 
        color: '#666',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          当前模块: <strong>{MODULE_CONFIGS[currentModule]?.displayName}</strong>
        </div>
        <div>
          字段配置: {MODULE_CONFIGS[currentModule]?.inputFields}个输入 + {MODULE_CONFIGS[currentModule]?.resultFields}个结果
        </div>
        <div>
          客户数量: <Badge count={customerCounts[currentModule] || 0} style={{ backgroundColor: '#52c41a' }} />
        </div>
      </div>
    </div>
  );
}

// 模块信息卡片组件
export function ModuleInfoCard({ moduleId }) {
  const config = MODULE_CONFIGS[moduleId];
  
  if (!config) return null;

  return (
    <div style={{
      padding: '16px',
      backgroundColor: '#f0f8ff',
      borderRadius: '8px',
      border: '1px solid #1890ff',
      marginBottom: '16px'
    }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '8px'
      }}>
        <h3 style={{ margin: 0, color: '#1890ff' }}>
          {config.displayName}
        </h3>
        <div style={{ fontSize: '14px', color: '#666' }}>
          {config.description}
        </div>
      </div>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(3, 1fr)', 
        gap: '12px',
        fontSize: '14px'
      }}>
        <div>
          <strong>输入字段:</strong> {config.inputFields}个
        </div>
        <div>
          <strong>结果字段:</strong> {config.resultFields}个
        </div>
        <div>
          <strong>总字段:</strong> {config.totalFields}个
        </div>
      </div>
      
      <div style={{ 
        marginTop: '12px',
        fontSize: '13px',
        color: '#666'
      }}>
        <strong>公式数量:</strong> {Object.keys(config.defaultFormulas).length}个 | 
        <strong> 结果映射:</strong> {Object.keys(config.resultMapping).length}个
      </div>
    </div>
  );
}
