'use client';

import { useState, useEffect } from 'react';
import { Layout, Card, Row, Col, Button, Typography, Space, message } from 'antd';
import { useRouter } from 'next/navigation';

const { Content } = Layout;
const { Title, Text } = Typography;

export default function ModulesPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
  }, [router]);

  // 7个数据模块配置
  const modules = [
    {
      id: 1,
      title: '模块1.1 - 基础财务数据',
      description: '7个数据输入字段 + 5个计算结果字段',
      color: '#1890ff',
      fields: '总收入、总支出、利润率、精确金额、增长率、投资额、税率',
      results: '净利润、投资回报率、税后利润、利润增长额、综合评分'
    },
    {
      id: 2,
      title: '模块2.1 - 成本分析',
      description: '7个数据输入字段 + 5个计算结果字段',
      color: '#52c41a',
      fields: '直接成本、间接成本、成本占比、单位成本、成本增长率、固定成本、变动成本',
      results: '总成本、成本效率、成本节约额、单位利润、成本控制指数'
    },
    {
      id: 3,
      title: '模块3.1 - 简化报表',
      description: '4个数据输入字段 + 1个计算结果字段',
      color: '#faad14',
      fields: '营业收入、营业成本、毛利率、关键指标',
      results: '综合评估分数'
    },
    {
      id: 4,
      title: '模块4.1 - 投资回报',
      description: '7个数据输入字段 + 5个计算结果字段',
      color: '#f5222d',
      fields: '初始投资、累计投资、预期收益率、实际收益、风险系数、回收金额、投资周期',
      results: 'ROI投资回报率、NPV净现值、IRR内部收益率、回收期、风险调整收益'
    },
    {
      id: 5,
      title: '模块5.1 - 风险评估',
      description: '7个数据输入字段 + 5个计算结果字段',
      color: '#722ed1',
      fields: '风险敞口、保险金额、风险概率、损失预期、风险容忍度、风险准备金、风险缓解率',
      results: '风险价值VaR、风险调整资本、预期损失、风险收益比、综合风险评级'
    },
    {
      id: 6,
      title: '模块6.1 - 现金流',
      description: '7个数据输入字段 + 4个计算结果字段',
      color: '#13c2c2',
      fields: '经营现金流、投资现金流、筹资现金流、现金周转率、现金比率、期初现金、最低现金需求',
      results: '净现金流、现金流缺口、现金周转天数、现金流健康度'
    },
    {
      id: 7,
      title: '模块7.1 - 预算对比',
      description: '7个数据输入字段 + 4个计算结果字段',
      color: '#eb2f96',
      fields: '预算收入、实际收入、预算支出、实际支出、预算执行率、差异分析、调整幅度',
      results: '收入差异、支出差异、预算偏差率、执行效率指数'
    }
  ];

  const handleModuleClick = (moduleId) => {
    setLoading(true);
    // 跳转到具体模块页面
    router.push(`/modules/${moduleId}`);
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        {/* 页面标题 */}
        <div style={{ marginBottom: '24px' }}>
          <Space>
            <Button onClick={handleBackToDashboard}>返回主页</Button>
            <Title level={2} style={{ margin: 0 }}>
              财务数据模块管理
            </Title>
          </Space>
          <Text type="secondary" style={{ display: 'block', marginTop: '8px' }}>
            选择要管理的数据模块，每个模块都支持独立的数据输入和计算功能
          </Text>
        </div>

        {/* 模块卡片网格 */}
        <Row gutter={[24, 24]}>
          {modules.map((module) => (
            <Col xs={24} sm={12} lg={8} key={module.id}>
              <Card
                hoverable
                style={{ 
                  height: '100%',
                  borderLeft: `4px solid ${module.color}`,
                  transition: 'all 0.3s ease'
                }}
                styles={{ body: { padding: '24px' } }}
                onClick={() => handleModuleClick(module.id)}
              >
                <div style={{
                  textAlign: 'center',
                  marginBottom: '16px',
                  width: '40px',
                  height: '40px',
                  borderRadius: '50%',
                  backgroundColor: module.color,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto',
                  color: 'white',
                  fontSize: '18px',
                  fontWeight: 'bold'
                }}>
                  {module.id}
                </div>
                
                <Title level={4} style={{ textAlign: 'center', marginBottom: '8px' }}>
                  {module.title}
                </Title>
                
                <Text type="secondary" style={{ 
                  display: 'block', 
                  textAlign: 'center', 
                  marginBottom: '16px',
                  fontSize: '14px'
                }}>
                  {module.description}
                </Text>
                
                <div style={{ marginBottom: '12px' }}>
                  <Text strong style={{ color: module.color }}>输入字段：</Text>
                  <Text style={{ fontSize: '12px', display: 'block', marginTop: '4px' }}>
                    {module.fields}
                  </Text>
                </div>
                
                <div>
                  <Text strong style={{ color: module.color }}>计算结果：</Text>
                  <Text style={{ fontSize: '12px', display: 'block', marginTop: '4px' }}>
                    {module.results}
                  </Text>
                </div>
                
                <div style={{ textAlign: 'center', marginTop: '16px' }}>
                  <Button 
                    type="primary" 
                    style={{ backgroundColor: module.color, borderColor: module.color }}
                    loading={loading}
                  >
                    进入模块
                  </Button>
                </div>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 汇总明细表入口 */}
        <Card 
          title="7组数据汇总明细表" 
          style={{ marginTop: '24px' }}
          extra={
            <Button 
              type="primary" 
              onClick={() => router.push('/modules/summary')}
            >
              查看汇总明细
            </Button>
          }
        >
          <Text>
            查看所有7个模块的数据汇总、对比分析和趋势图表。支持跨模块数据关联分析和导出功能。
          </Text>
        </Card>
      </Content>
    </Layout>
  );
}
