'use client';

import React from 'react';
import { Table, Input, InputNumber, Button, Typography } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { Text } = Typography;

const ShareholderTable = ({ 
  shareholders = [], 
  customerData = {},
  formulaExpressions = {},
  onAddShareholder,
  onUpdateShareholder,
  onDeleteShareholder
}) => {
  
  // 计算公式结果的函数
  const calculateFormulaResult = (expression, data) => {
    if (!expression || !data) return 0;
    
    try {
      // 简单的表达式计算器
      let result = expression;
      
      // 替换变量，确保负数正确处理
      const dataValues = {
        data1: data.data1 || 0,
        data2: data.data2 || 0,
        data3: data.data3 || 0,
        data4: data.data4 || 0,
        data5: data.data5 || 0,
        data6: data.data6 || 0,
        data7: data.data7 || 0
      };

      // 按变量名长度排序，避免替换冲突
      const sortedKeys = Object.keys(dataValues).sort((a, b) => b.length - a.length);

      for (const key of sortedKeys) {
        const value = dataValues[key];
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        // 用括号包围数值，确保负数正确处理
        result = result.replace(regex, `(${value})`);
      }
      
      // 处理幂运算
      result = result.replace(/(\d+)\^(\d+)/g, (match, base, exp) => {
        return Math.pow(parseFloat(base), parseFloat(exp));
      });
      
      // 计算结果，保留两位小数精度
      const calculatedResult = eval(result);
      return isNaN(calculatedResult) ? 0 : Math.round(calculatedResult * 100) / 100;
    } catch (error) {
      console.error('公式计算错误:', error);
      return 0;
    }
  };

  // 根据股东序号获取对应的公式
  const getShareholderFormulas = (shareholderIndex) => {
    if (shareholderIndex === 0) {
      return {
        formula1: '公式1.31',
        formula2: '公式1.32'
      };
    } else if (shareholderIndex === 1) {
      return {
        formula1: '公式1.41',
        formula2: '公式1.42'
      };
    } else {
      return {
        formula1: '公式1.51',
        formula2: '公式1.52'
      };
    }
  };

  // 计算股东结果
  const calculateShareholderResults = (shareholderIndex) => {
    const formulas = getShareholderFormulas(shareholderIndex);
    const expression1 = formulaExpressions[formulas.formula1] || '';
    const expression2 = formulaExpressions[formulas.formula2] || '';
    
    return {
      result1: calculateFormulaResult(expression1, customerData),
      result2: calculateFormulaResult(expression2, customerData)
    };
  };

  // 表格列配置
  const columns = [
    {
      title: '股东姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (text, record, index) => {
        // 第一行：三个单元格合并为一个，显示股东姓名输入框
        if (index % 2 === 0) {
          return {
            children: (
              <Input
                size="small"
                placeholder="输入股东姓名"
                value={text || ''}
                onChange={(e) => {
                  onUpdateShareholder(record.shareholderKey, 'name', e.target.value);
                }}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.target.blur(); // 失去焦点，触发保存
                  }
                }}
                style={{ width: '100%' }}
              />
            ),
            props: {
              colSpan: 3, // 合并三列
            },
          };
        }
        return {
          children: null,
          props: {
            colSpan: 0,
          },
        };
      }
    },
    {
      title: '比例/结果1',
      dataIndex: 'ratio',
      key: 'ratio',
      width: 100,
      render: (text, record, index) => {
        const shareholderIndex = Math.floor(index / 2);
        const results = calculateShareholderResults(shareholderIndex);
        
        if (index % 2 === 0) {
          // 第一行被合并，不显示
          return {
            children: null,
            props: {
              colSpan: 0,
            },
          };
        } else {
          // 第二行：比例输入框
          return (
            <InputNumber
              size="small"
              step={0.01}
              min={0}
              max={1}
              precision={2}
              placeholder="0.1"
              value={text}
              onChange={(value) => {
                const finalValue = Math.min(Math.max(value || 0, 0), 1);
                onUpdateShareholder(record.shareholderKey, 'ratio', finalValue);
              }}
              onPressEnter={(e) => {
                e.target.blur();
              }}
              style={{ width: '100%', textAlign: 'right' }}
            />
          );
        }
      }
    },
    {
      title: '结果1',
      key: 'result1',
      width: 80,
      render: (text, record, index) => {
        const shareholderIndex = Math.floor(index / 2);
        const results = calculateShareholderResults(shareholderIndex);
        
        if (index % 2 === 0) {
          // 第一行被合并，不显示
          return {
            children: null,
            props: {
              colSpan: 0,
            },
          };
        } else {
          // 第二行：显示公式计算结果
          return (
            <Text strong style={{ color: '#1890ff' }}>
              {results.result1}
            </Text>
          );
        }
      }
    },
    {
      title: '结果2',
      key: 'result2',
      width: 80,
      render: (text, record, index) => {
        const shareholderIndex = Math.floor(index / 2);
        const results = calculateShareholderResults(shareholderIndex);
        
        if (index % 2 === 0) {
          // 第一行被合并，不显示
          return {
            children: null,
            props: {
              colSpan: 0,
            },
          };
        } else {
          // 第二行：显示公式计算结果
          return (
            <Text strong style={{ color: '#52c41a' }}>
              {results.result2}
            </Text>
          );
        }
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (text, record, index) => {
        if (index % 2 === 0) {
          // 第一行：删除按钮
          return (
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => onDeleteShareholder(record.shareholderKey)}
            >
              删除
            </Button>
          );
        } else {
          // 第二行：空白
          return null;
        }
      }
    }
  ];

  // 准备表格数据 - 每个股东需要两行数据
  const tableData = [];
  shareholders.forEach((shareholder, index) => {
    // 第一行：股东姓名（合并单元格）
    tableData.push({
      key: `${shareholder.key}_name`,
      name: shareholder.name,
      ratio: shareholder.ratio,
      shareholderKey: shareholder.key,
      rowType: 'name'
    });
    
    // 第二行：比例和结果
    tableData.push({
      key: `${shareholder.key}_data`,
      name: '',
      ratio: shareholder.ratio,
      shareholderKey: shareholder.key,
      rowType: 'data'
    });
  });

  return (
    <div>
      <div style={{ marginBottom: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Text strong>股东信息</Text>
        <Button
          size="small"
          type="dashed"
          icon={<PlusOutlined />}
          onClick={onAddShareholder}
        >
          添加股东
        </Button>
      </div>
      
      {tableData.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '40px 20px',
          background: '#fafafa',
          border: '1px dashed #d9d9d9',
          borderRadius: '6px',
          marginBottom: '16px'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>👥</div>
          <Text type="secondary">暂无股东信息</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            点击上方"添加股东"按钮开始添加
          </Text>
        </div>
      ) : (
        <Table
          dataSource={tableData}
          columns={columns}
          pagination={false}
          size="small"
          bordered
          showHeader={false}
          style={{ marginBottom: '16px' }}
        />
      )}
      
      {/* 公式说明 */}
      <div style={{ 
        fontSize: '12px', 
        color: '#666', 
        background: '#f9f9f9', 
        padding: '8px', 
        borderRadius: '4px' 
      }}>
        <Text strong>公式映射说明：</Text>
        <br />
        第1个股东：公式1.31 & 公式1.32
        <br />
        第2个股东：公式1.41 & 公式1.42
        <br />
        第3个及以上股东：公式1.51 & 公式1.52
      </div>
    </div>
  );
};

export default ShareholderTable;
