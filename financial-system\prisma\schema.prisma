// 财务管理系统数据库模型
// JavaScript版本 - 避免TypeScript版本冲突

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表 (简化版)
model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 代理商表
model Agent {
  id          Int        @id @default(autoincrement())
  name        String
  clientName  String?
  userName    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  customers   Customer[]
  periods     Period[]
}

// 客户表
model Customer {
  id          Int      @id @default(autoincrement())
  name        String
  userName    String
  agentId     Int
  agent       Agent    @relation(fields: [agentId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联7个数据模块
  module1     Module1[]
  module2     Module2[]
  module3     Module3[]
  module4     Module4[]
  module5     Module5[]
  module6     Module6[]
  module7     Module7[]

  financialData FinancialData[]
}

// 模块1.1 - 基础财务数据模块 (7输入 + 5计算)
model Module1 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 7个数据输入字段
  data1_1     Float?   // 千万整数 - 总收入
  data1_2     Float?   // 十亿整数 - 总支出
  data1_3     Float?   // 百分比 - 利润率
  data1_4     Float?   // 高精度 - 精确金额
  data1_5     Float?   // 百分比 - 增长率
  data1_6     Float?   // 千万整数 - 投资额
  data1_7     Float?   // 百分比 - 税率

  // 5个计算结果字段
  result1_1   Float?   // 净利润
  result1_2   Float?   // 投资回报率
  result1_3   Float?   // 税后利润
  result1_4   Float?   // 利润增长额
  result1_5   Float?   // 综合评分

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 模块2.1 - 成本分析模块 (7输入 + 5计算)
model Module2 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 7个数据输入字段
  data2_1     Float?   // 千万整数 - 直接成本
  data2_2     Float?   // 十亿整数 - 间接成本
  data2_3     Float?   // 百分比 - 成本占比
  data2_4     Float?   // 高精度 - 单位成本
  data2_5     Float?   // 百分比 - 成本增长率
  data2_6     Float?   // 千万整数 - 固定成本
  data2_7     Float?   // 千万整数 - 变动成本

  // 5个计算结果字段
  result2_1   Float?   // 总成本
  result2_2   Float?   // 成本效率
  result2_3   Float?   // 成本节约额
  result2_4   Float?   // 单位利润
  result2_5   Float?   // 成本控制指数

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 模块3.1 - 简化报表模块 (4输入 + 1计算)
model Module3 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 4个数据输入字段
  data3_1     Float?   // 千万整数 - 营业收入
  data3_2     Float?   // 千万整数 - 营业成本
  data3_3     Float?   // 百分比 - 毛利率
  data3_4     Float?   // 高精度 - 关键指标

  // 1个计算结果字段
  result3_1   Float?   // 综合评估分数

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 模块4.1 - 投资回报模块 (7输入 + 5计算)
model Module4 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 7个数据输入字段
  data4_1     Float?   // 千万整数 - 初始投资
  data4_2     Float?   // 十亿整数 - 累计投资
  data4_3     Float?   // 百分比 - 预期收益率
  data4_4     Float?   // 高精度 - 实际收益
  data4_5     Float?   // 百分比 - 风险系数
  data4_6     Float?   // 千万整数 - 回收金额
  data4_7     Float?   // 整数 - 投资周期(月)

  // 5个计算结果字段
  result4_1   Float?   // ROI投资回报率
  result4_2   Float?   // NPV净现值
  result4_3   Float?   // IRR内部收益率
  result4_4   Float?   // 回收期
  result4_5   Float?   // 风险调整收益

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 模块5.1 - 风险评估模块 (7输入 + 5计算)
model Module5 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 7个数据输入字段
  data5_1     Float?   // 千万整数 - 风险敞口
  data5_2     Float?   // 十亿整数 - 保险金额
  data5_3     Float?   // 百分比 - 风险概率
  data5_4     Float?   // 高精度 - 损失预期
  data5_5     Float?   // 百分比 - 风险容忍度
  data5_6     Float?   // 千万整数 - 风险准备金
  data5_7     Float?   // 百分比 - 风险缓解率

  // 5个计算结果字段
  result5_1   Float?   // 风险价值VaR
  result5_2   Float?   // 风险调整资本
  result5_3   Float?   // 预期损失
  result5_4   Float?   // 风险收益比
  result5_5   Float?   // 综合风险评级

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 模块6.1 - 现金流模块 (7输入 + 4计算)
model Module6 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 7个数据输入字段
  data6_1     Float?   // 千万整数 - 经营现金流
  data6_2     Float?   // 十亿整数 - 投资现金流
  data6_3     Float?   // 千万整数 - 筹资现金流
  data6_4     Float?   // 高精度 - 现金周转率
  data6_5     Float?   // 百分比 - 现金比率
  data6_6     Float?   // 千万整数 - 期初现金
  data6_7     Float?   // 千万整数 - 最低现金需求

  // 4个计算结果字段
  result6_1   Float?   // 净现金流
  result6_2   Float?   // 现金流缺口
  result6_3   Float?   // 现金周转天数
  result6_4   Float?   // 现金流健康度

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 模块7.1 - 预算对比模块 (7输入 + 4计算)
model Module7 {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  // 7个数据输入字段
  data7_1     Float?   // 千万整数 - 预算收入
  data7_2     Float?   // 千万整数 - 实际收入
  data7_3     Float?   // 千万整数 - 预算支出
  data7_4     Float?   // 千万整数 - 实际支出
  data7_5     Float?   // 百分比 - 预算执行率
  data7_6     Float?   // 高精度 - 差异分析
  data7_7     Float?   // 百分比 - 调整幅度

  // 4个计算结果字段
  result7_1   Float?   // 收入差异
  result7_2   Float?   // 支出差异
  result7_3   Float?   // 预算偏差率
  result7_4   Float?   // 执行效率指数

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 周期管理表
model Period {
  id          Int      @id @default(autoincrement())
  agentId     Int
  agent       Agent    @relation(fields: [agentId], references: [id])
  periodType  String   // "1-3期" 或 "4-7期"
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 财务数据表
model FinancialData {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id])

  // 公式相关 (JSON字符串存储)
  formulas    String?  // JSON格式存储公式配置
  results     String?  // JSON格式存储计算结果

  // 汇总数据
  totalSum    Float?
  roundedSum  Int?

  // 右侧统计区域
  quantity    Int?
  price       Float?
  income      Float?
  expense     Float?
  cashFlow    Float?
  lastWeekBalance Float?
  reduction   Float?

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 全局公式配置表
model GlobalFormula {
  id          Int      @id @default(autoincrement())
  name        String   @unique // 如: "formula1_1", "formula1_2"
  expression  String   // 公式表达式
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
