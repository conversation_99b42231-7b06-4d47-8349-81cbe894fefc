// API测试脚本 - 测试新布局汇总表API功能
// 直接测试API接口而不依赖浏览器

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';

async function testSummaryLayoutAPI() {
  console.log('🧪 开始测试新布局汇总表API功能...');

  // 测试数据
  const testData = {
    customerData: {
      group1: [
        {
          key: 'customer1',
          customer: '测试客户A',
          user: '测试用户A',
          data1: 1000,
          data2: 2000,
          data3: 0.1,
          data4: 500,
          data5: 0.05,
          data6: 800,
          data7: 1200,
          result1: 3500,
          result2: 2000,
          result3: 1500,
          shareholders: []
        },
        {
          key: 'customer2',
          customer: '测试客户B',
          user: '测试用户B',
          data1: 2000,
          data2: 3000,
          data3: 0.15,
          data4: 800,
          data5: 0.08,
          data6: 1200,
          data7: 1800,
          result1: 4000,
          result2: 2500,
          result3: 2000,
          shareholders: []
        }
      ]
    },
    formulaExpressions: {
      '公式1.1': '(data1+data2+data4)+data3',
      '公式1.2': '(data1+data2+data4)+data3'
    },
    timestamp: new Date().toISOString(),
    totalCustomers: 2
  };

  try {
    // 1. 测试保存数据 (POST)
    console.log('📤 步骤1: 测试保存汇总表数据');
    const saveResponse = await fetch(`${BASE_URL}/api/summary-layout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const saveResult = await saveResponse.json();
    console.log('保存响应:', saveResult);

    if (saveResponse.ok && saveResult.success) {
      console.log('✅ 保存数据成功');
    } else {
      console.log('❌ 保存数据失败:', saveResult.error);
    }

    // 2. 测试获取数据 (GET)
    console.log('📥 步骤2: 测试获取汇总表数据');
    const getResponse = await fetch(`${BASE_URL}/api/summary-layout`);
    const getData = await getResponse.json();
    console.log('获取响应:', getData);

    if (getResponse.ok && getData.success) {
      console.log('✅ 获取数据成功');
      console.log('客户数量:', getData.totalCustomers);
      console.log('保存时间:', getData.savedAt);
    } else {
      console.log('❌ 获取数据失败:', getData.error);
    }

    // 3. 测试更新数据 (PUT)
    console.log('🔄 步骤3: 测试更新汇总表数据');
    const updateData = {
      updateType: 'customerData',
      customerData: {
        group2: [
          {
            key: 'customer3',
            customer: '测试客户C',
            user: '测试用户C',
            data1: 1500,
            data2: 2500,
            result1: 3000,
            result2: 1800,
            result3: 1200,
            shareholders: []
          }
        ]
      }
    };

    const updateResponse = await fetch(`${BASE_URL}/api/summary-layout`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    const updateResult = await updateResponse.json();
    console.log('更新响应:', updateResult);

    if (updateResponse.ok && updateResult.success) {
      console.log('✅ 更新数据成功');
    } else {
      console.log('❌ 更新数据失败:', updateResult.error);
    }

    // 4. 再次获取数据验证更新
    console.log('🔍 步骤4: 验证更新后的数据');
    const getUpdatedResponse = await fetch(`${BASE_URL}/api/summary-layout`);
    const getUpdatedData = await getUpdatedResponse.json();

    if (getUpdatedResponse.ok && getUpdatedData.success) {
      console.log('✅ 验证更新成功');
      console.log('更新后的数据结构:', Object.keys(getUpdatedData.customerData || {}));
    } else {
      console.log('❌ 验证更新失败');
    }

    // 5. 测试删除数据 (DELETE)
    console.log('🗑️ 步骤5: 测试删除汇总表数据');
    const deleteResponse = await fetch(`${BASE_URL}/api/summary-layout`, {
      method: 'DELETE',
    });

    const deleteResult = await deleteResponse.json();
    console.log('删除响应:', deleteResult);

    if (deleteResponse.ok && deleteResult.success) {
      console.log('✅ 删除数据成功');
    } else {
      console.log('❌ 删除数据失败:', deleteResult.error);
    }

    // 6. 验证删除后获取数据
    console.log('🔍 步骤6: 验证删除后的状态');
    const getAfterDeleteResponse = await fetch(`${BASE_URL}/api/summary-layout`);
    const getAfterDeleteData = await getAfterDeleteResponse.json();

    if (getAfterDeleteResponse.ok) {
      if (getAfterDeleteData.success) {
        console.log('⚠️ 删除后仍有数据，可能删除未完全生效');
      } else {
        console.log('✅ 删除验证成功，数据已清空');
      }
    } else {
      console.log('❌ 删除验证失败');
    }

    console.log('🎉 API测试完成！');

  } catch (error) {
    console.error('❌ API测试失败:', error);
    throw error;
  }
}

// 测试页面访问
async function testPageAccess() {
  console.log('🌐 测试页面访问...');
  
  try {
    // 测试汇总表页面是否可访问
    const pageResponse = await fetch(`${BASE_URL}/summary-layout`);
    
    if (pageResponse.ok) {
      console.log('✅ 汇总表页面可访问');
    } else {
      console.log('❌ 汇总表页面访问失败，状态码:', pageResponse.status);
    }
  } catch (error) {
    console.error('❌ 页面访问测试失败:', error);
  }
}

// 运行所有测试
async function runAllTests() {
  try {
    await testSummaryLayoutAPI();
    await testPageAccess();
    console.log('🎊 所有测试完成！');
  } catch (error) {
    console.error('💥 测试过程中出现错误:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runAllTests();
}

module.exports = { testSummaryLayoutAPI, testPageAccess };
