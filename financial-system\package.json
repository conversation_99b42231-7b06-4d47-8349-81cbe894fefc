{"name": "financial-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "node scripts/deploy.js", "deploy:prod": "NODE_ENV=production node scripts/deploy.js", "health-check": "curl -f http://localhost:3000/api/health || exit 1", "warm-up": "curl -f http://localhost:3000/dashboard || exit 1", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate deploy", "db:push": "npx prisma db push", "db:studio": "npx prisma studio", "test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:debug": "npx playwright test --debug"}, "dependencies": {"@prisma/client": "^5.20.0", "antd": "^5.21.0", "better-sqlite3": "^12.2.0", "critters": "^0.0.23", "ioredis": "^5.6.1", "next": "14.2.30", "node-fetch": "^3.3.2", "prisma": "^5.20.0", "puppeteer": "^24.15.0", "react": "^18", "react-dom": "^18", "redis": "^5.6.1", "sqlite3": "^5.1.7", "zustand": "^4.5.5"}, "devDependencies": {"@playwright/test": "^1.54.2", "eslint": "^8", "eslint-config-next": "14.2.30", "prettier": "^3.3.3"}}