'use client';

// 动态公式配置组件
// 根据模块配置动态生成公式输入框

import { Input, Card, Row, Col, Tooltip, Button } from 'antd';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { getModuleConfig } from '../config/moduleConfigs';

const { TextArea } = Input;

export default function DynamicFormulaConfig({
  moduleId,
  formulas = {},
  onFormulaChange,
  onFormulaBlur,
  onResetFormulas
}) {
  const moduleConfig = getModuleConfig(moduleId);
  
  if (!moduleConfig) {
    return <div>模块配置未找到</div>;
  }

  const defaultFormulas = moduleConfig.defaultFormulas;
  const resultMapping = moduleConfig.resultMapping;

  // 处理公式重置
  const handleResetFormulas = () => {
    if (onResetFormulas) {
      onResetFormulas(defaultFormulas);
    }
  };

  // 获取公式对应的结果字段
  const getResultField = (formulaKey) => {
    return Object.entries(resultMapping).find(([resultKey, formula]) => 
      formula === formulaKey
    )?.[0] || '';
  };

  // 获取公式说明
  const getFormulaDescription = (formulaKey) => {
    const resultField = getResultField(formulaKey);
    const fieldConfig = moduleConfig.fields[resultField];
    return fieldConfig ? `计算 ${fieldConfig.title}` : '公式计算';
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>🧮 {moduleConfig.displayName} - 全局公式配置</span>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={handleResetFormulas}
            size="small"
          >
            重置为默认
          </Button>
        </div>
      }
      style={{ marginBottom: '24px' }}
      styles={{ body: { padding: '16px' } }}
    >
      <div style={{ 
        marginBottom: '16px', 
        fontSize: '14px', 
        color: '#666',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <div>
          配置 {moduleConfig.displayName} 的计算公式，支持基本数学运算符 (+, -, *, /, 括号)
        </div>
        <div>
          公式数量: {Object.keys(defaultFormulas).length}个
        </div>
      </div>

      <Row gutter={[16, 16]}>
        {Object.entries(defaultFormulas).map(([formulaKey, defaultValue]) => {
          const currentValue = formulas[formulaKey] || defaultValue;
          const resultField = getResultField(formulaKey);
          const description = getFormulaDescription(formulaKey);

          return (
            <Col xs={24} sm={12} md={8} lg={6} key={formulaKey}>
              <div style={{ marginBottom: '8px' }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '8px',
                  marginBottom: '4px'
                }}>
                  <strong>{formulaKey}:</strong>
                  <Tooltip 
                    title={
                      <div>
                        <div><strong>说明:</strong> {description}</div>
                        <div><strong>结果字段:</strong> {resultField}</div>
                        <div><strong>默认公式:</strong> {defaultValue}</div>
                        <div><strong>支持变量:</strong> data1, data2, data3, data4, data5, data6, data7</div>
                      </div>
                    }
                    placement="top"
                  >
                    <InfoCircleOutlined style={{ color: '#1890ff', cursor: 'help' }} />
                  </Tooltip>
                </div>
                <div style={{ 
                  fontSize: '12px', 
                  color: '#666',
                  marginBottom: '4px'
                }}>
                  → {description}
                </div>
              </div>
              
              <TextArea
                value={currentValue}
                onChange={(e) => onFormulaChange && onFormulaChange(formulaKey, e.target.value)}
                onBlur={(e) => onFormulaBlur && onFormulaBlur(formulaKey, e.target.value)}
                placeholder="输入公式"
                rows={2}
                style={{ 
                  fontSize: '13px',
                  fontFamily: 'monospace'
                }}
              />
              
              {currentValue !== defaultValue && (
                <div style={{ 
                  fontSize: '11px', 
                  color: '#ff7875',
                  marginTop: '4px'
                }}>
                  已修改 (默认: {defaultValue})
                </div>
              )}
            </Col>
          );
        })}
      </Row>

      <div style={{ 
        marginTop: '16px',
        padding: '12px',
        backgroundColor: '#f6ffed',
        border: '1px solid #b7eb8f',
        borderRadius: '6px',
        fontSize: '13px'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
          💡 公式编写提示:
        </div>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '8px' }}>
          <div>• 支持变量: data1-data7</div>
          <div>• 支持运算: +, -, *, /, ()</div>
          <div>• 示例: (data1 + data2) * data3</div>
          <div>• 修改后自动保存并重新计算</div>
        </div>
      </div>
    </Card>
  );
}
