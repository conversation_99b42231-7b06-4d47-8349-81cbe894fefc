<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇总表调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #40a9ff; }
        .result { margin: 10px 0; padding: 10px; background: #f0f8ff; border-radius: 4px; }
        .error { background: #fff2f0; color: #ff4d4f; }
        .success { background: #f6ffed; color: #52c41a; }
    </style>
</head>
<body>
    <h1>🔧 汇总表功能调试</h1>
    
    <div class="debug-section">
        <h3>1. 测试API连接</h3>
        <button onclick="testAPIs()">测试所有API</button>
        <div id="api-results"></div>
    </div>

    <div class="debug-section">
        <h3>2. 测试汇总页面访问</h3>
        <button onclick="testSummaryPage()">访问汇总页面</button>
        <button onclick="testSummaryWithAgent(1)">测试代理商1</button>
        <button onclick="testSummaryWithAgent(2)">测试代理商2</button>
        <div id="summary-results"></div>
    </div>

    <div class="debug-section">
        <h3>3. 测试客户归纳汇总API</h3>
        <button onclick="testConsolidatedAPI()">测试归纳汇总API</button>
        <div id="consolidated-results"></div>
    </div>

    <div class="debug-section">
        <h3>4. 模拟汇总表切换</h3>
        <button onclick="simulateSummaryToggle()">模拟切换汇总表</button>
        <div id="toggle-results"></div>
    </div>

    <div class="debug-section">
        <h3>5. 快速导航</h3>
        <button onclick="openPage('/')">主页</button>
        <button onclick="openPage('/login')">登录</button>
        <button onclick="openPage('/dashboard')">仪表盘</button>
        <button onclick="openPage('/summary/1')">汇总页面</button>
        <button onclick="openPage('/formula-config')">公式配置</button>
    </div>

    <script>
        const BASE_URL = 'http://localhost:3000';

        function addResult(containerId, message, isSuccess = true) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
        }

        async function testAPIs() {
            const container = document.getElementById('api-results');
            container.innerHTML = '';

            const apis = [
                { name: '代理商API', url: '/api/agents' },
                { name: '客户API', url: '/api/customers?agentId=1' },
                { name: '健康检查', url: '/api/health' },
                { name: '归纳汇总API', url: '/api/summary/consolidated?agentId=1' }
            ];

            for (const api of apis) {
                try {
                    const response = await fetch(BASE_URL + api.url);
                    if (response.ok) {
                        const data = await response.json();
                        addResult('api-results', `✅ ${api.name}: 成功 (${JSON.stringify(data).length} 字符)`, true);
                    } else {
                        addResult('api-results', `❌ ${api.name}: HTTP ${response.status}`, false);
                    }
                } catch (error) {
                    addResult('api-results', `❌ ${api.name}: ${error.message}`, false);
                }
            }
        }

        function testSummaryPage() {
            openPage('/summary/1');
            addResult('summary-results', '已打开汇总页面，请检查页面是否正常加载', true);
        }

        function testSummaryWithAgent(agentId) {
            openPage(`/summary/${agentId}`);
            addResult('summary-results', `已打开代理商${agentId}的汇总页面`, true);
        }

        async function testConsolidatedAPI() {
            const container = document.getElementById('consolidated-results');
            container.innerHTML = '';

            try {
                const response = await fetch(`${BASE_URL}/api/summary/consolidated?agentId=1`);
                if (response.ok) {
                    const data = await response.json();
                    addResult('consolidated-results', `✅ 归纳汇总API成功: ${JSON.stringify(data, null, 2)}`, true);
                } else {
                    const errorText = await response.text();
                    addResult('consolidated-results', `❌ API错误 (${response.status}): ${errorText}`, false);
                }
            } catch (error) {
                addResult('consolidated-results', `❌ 网络错误: ${error.message}`, false);
            }
        }

        function simulateSummaryToggle() {
            const container = document.getElementById('toggle-results');
            container.innerHTML = '';

            // 模拟React状态变化
            let showSummaryTable = false;
            let summaryTableVersion = 'v1';

            addResult('toggle-results', `初始状态: showSummaryTable=${showSummaryTable}, version=${summaryTableVersion}`, true);

            // 模拟点击"查看汇总表"
            showSummaryTable = !showSummaryTable;
            addResult('toggle-results', `点击"查看汇总表": showSummaryTable=${showSummaryTable}`, true);

            // 模拟切换版本
            if (summaryTableVersion === 'v1') {
                summaryTableVersion = 'v2';
            } else if (summaryTableVersion === 'v2') {
                summaryTableVersion = 'consolidated';
            } else {
                summaryTableVersion = 'v1';
            }
            addResult('toggle-results', `切换版本: version=${summaryTableVersion}`, true);

            // 显示当前应该渲染的组件
            let componentName = '';
            if (!showSummaryTable) {
                componentName = '模块视图 (DynamicTable)';
            } else if (summaryTableVersion === 'v1') {
                componentName = '原版汇总表 (SummaryTable)';
            } else if (summaryTableVersion === 'v2') {
                componentName = '新版汇总表 (NewSummaryTable)';
            } else if (summaryTableVersion === 'consolidated') {
                componentName = '客户归纳汇总表 (CustomerConsolidatedSummaryTable)';
            }

            addResult('toggle-results', `应该渲染: ${componentName}`, true);
        }

        function openPage(path) {
            window.open(BASE_URL + path, '_blank');
        }

        // 页面加载时自动测试API
        window.onload = function() {
            console.log('调试页面已加载');
            // 自动测试API连接
            setTimeout(testAPIs, 1000);
        };
    </script>
</body>
</html>
