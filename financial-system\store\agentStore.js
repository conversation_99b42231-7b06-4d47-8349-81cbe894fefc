// 代理商状态管理 - Zustand JavaScript版本
// 避免TypeScript版本冲突

import { create } from 'zustand';

export const useAgentStore = create((set, get) => ({
  // 状态
  agents: [],
  loading: false,
  error: null,
  selectedAgent: null,
  
  // Actions
  setAgents: (agents) => set({ agents }),
  
  setLoading: (loading) => set({ loading }),
  
  setError: (error) => set({ error }),
  
  setSelectedAgent: (agent) => set({ selectedAgent: agent }),
  
  // 获取代理商列表
  fetchAgents: async () => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/agents');
      
      if (response.ok) {
        const agents = await response.json();
        set({ agents, loading: false });
        return agents;
      } else {
        throw new Error('获取代理商列表失败');
      }
    } catch (error) {
      console.error('获取代理商列表失败:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },
  
  // 添加代理商
  addAgent: async (agentData) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(agentData),
      });
      
      if (response.ok) {
        const newAgent = await response.json();
        set(state => ({ 
          agents: [...state.agents, newAgent],
          loading: false 
        }));
        return newAgent;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || '添加代理商失败');
      }
    } catch (error) {
      console.error('添加代理商失败:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },
  
  // 更新代理商
  updateAgent: async (id, updates) => {
    // 乐观更新
    set(state => ({
      agents: state.agents.map(a => 
        a.id === id ? { ...a, ...updates } : a
      )
    }));
    
    try {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      
      if (response.ok) {
        const updatedAgent = await response.json();
        set(state => ({
          agents: state.agents.map(a => 
            a.id === id ? updatedAgent : a
          )
        }));
        return updatedAgent;
      } else {
        // 回滚乐观更新
        const { agents } = get();
        const originalAgent = agents.find(a => a.id === id);
        if (originalAgent) {
          set(state => ({
            agents: state.agents.map(a => 
              a.id === id ? originalAgent : a
            )
          }));
        }
        throw new Error('更新代理商失败');
      }
    } catch (error) {
      console.error('更新代理商失败:', error);
      set({ error: error.message });
      throw error;
    }
  },
  
  // 删除代理商
  deleteAgent: async (id) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        set(state => ({
          agents: state.agents.filter(a => a.id !== id),
          loading: false
        }));
      } else {
        throw new Error('删除代理商失败');
      }
    } catch (error) {
      console.error('删除代理商失败:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },
  
  // 根据ID获取代理商
  getAgentById: (id) => {
    const { agents } = get();
    return agents.find(agent => agent.id === id);
  },
  
  // 清除错误
  clearError: () => set({ error: null }),
  
  // 重置状态
  reset: () => set({
    agents: [],
    loading: false,
    error: null,
    selectedAgent: null
  }),
}));
