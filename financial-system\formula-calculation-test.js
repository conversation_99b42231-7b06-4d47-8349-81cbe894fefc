// 专门的公式计算验证测试
const { chromium } = require('playwright');

async function testFormulaCalculations() {
  console.log('🎯 开始公式计算验证测试...\n');
  
  let browser = null;
  try {
    // 启动浏览器
    browser = await chromium.launch({ 
      headless: false,
      slowMo: 2000
    });
    
    const page = await browser.newPage();
    
    // 访问页面
    console.log('📊 访问公式配置页面...');
    await page.goto('http://localhost:3000/formula-config');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 验证页面加载
    const pageLoaded = await page.locator('text=公式配置网格').isVisible();
    if (!pageLoaded) {
      console.log('❌ 页面加载失败');
      return;
    }
    console.log('✅ 页面加载成功');
    
    // 测试用例定义
    const testCases = [
      {
        name: '正数基础测试',
        data: { data1: 100, data2: 2, data3: 50, data4: 1 },
        expectedResults: {
          '公式1.1 (data1+data2+data4)+data3': 153, // (100+2+1)+50 = 153
          '公式2.1 data1*data2': 200,              // 100*2 = 200
          '公式2.2 data2*data4': 2,                // 2*1 = 2
          '公式3.1 data1': 100                     // 100
        }
      },
      {
        name: '负数基础测试',
        data: { data1: -100, data2: -2, data3: -50, data4: -1 },
        expectedResults: {
          '公式1.1 (data1+data2+data4)+data3': -153, // (-100+-2+-1)+(-50) = -153
          '公式2.1 data1*data2': 200,                // (-100)*(-2) = 200
          '公式2.2 data2*data4': 2,                  // (-2)*(-1) = 2
          '公式3.1 data1': -100                      // -100
        }
      },
      {
        name: '正负混合测试',
        data: { data1: 100, data2: -2, data3: 50, data4: -1 },
        expectedResults: {
          '公式1.1 (data1+data2+data4)+data3': 147, // (100+-2+-1)+50 = 147
          '公式2.1 data1*data2': -200,              // 100*(-2) = -200
          '公式2.2 data2*data4': 2,                 // (-2)*(-1) = 2
          '公式3.1 data1': 100                      // 100
        }
      },
      {
        name: '零值测试',
        data: { data1: 0, data2: -2, data3: 0, data4: 1 },
        expectedResults: {
          '公式1.1 (data1+data2+data4)+data3': -1,  // (0+-2+1)+0 = -1
          '公式2.1 data1*data2': 0,                 // 0*(-2) = 0
          '公式2.2 data2*data4': -2,                // (-2)*1 = -2
          '公式3.1 data1': 0                        // 0
        }
      }
    ];
    
    // 执行每个测试用例
    for (const testCase of testCases) {
      console.log(`\n🧪 执行测试: ${testCase.name}`);
      console.log(`📊 输入数据: ${JSON.stringify(testCase.data)}`);
      
      // 输入测试数据
      await inputTestData(page, testCase.data);
      
      // 等待计算完成
      await page.waitForTimeout(3000);
      
      // 获取页面计算结果
      const pageResults = await getPageResults(page);
      console.log(`📈 页面显示的结果数量: ${Object.keys(pageResults).length}`);
      
      // 验证每个预期结果
      console.log('🔍 验证计算结果:');
      let correctCount = 0;
      let totalCount = Object.keys(testCase.expectedResults).length;
      
      for (const [formulaDesc, expected] of Object.entries(testCase.expectedResults)) {
        let found = false;
        let actualValue = null;
        let foundLocation = null;
        
        // 在页面结果中查找匹配的值
        for (const [location, value] of Object.entries(pageResults)) {
          if (Math.abs(value - expected) < 0.01) {
            found = true;
            actualValue = value;
            foundLocation = location;
            break;
          }
        }
        
        if (found) {
          console.log(`   ✅ ${formulaDesc}: 预期=${expected}, 实际=${actualValue} (位置: ${foundLocation})`);
          correctCount++;
        } else {
          // 查找最接近的值
          let closestValue = null;
          let closestLocation = null;
          let minDiff = Infinity;
          
          for (const [location, value] of Object.entries(pageResults)) {
            const diff = Math.abs(value - expected);
            if (diff < minDiff) {
              minDiff = diff;
              closestValue = value;
              closestLocation = location;
            }
          }
          
          console.log(`   ❌ ${formulaDesc}: 预期=${expected}, 最接近=${closestValue} (位置: ${closestLocation}, 差值: ${minDiff.toFixed(2)})`);
        }
      }
      
      const successRate = ((correctCount / totalCount) * 100).toFixed(1);
      console.log(`📊 ${testCase.name} 结果: ${correctCount}/${totalCount} 正确 (${successRate}%)`);
      
      // 显示所有页面结果供参考
      console.log('📋 所有页面结果:');
      Object.entries(pageResults).forEach(([location, value]) => {
        console.log(`   ${location}: ${value}`);
      });
    }
    
    console.log('\n🎉 所有测试完成！');
    console.log('🔍 浏览器保持打开状态，请手动验证结果...');
    console.log('📋 您可以在页面中手动输入数据来进一步验证公式计算');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 输入测试数据到页面
async function inputTestData(page, data) {
  try {
    console.log('📝 输入测试数据...');
    
    // 确保有客户行
    const addButton = page.locator('button:has-text("添加客户")').first();
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(2000);
    }
    
    // 清空现有数据
    const allInputs = await page.$$('table input');
    for (const input of allInputs) {
      try {
        await input.clear();
      } catch (e) {
        // 忽略无法清空的输入框
      }
    }
    
    // 输入新数据
    for (let i = 1; i <= 4; i++) { // 只输入前4个字段，足够测试主要公式
      const fieldName = `data${i}`;
      const value = data[fieldName];
      
      if (value !== undefined) {
        // 尝试多种方式找到输入框
        const selectors = [
          `table input[placeholder*="数据${i}"]`,
          `table tbody tr:first-child td:nth-child(${i + 2}) input`,
          `table input:nth-of-type(${i + 2})` // 跳过客户名和用户名
        ];
        
        let success = false;
        for (const selector of selectors) {
          try {
            const inputs = page.locator(selector);
            const count = await inputs.count();
            
            if (count > 0) {
              const input = inputs.first();
              if (await input.isVisible({ timeout: 1000 })) {
                await input.fill(value.toString());
                await page.waitForTimeout(500);
                
                // 验证输入
                const actualValue = await input.inputValue();
                if (actualValue === value.toString()) {
                  console.log(`   ✅ ${fieldName}: ${value}`);
                  success = true;
                  break;
                }
              }
            }
          } catch (e) {
            // 继续尝试下一个选择器
          }
        }
        
        if (!success) {
          console.log(`   ❌ 无法输入 ${fieldName}: ${value}`);
        }
      }
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    
  } catch (error) {
    console.log(`❌ 输入数据异常: ${error.message}`);
  }
}

// 获取页面计算结果
async function getPageResults(page) {
  try {
    const results = await page.evaluate(() => {
      const resultData = {};
      
      // 查找所有strong元素中的数字
      const strongElements = document.querySelectorAll('strong');
      strongElements.forEach((element, index) => {
        const text = element.textContent.trim();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          resultData[`strong_${index}`] = num;
        }
      });
      
      // 查找表格中的结果
      const tables = document.querySelectorAll('table');
      tables.forEach((table, tableIndex) => {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, rowIndex) => {
          const cells = row.querySelectorAll('td');
          cells.forEach((cell, cellIndex) => {
            const strongInCell = cell.querySelector('strong');
            if (strongInCell) {
              const text = strongInCell.textContent.trim();
              const num = parseFloat(text);
              if (!isNaN(num) && isFinite(num)) {
                resultData[`table_${tableIndex}_row_${rowIndex}_col_${cellIndex}`] = num;
              }
            }
          });
        });
      });
      
      return resultData;
    });
    
    return results;
  } catch (error) {
    console.log(`❌ 获取结果异常: ${error.message}`);
    return {};
  }
}

// 执行测试
testFormulaCalculations().catch(console.error);
