// 单个代理商API路由 - JavaScript版本
// 避免TypeScript版本冲突

import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';

// 获取单个代理商
export async function GET(request, { params }) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的代理商ID' }, 
        { status: 400 }
      );
    }
    
    const agent = await prisma.agent.findUnique({
      where: { id },
      include: {
        customers: {
          include: {
            financialData: true
          }
        },
        periods: true,
      }
    });
    
    if (!agent) {
      return NextResponse.json(
        { error: '代理商不存在' }, 
        { status: 404 }
      );
    }
    
    return NextResponse.json(agent);
  } catch (error) {
    console.error('获取代理商失败:', error);
    return NextResponse.json(
      { error: '获取代理商失败' }, 
      { status: 500 }
    );
  }
}

// 更新代理商
export async function PATCH(request, { params }) {
  try {
    const id = parseInt(params.id);
    const data = await request.json();
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的代理商ID' }, 
        { status: 400 }
      );
    }
    
    // 检查代理商是否存在
    const existingAgent = await prisma.agent.findUnique({
      where: { id }
    });
    
    if (!existingAgent) {
      return NextResponse.json(
        { error: '代理商不存在' }, 
        { status: 404 }
      );
    }
    
    // 更新代理商
    const updatedAgent = await prisma.agent.update({
      where: { id },
      data: {
        name: data.name?.trim(),
        clientName: data.clientName?.trim() || null,
        userName: data.userName?.trim() || null,
      },
      include: {
        customers: {
          include: {
            financialData: true
          }
        },
        periods: true,
      }
    });
    
    return NextResponse.json(updatedAgent);
  } catch (error) {
    console.error('更新代理商失败:', error);
    
    // 处理唯一约束错误
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: '代理商名称已存在' }, 
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: '更新代理商失败' }, 
      { status: 500 }
    );
  }
}

// 删除代理商
export async function DELETE(request, { params }) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的代理商ID' }, 
        { status: 400 }
      );
    }
    
    // 检查代理商是否存在
    const existingAgent = await prisma.agent.findUnique({
      where: { id },
      include: {
        customers: true
      }
    });
    
    if (!existingAgent) {
      return NextResponse.json(
        { error: '代理商不存在' }, 
        { status: 404 }
      );
    }
    
    // 检查是否有关联的客户数据
    if (existingAgent.customers.length > 0) {
      return NextResponse.json(
        { error: '该代理商下还有客户数据，无法删除' }, 
        { status: 400 }
      );
    }
    
    // 删除代理商
    await prisma.agent.delete({
      where: { id }
    });
    
    return NextResponse.json({ message: '代理商删除成功' });
  } catch (error) {
    console.error('删除代理商失败:', error);
    return NextResponse.json(
      { error: '删除代理商失败' }, 
      { status: 500 }
    );
  }
}
