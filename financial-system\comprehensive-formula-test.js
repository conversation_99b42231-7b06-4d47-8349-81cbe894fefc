// 财务管理系统公式计算全面测试脚本
// 使用Playwright在真实浏览器中测试所有数学运算和负数处理

const { chromium } = require('playwright');

class ComprehensiveFormulaTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:3000';
    this.testResults = [];
    this.currentTestGroup = '';
  }

  // 记录测试结果
  logTestResult(testName, expected, actual, passed, details = '') {
    const result = {
      group: this.currentTestGroup,
      test: testName,
      expected,
      actual,
      passed,
      details,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: 预期=${expected}, 实际=${actual} ${details}`);
  }

  // 手动计算预期结果
  calculateExpected(expression, data) {
    try {
      // 安全的表达式计算
      let safeExpression = expression
        .replace(/data1/g, `(${data.data1 || 0})`)
        .replace(/data2/g, `(${data.data2 || 0})`)
        .replace(/data3/g, `(${data.data3 || 0})`)
        .replace(/data4/g, `(${data.data4 || 0})`)
        .replace(/data5/g, `(${data.data5 || 0})`)
        .replace(/data6/g, `(${data.data6 || 0})`)
        .replace(/data7/g, `(${data.data7 || 0})`)
        .replace(/\^/g, '**'); // 处理幂运算
      
      const result = eval(safeExpression);
      return isNaN(result) || !isFinite(result) ? 0 : Math.round(result);
    } catch (error) {
      console.log(`❌ 计算预期结果失败: ${expression}, 错误: ${error.message}`);
      return 0;
    }
  }

  // 启动浏览器
  async setup() {
    console.log('🚀 启动浏览器进行全面公式测试...');
    this.browser = await chromium.launch({ 
      headless: false,  // 保持浏览器可见
      slowMo: 1500,     // 慢速执行便于观察
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    this.page = await context.newPage();
    
    // 监听页面日志
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🔴 页面错误: ${msg.text()}`);
      }
    });
  }

  // 访问公式配置页面
  async navigateToFormulaPage() {
    console.log('📊 访问公式配置页面...');
    await this.page.goto(`${this.baseUrl}/formula-config`);
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(3000);
    
    // 验证页面加载
    const pageLoaded = await this.page.locator('text=公式配置网格').isVisible();
    if (pageLoaded) {
      console.log('✅ 公式配置页面加载成功');
      return true;
    } else {
      console.log('❌ 公式配置页面加载失败');
      return false;
    }
  }

  // 清空所有输入框
  async clearAllInputs() {
    console.log('🧹 清空所有输入框...');
    try {
      // 查找所有数据输入框并清空
      const inputs = await this.page.$$('table input[type="text"], table input:not([type])');
      for (const input of inputs) {
        try {
          await input.clear();
          await this.page.waitForTimeout(100);
        } catch (e) {
          // 忽略无法清空的输入框
        }
      }
    } catch (error) {
      console.log(`⚠️ 清空输入框时出现问题: ${error.message}`);
    }
  }

  // 在浏览器中输入测试数据
  async inputTestDataToBrowser(data, testName) {
    console.log(`📝 输入${testName}测试数据: ${JSON.stringify(data)}`);
    
    try {
      // 先清空所有输入框
      await this.clearAllInputs();
      await this.page.waitForTimeout(1000);
      
      // 确保有客户行存在
      const addButton = this.page.locator('button:has-text("添加客户")').first();
      if (await addButton.isVisible()) {
        await addButton.click();
        await this.page.waitForTimeout(2000);
      }

      // 输入数据到各个字段
      for (let i = 1; i <= 7; i++) {
        const fieldName = `data${i}`;
        const value = data[fieldName];
        
        if (value !== undefined) {
          // 尝试多种选择器策略找到输入框
          const selectors = [
            `table input[placeholder*="数据${i}"]`,
            `table input[title*="数据${i}"]`,
            `table tbody tr:first-child td:nth-child(${i + 2}) input`,
            `table .ant-input:nth-of-type(${i})`,
            `input[data-field="data${i}"]`
          ];
          
          let success = false;
          for (const selector of selectors) {
            try {
              const inputs = this.page.locator(selector);
              const count = await inputs.count();
              
              if (count > 0) {
                const input = inputs.first();
                if (await input.isVisible({ timeout: 1000 })) {
                  await input.clear();
                  await input.fill(value.toString());
                  await this.page.waitForTimeout(300);
                  
                  // 验证输入值
                  const actualValue = await input.inputValue();
                  if (actualValue === value.toString()) {
                    console.log(`  ✅ 成功输入 ${fieldName}: ${value}`);
                    success = true;
                    break;
                  }
                }
              }
            } catch (e) {
              // 继续尝试下一个选择器
            }
          }
          
          if (!success) {
            console.log(`  ❌ 未找到或无法输入 ${fieldName} 的输入框`);
          }
        }
      }

      // 触发计算
      console.log('⚡ 触发计算...');
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(3000);
      
    } catch (error) {
      console.log(`❌ 输入数据异常: ${error.message}`);
    }
  }

  // 从页面获取计算结果
  async getCalculationResultsFromPage() {
    try {
      const results = await this.page.evaluate(() => {
        const resultData = {};
        
        // 查找所有包含数字的strong元素
        const strongElements = document.querySelectorAll('strong');
        strongElements.forEach((element, index) => {
          const text = element.textContent.trim();
          if (!isNaN(text) && text !== '' && text !== '0' && text !== 'NaN') {
            resultData[`strong_${index}`] = parseFloat(text);
          }
        });
        
        // 查找表格中的结果列
        const tables = document.querySelectorAll('table');
        tables.forEach((table, tableIndex) => {
          const rows = table.querySelectorAll('tbody tr');
          rows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, cellIndex) => {
              const strongInCell = cell.querySelector('strong');
              if (strongInCell) {
                const text = strongInCell.textContent.trim();
                if (!isNaN(text) && text !== '' && text !== '0' && text !== 'NaN') {
                  resultData[`table_${tableIndex}_row_${rowIndex}_col_${cellIndex}`] = parseFloat(text);
                }
              }
            });
          });
        });
        
        return resultData;
      });
      
      return results;
    } catch (error) {
      console.log(`❌ 获取结果异常: ${error.message}`);
      return {};
    }
  }

  // 获取页面中的公式表达式
  async getFormulaExpressions() {
    console.log('📋 获取公式表达式...');

    try {
      // 点击第一个公式卡片获取表达式
      const formulaCard = this.page.locator('.formula-card').first();
      if (await formulaCard.isVisible()) {
        await formulaCard.click();
        await this.page.waitForTimeout(1000);

        // 获取公式表达式
        const expression = await this.page.locator('textarea').inputValue();
        console.log(`📐 获取到公式1.1表达式: ${expression}`);

        // 关闭模态框
        const cancelButton = this.page.locator('button:has-text("取消"), .ant-modal-close');
        if (await cancelButton.isVisible()) {
          await cancelButton.click();
          await this.page.waitForTimeout(500);
        }

        return {
          '公式1.1': expression,
          '公式1.2': expression, // 通常相同
          '公式2.1': 'data1*data2',
          '公式2.2': 'data2*data4',
          '公式3.1': 'data1'
        };
      }
    } catch (error) {
      console.log(`❌ 获取公式表达式失败: ${error.message}`);
    }

    // 返回默认公式
    return {
      '公式1.1': '(data1+data2+data4)+data3',
      '公式1.2': '(data1+data2+data4)+data3',
      '公式2.1': 'data1*data2',
      '公式2.2': 'data2*data4',
      '公式3.1': 'data1'
    };
  }

  // 执行单个测试用例
  async runSingleTestCase(testData, testName, formulas) {
    this.currentTestGroup = testName;
    console.log(`\n🧪 执行${testName}测试...`);
    console.log(`📊 测试数据: ${JSON.stringify(testData)}`);

    // 输入测试数据
    await this.inputTestDataToBrowser(testData, testName);

    // 获取页面计算结果
    const pageResults = await this.getCalculationResultsFromPage();
    console.log(`📈 页面计算结果: ${JSON.stringify(pageResults, null, 2)}`);

    // 验证每个公式的计算结果
    console.log(`\n🔍 验证${testName}公式计算结果:`);
    console.log('='.repeat(60));

    for (const [formulaName, expression] of Object.entries(formulas)) {
      const expected = this.calculateExpected(expression, testData);

      console.log(`\n📐 ${formulaName}:`);
      console.log(`   表达式: ${expression}`);
      console.log(`   预期结果: ${expected}`);

      // 从页面结果中查找匹配的值
      let found = false;
      let actualValue = null;

      for (const [key, value] of Object.entries(pageResults)) {
        if (Math.abs(value - expected) < 0.01) { // 允许小的浮点误差
          actualValue = value;
          found = true;
          console.log(`   ✅ 在 ${key} 找到匹配结果: ${value}`);
          this.logTestResult(formulaName, expected, value, true, `在${key}找到匹配`);
          break;
        }
      }

      if (!found) {
        // 查找最接近的值
        let closestValue = null;
        let closestKey = null;
        let minDiff = Infinity;

        for (const [key, value] of Object.entries(pageResults)) {
          const diff = Math.abs(value - expected);
          if (diff < minDiff) {
            minDiff = diff;
            closestValue = value;
            closestKey = key;
          }
        }

        if (closestValue !== null) {
          console.log(`   ❌ 未找到精确匹配，最接近的是 ${closestKey}: ${closestValue} (差值: ${minDiff})`);
          this.logTestResult(formulaName, expected, closestValue, false, `最接近值在${closestKey}，差值${minDiff}`);
        } else {
          console.log(`   ❌ 未找到任何匹配的结果`);
          this.logTestResult(formulaName, expected, 'N/A', false, '未找到结果');
        }
      }
    }

    console.log('='.repeat(60));
  }

  // 基本数学运算测试
  async testBasicMathOperations() {
    console.log('\n🔢 开始基本数学运算测试...');

    const formulas = await this.getFormulaExpressions();

    // 1. 加法运算测试
    console.log('\n➕ 加法运算测试');
    await this.runSingleTestCase(
      { data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 },
      '正数加法',
      formulas
    );

    await this.runSingleTestCase(
      { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 },
      '负数加法',
      formulas
    );

    await this.runSingleTestCase(
      { data1: 100, data2: -2, data3: 50, data4: -1, data5: 25, data6: -80, data7: 3 },
      '正负数混合加法',
      formulas
    );

    // 2. 乘法运算测试
    console.log('\n✖️ 乘法运算测试');
    await this.runSingleTestCase(
      { data1: 10, data2: 5, data3: 2, data4: 3, data5: 4, data6: 6, data7: 2 },
      '正数乘法',
      formulas
    );

    await this.runSingleTestCase(
      { data1: -10, data2: -5, data3: -2, data4: -3, data5: -4, data6: -6, data7: -2 },
      '负数乘法',
      formulas
    );

    await this.runSingleTestCase(
      { data1: 10, data2: -5, data3: 2, data4: -3, data5: 4, data6: -6, data7: 2 },
      '正负数混合乘法',
      formulas
    );
  }

  // 负数处理专项测试
  async testNegativeNumberHandling() {
    console.log('\n➖ 开始负数处理专项测试...');

    const formulas = await this.getFormulaExpressions();

    // 单个负数测试
    await this.runSingleTestCase(
      { data1: -100, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0 },
      '单个负数-100',
      formulas
    );

    await this.runSingleTestCase(
      { data1: 0, data2: -2.5, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0 },
      '单个负小数-2.5',
      formulas
    );

    // 全负数测试
    await this.runSingleTestCase(
      { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 },
      '全负数数据集',
      formulas
    );

    // 零值混合测试
    await this.runSingleTestCase(
      { data1: 0, data2: -2, data3: 0, data4: 1, data5: 0, data6: -80, data7: 0 },
      '零值混合测试',
      formulas
    );
  }

  // 边界值测试
  async testBoundaryValues() {
    console.log('\n🔬 开始边界值测试...');

    const formulas = await this.getFormulaExpressions();

    // 大数值测试
    await this.runSingleTestCase(
      { data1: 1000000, data2: 500, data3: 250000, data4: 100, data5: 50000, data6: 800000, data7: 300 },
      '大数值测试',
      formulas
    );

    // 小数值测试
    await this.runSingleTestCase(
      { data1: 0.1, data2: 0.01, data3: 0.5, data4: 0.001, data5: 0.25, data6: 0.8, data7: 0.03 },
      '小数值测试',
      formulas
    );

    // 负大数值测试
    await this.runSingleTestCase(
      { data1: -1000000, data2: -500, data3: -250000, data4: -100, data5: -50000, data6: -800000, data7: -300 },
      '负大数值测试',
      formulas
    );
  }

  // 生成测试报告
  generateTestReport() {
    console.log('\n📋 生成测试报告...');
    console.log('='.repeat(80));
    console.log('🎯 财务管理系统公式计算全面测试报告');
    console.log('='.repeat(80));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0;

    console.log(`📊 测试统计:`);
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过测试: ${passedTests}`);
    console.log(`   失败测试: ${failedTests}`);
    console.log(`   成功率: ${successRate}%`);

    // 按测试组分类显示结果
    const groupedResults = {};
    this.testResults.forEach(result => {
      if (!groupedResults[result.group]) {
        groupedResults[result.group] = [];
      }
      groupedResults[result.group].push(result);
    });

    console.log('\n📈 分组测试结果:');
    for (const [group, results] of Object.entries(groupedResults)) {
      const groupPassed = results.filter(r => r.passed).length;
      const groupTotal = results.length;
      const groupRate = ((groupPassed / groupTotal) * 100).toFixed(1);

      console.log(`\n🔸 ${group} (${groupPassed}/${groupTotal}, ${groupRate}%)`);
      results.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`   ${status} ${result.test}: 预期=${result.expected}, 实际=${result.actual}`);
        if (result.details) {
          console.log(`      详情: ${result.details}`);
        }
      });
    }

    // 失败测试汇总
    const failedTestsList = this.testResults.filter(r => !r.passed);
    if (failedTestsList.length > 0) {
      console.log('\n🔴 失败测试详细分析:');
      failedTestsList.forEach((result, index) => {
        console.log(`${index + 1}. ${result.group} - ${result.test}`);
        console.log(`   预期: ${result.expected}, 实际: ${result.actual}`);
        console.log(`   详情: ${result.details}`);
        console.log('');
      });
    }

    console.log('='.repeat(80));

    // 总结
    if (successRate >= 90) {
      console.log('🎉 测试结果优秀！公式计算功能运行良好。');
    } else if (successRate >= 70) {
      console.log('⚠️ 测试结果良好，但有一些问题需要关注。');
    } else {
      console.log('❌ 测试结果不理想，需要检查公式计算逻辑。');
    }
  }

  // 执行全面测试
  async runComprehensiveTest() {
    console.log('🎯 开始执行财务管理系统公式计算全面测试...\n');

    try {
      // 1. 启动浏览器并访问页面
      await this.setup();
      const pageLoaded = await this.navigateToFormulaPage();

      if (!pageLoaded) {
        console.log('❌ 页面加载失败，测试终止');
        return;
      }

      // 2. 执行基本数学运算测试
      await this.testBasicMathOperations();

      // 3. 执行负数处理专项测试
      await this.testNegativeNumberHandling();

      // 4. 执行边界值测试
      await this.testBoundaryValues();

      // 5. 生成测试报告
      this.generateTestReport();

      console.log('\n🎉 全面测试完成！');
      console.log('🔍 浏览器将保持打开状态，请手动检查页面结果');
      console.log('📋 详细测试结果已在控制台输出');

    } catch (error) {
      console.log(`❌ 测试执行异常: ${error.message}`);
      console.error(error);
    } finally {
      // 保持浏览器打开以便手动检查
      console.log('\n⏳ 浏览器保持打开状态...');
      console.log('按 Ctrl+C 退出测试');

      // 等待用户手动关闭
      await new Promise(() => {});
    }
  }
}

// 执行测试
async function main() {
  const tester = new ComprehensiveFormulaTest();
  await tester.runComprehensiveTest();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ComprehensiveFormulaTest;
}
