// 汇总表计算工具
// 跨模块数据汇总和统计计算

import { MODULE_CONFIGS } from '../config/moduleConfigs';
import { FormulaEngine } from './calculations';

export class SummaryCalculations {
  
  /**
   * 跨模块数据汇总计算
   * @param {Array} customers - 所有客户数据
   * @param {Array} selectedModules - 选中的模块
   * @returns {Array} 汇总后的数据
   */
  static calculateCrossModuleSummary(customers = [], selectedModules = ['all']) {
    if (!customers || customers.length === 0) return [];

    // 按客户名和用户名分组
    const customerGroups = {};
    
    customers.forEach(customer => {
      const key = `${customer.name || '未知客户'}_${customer.userName || '未知用户'}`;
      
      if (!customerGroups[key]) {
        customerGroups[key] = {
          customerName: customer.name || '未知客户',
          userName: customer.userName || '未知用户',
          moduleData: {},
          calculatedResults: {},
          totals: {},
          metadata: {
            lastUpdated: new Date(),
            customerCount: 0,
            moduleCount: 0
          }
        };
      }
      
      // 确定模块ID
      const moduleId = customer.moduleId || 'module1_1';
      
      // 过滤选中的模块
      if (selectedModules.includes('all') || selectedModules.includes(moduleId)) {
        // 添加模块数据
        customerGroups[key].moduleData[moduleId] = {
          data1: parseFloat(customer.data1) || 0,
          data2: parseFloat(customer.data2) || 0,
          data3: parseFloat(customer.data3) || 0,
          data4: parseFloat(customer.data4) || 0,
          data5: parseFloat(customer.data5) || 0,
          data6: parseFloat(customer.data6) || 0,
          data7: parseFloat(customer.data7) || 0,
          result1: parseFloat(customer.result1) || 0,
          result2: parseFloat(customer.result2) || 0,
          result3: parseFloat(customer.result3) || 0,
          result4: parseFloat(customer.result4) || 0,
          result5: parseFloat(customer.result5) || 0,
          createdAt: customer.createdAt,
          updatedAt: customer.updatedAt
        };
        
        customerGroups[key].metadata.customerCount++;
      }
    });

    // 计算每个客户组的汇总数据
    Object.values(customerGroups).forEach(group => {
      this.calculateGroupTotals(group);
      this.calculateGroupFormulas(group);
    });

    return Object.values(customerGroups);
  }

  /**
   * 计算客户组的汇总数据
   * @param {Object} group - 客户组数据
   */
  static calculateGroupTotals(group) {
    let totalRevenue = 0;
    let totalExpense = 0;
    let totalResult1 = 0;
    let totalResult2 = 0;
    let totalResult3 = 0;
    let moduleCount = 0;

    // 遍历所有模块数据
    Object.entries(group.moduleData).forEach(([moduleId, data]) => {
      const moduleConfig = MODULE_CONFIGS[moduleId];
      
      if (moduleConfig) {
        // 根据模块类型累加数据
        totalRevenue += data.data1; // 通常data1代表收入
        totalExpense += data.data2;  // 通常data2代表支出
        totalResult1 += data.result1;
        totalResult2 += data.result2;
        totalResult3 += data.result3;
        moduleCount++;
      }
    });

    // 计算汇总指标
    const netProfit = totalRevenue - totalExpense;
    const profitRate = totalRevenue > 0 ? (netProfit / totalRevenue * 100) : 0;
    const avgResult = moduleCount > 0 ? totalResult1 / moduleCount : 0;

    group.totals = {
      totalRevenue,
      totalExpense,
      netProfit,
      profitRate,
      totalResult1,
      totalResult2,
      totalResult3,
      avgResult,
      moduleCount
    };

    group.metadata.moduleCount = moduleCount;
  }

  /**
   * 计算客户组的公式结果
   * @param {Object} group - 客户组数据
   */
  static calculateGroupFormulas(group) {
    const totals = group.totals;
    
    // 跨模块公式计算
    group.calculatedResults = {
      // 公式1: 总收入的10%作为管理费
      formula1: totals.totalRevenue * 0.1,
      
      // 公式2: 总支出的5%作为风险准备金
      formula2: totals.totalExpense * 0.05,
      
      // 综合评分: 基于利润率和模块数量的综合评分
      综合评分: this.calculateComprehensiveScore(totals),
      
      // 投资回报率: 净利润/总投入
      投资回报率: totals.totalExpense > 0 ? 
        (totals.netProfit / totals.totalExpense * 100) : 0,
      
      // 风险系数: 基于数据波动的风险评估
      风险系数: this.calculateRiskFactor(group.moduleData),
      
      // 增长潜力: 基于历史数据的增长潜力评估
      增长潜力: this.calculateGrowthPotential(group.moduleData)
    };
  }

  /**
   * 计算综合评分
   * @param {Object} totals - 汇总数据
   * @returns {number} 综合评分 (0-100)
   */
  static calculateComprehensiveScore(totals) {
    let score = 50; // 基础分数

    // 利润率影响 (40分)
    if (totals.profitRate > 20) score += 40;
    else if (totals.profitRate > 10) score += 30;
    else if (totals.profitRate > 0) score += 20;
    else if (totals.profitRate > -10) score += 10;
    else score -= 10;

    // 收入规模影响 (30分)
    if (totals.totalRevenue > 1000000) score += 30;
    else if (totals.totalRevenue > 500000) score += 20;
    else if (totals.totalRevenue > 100000) score += 10;

    // 模块多样性影响 (20分)
    if (totals.moduleCount >= 5) score += 20;
    else if (totals.moduleCount >= 3) score += 15;
    else if (totals.moduleCount >= 2) score += 10;

    // 确保分数在0-100范围内
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * 计算风险系数
   * @param {Object} moduleData - 模块数据
   * @returns {number} 风险系数 (0-1)
   */
  static calculateRiskFactor(moduleData) {
    const modules = Object.values(moduleData);
    if (modules.length === 0) return 0.5;

    // 计算收入的标准差作为风险指标
    const revenues = modules.map(m => m.data1);
    const avgRevenue = revenues.reduce((sum, r) => sum + r, 0) / revenues.length;
    const variance = revenues.reduce((sum, r) => sum + Math.pow(r - avgRevenue, 2), 0) / revenues.length;
    const stdDev = Math.sqrt(variance);
    
    // 标准化风险系数 (0-1)
    const riskFactor = avgRevenue > 0 ? Math.min(1, stdDev / avgRevenue) : 0.5;
    
    return Math.round(riskFactor * 100) / 100;
  }

  /**
   * 计算增长潜力
   * @param {Object} moduleData - 模块数据
   * @returns {number} 增长潜力评分 (0-100)
   */
  static calculateGrowthPotential(moduleData) {
    const modules = Object.values(moduleData);
    if (modules.length === 0) return 50;

    let potentialScore = 50; // 基础分数

    // 基于模块数量的增长潜力
    potentialScore += modules.length * 5;

    // 基于平均利润率的增长潜力
    const avgProfitRate = modules.reduce((sum, m) => {
      const rate = m.data1 > 0 ? ((m.result1 || 0) / m.data1 * 100) : 0;
      return sum + rate;
    }, 0) / modules.length;

    if (avgProfitRate > 15) potentialScore += 20;
    else if (avgProfitRate > 10) potentialScore += 15;
    else if (avgProfitRate > 5) potentialScore += 10;

    // 基于数据完整性的增长潜力
    const dataCompleteness = modules.reduce((sum, m) => {
      const nonZeroFields = Object.values(m).filter(v => v > 0).length;
      return sum + nonZeroFields;
    }, 0) / (modules.length * 12); // 假设每个模块有12个字段

    potentialScore += dataCompleteness * 20;

    return Math.max(0, Math.min(100, Math.round(potentialScore)));
  }

  /**
   * 计算全局统计数据
   * @param {Array} summaryData - 汇总数据
   * @returns {Object} 全局统计
   */
  static calculateGlobalStats(summaryData) {
    if (!summaryData || summaryData.length === 0) {
      return {
        totalCustomers: 0,
        totalRevenue: 0,
        totalExpense: 0,
        totalProfit: 0,
        avgProfitRate: 0,
        totalModules: 0,
        avgScore: 0
      };
    }

    const stats = summaryData.reduce((acc, item) => {
      acc.totalCustomers += 1;
      acc.totalRevenue += item.totals.totalRevenue || 0;
      acc.totalExpense += item.totals.totalExpense || 0;
      acc.totalProfit += item.totals.netProfit || 0;
      acc.totalModules += item.metadata.moduleCount || 0;
      acc.totalScore += item.calculatedResults.综合评分 || 0;
      return acc;
    }, {
      totalCustomers: 0,
      totalRevenue: 0,
      totalExpense: 0,
      totalProfit: 0,
      totalModules: 0,
      totalScore: 0
    });

    return {
      ...stats,
      avgProfitRate: stats.totalRevenue > 0 ? 
        (stats.totalProfit / stats.totalRevenue * 100) : 0,
      avgScore: stats.totalCustomers > 0 ? 
        (stats.totalScore / stats.totalCustomers) : 0,
      avgModulesPerCustomer: stats.totalCustomers > 0 ? 
        (stats.totalModules / stats.totalCustomers) : 0
    };
  }

  /**
   * 导出数据为Excel格式
   * @param {Array} summaryData - 汇总数据
   * @returns {Array} Excel格式的数据
   */
  static exportToExcelFormat(summaryData) {
    return summaryData.map(item => ({
      '客户名': item.customerName || '未知客户',
      '用户名': item.userName || '未知用户',
      '总收入': item.totals?.totalRevenue || 0,
      '总支出': item.totals?.totalExpense || 0,
      '净利润': item.totals?.netProfit || 0,
      '利润率(%)': (item.totals?.profitRate || 0).toFixed(2),
      '公式1': item.calculatedResults?.formula1 || 0,
      '公式2': item.calculatedResults?.formula2 || 0,
      '综合评分': item.calculatedResults?.综合评分 || 0,
      '投资回报率(%)': (item.calculatedResults?.投资回报率 || 0).toFixed(2),
      '风险系数': item.calculatedResults?.风险系数 || 0,
      '增长潜力': item.calculatedResults?.增长潜力 || 0,
      '模块数量': item.metadata?.moduleCount || 0,
      '客户记录数': item.metadata?.customerCount || 0,
      '最后更新': item.metadata?.lastUpdated?.toLocaleString() || '未知时间'
    }));
  }

  /**
   * 格式化显示数值
   * @param {number} value - 数值
   * @param {string} type - 格式类型
   * @returns {string} 格式化后的字符串
   */
  static formatDisplayValue(value, type = 'number') {
    if (value === null || value === undefined || isNaN(value)) return '-';
    
    switch (type) {
      case 'currency':
        return new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY'
        }).format(value);
      case 'percentage':
        return `${value.toFixed(2)}%`;
      case 'integer':
        return Math.round(value).toLocaleString();
      case 'decimal':
        return value.toFixed(2);
      default:
        return value.toLocaleString();
    }
  }
}
