{"testTime": "2025-07-30T06:58:45.474Z", "testType": "comprehensive_formula_calculation", "summary": {"totalTests": 36, "passedTests": 32, "failedTests": 0, "warningTests": 4}, "detailedResults": [{"testSet": "正常业务数据", "formulaId": "formula1_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 34264800, "issues": [], "error": null}, {"testSet": "正常业务数据", "formulaId": "formula1_2", "formulaName": "基础加乘公式", "expression": "(data1+data2*data4)", "status": "PASS", "result": 28554, "issues": [], "error": null}, {"testSet": "正常业务数据", "formulaId": "formula2_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 34264800, "issues": [], "error": null}, {"testSet": "正常业务数据", "formulaId": "formula2_2", "formulaName": "多重乘积公式", "expression": "(data1+data2*data4)*data3*data5", "status": "PASS", "result": 37691280000, "issues": [], "error": null}, {"testSet": "正常业务数据", "formulaId": "formula6_1", "formulaName": "直接取值", "expression": "data1", "status": "PASS", "result": 25656, "issues": [], "error": null}, {"testSet": "正常业务数据", "formulaId": "formula6_2", "formulaName": "简单乘积", "expression": "data2*data4", "status": "PASS", "result": 2898, "issues": [], "error": null}, {"testSet": "小数值测试", "formulaId": "formula1_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 200.8, "issues": [], "error": null}, {"testSet": "小数值测试", "formulaId": "formula1_2", "formulaName": "基础加乘公式", "expression": "(data1+data2*data4)", "status": "PASS", "result": 100.4, "issues": [], "error": null}, {"testSet": "小数值测试", "formulaId": "formula2_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 200.8, "issues": [], "error": null}, {"testSet": "小数值测试", "formulaId": "formula2_2", "formulaName": "多重乘积公式", "expression": "(data1+data2*data4)*data3*data5", "status": "WARNING", "result": 301.20000000000005, "issues": ["小数位数过多，可能存在精度问题"], "error": null}, {"testSet": "小数值测试", "formulaId": "formula6_1", "formulaName": "直接取值", "expression": "data1", "status": "PASS", "result": 100, "issues": [], "error": null}, {"testSet": "小数值测试", "formulaId": "formula6_2", "formulaName": "简单乘积", "expression": "data2*data4", "status": "PASS", "result": 0.4, "issues": [], "error": null}, {"testSet": "大数值测试", "formulaId": "formula1_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 501050000, "issues": [], "error": null}, {"testSet": "大数值测试", "formulaId": "formula1_2", "formulaName": "基础加乘公式", "expression": "(data1+data2*data4)", "status": "PASS", "result": 1002100, "issues": [], "error": null}, {"testSet": "大数值测试", "formulaId": "formula2_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 501050000, "issues": [], "error": null}, {"testSet": "大数值测试", "formulaId": "formula2_2", "formulaName": "多重乘积公式", "expression": "(data1+data2*data4)*data3*data5", "status": "PASS", "result": 150315000000, "issues": [], "error": null}, {"testSet": "大数值测试", "formulaId": "formula6_1", "formulaName": "直接取值", "expression": "data1", "status": "PASS", "result": 1000000, "issues": [], "error": null}, {"testSet": "大数值测试", "formulaId": "formula6_2", "formulaName": "简单乘积", "expression": "data2*data4", "status": "PASS", "result": 2100, "issues": [], "error": null}, {"testSet": "零值测试", "formulaId": "formula1_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 0, "issues": [], "error": null}, {"testSet": "零值测试", "formulaId": "formula1_2", "formulaName": "基础加乘公式", "expression": "(data1+data2*data4)", "status": "PASS", "result": 0, "issues": [], "error": null}, {"testSet": "零值测试", "formulaId": "formula2_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": 0, "issues": [], "error": null}, {"testSet": "零值测试", "formulaId": "formula2_2", "formulaName": "多重乘积公式", "expression": "(data1+data2*data4)*data3*data5", "status": "PASS", "result": 0, "issues": [], "error": null}, {"testSet": "零值测试", "formulaId": "formula6_1", "formulaName": "直接取值", "expression": "data1", "status": "PASS", "result": 0, "issues": [], "error": null}, {"testSet": "零值测试", "formulaId": "formula6_2", "formulaName": "简单乘积", "expression": "data2*data4", "status": "PASS", "result": 0, "issues": [], "error": null}, {"testSet": "负数测试", "formulaId": "formula1_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": -2991, "issues": [], "error": null}, {"testSet": "负数测试", "formulaId": "formula1_2", "formulaName": "基础加乘公式", "expression": "(data1+data2*data4)", "status": "PASS", "result": -997, "issues": [], "error": null}, {"testSet": "负数测试", "formulaId": "formula2_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "PASS", "result": -2991, "issues": [], "error": null}, {"testSet": "负数测试", "formulaId": "formula2_2", "formulaName": "多重乘积公式", "expression": "(data1+data2*data4)*data3*data5", "status": "PASS", "result": -5982, "issues": [], "error": null}, {"testSet": "负数测试", "formulaId": "formula6_1", "formulaName": "直接取值", "expression": "data1", "status": "PASS", "result": -1000, "issues": [], "error": null}, {"testSet": "负数测试", "formulaId": "formula6_2", "formulaName": "简单乘积", "expression": "data2*data4", "status": "PASS", "result": 3, "issues": [], "error": null}, {"testSet": "极小值测试", "formulaId": "formula1_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "WARNING", "result": 0.0010002000000000001, "issues": ["小数位数过多，可能存在精度问题"], "error": null}, {"testSet": "极小值测试", "formulaId": "formula1_2", "formulaName": "基础加乘公式", "expression": "(data1+data2*data4)", "status": "PASS", "result": 0.010002, "issues": [], "error": null}, {"testSet": "极小值测试", "formulaId": "formula2_1", "formulaName": "复合计算公式", "expression": "(data1+data2*data4)*data3", "status": "WARNING", "result": 0.0010002000000000001, "issues": ["小数位数过多，可能存在精度问题"], "error": null}, {"testSet": "极小值测试", "formulaId": "formula2_2", "formulaName": "多重乘积公式", "expression": "(data1+data2*data4)*data3*data5", "status": "WARNING", "result": 5.001000000000001e-05, "issues": ["小数位数过多，可能存在精度问题"], "error": null}, {"testSet": "极小值测试", "formulaId": "formula6_1", "formulaName": "直接取值", "expression": "data1", "status": "PASS", "result": 0.01, "issues": [], "error": null}, {"testSet": "极小值测试", "formulaId": "formula6_2", "formulaName": "简单乘积", "expression": "data2*data4", "status": "PASS", "result": 2e-06, "issues": [], "error": null}], "riskAnalysis": {}, "performanceMetrics": {"正常业务数据": 32, "小数值测试": 8, "大数值测试": 6, "零值测试": 8, "负数测试": 5, "极小值测试": 8}, "conclusion": "所有公式计算功能正常"}