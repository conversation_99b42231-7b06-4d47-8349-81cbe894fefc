// Redis配置和连接管理
// 用于解决React状态管理问题

import Redis from 'ioredis';

// Redis云服务连接配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'redis-11161.c263.us-east-1-2.ec2.redns.redis-cloud.com',
  port: process.env.REDIS_PORT || 11161,
  username: process.env.REDIS_USERNAME || 'default',
  password: process.env.REDIS_PASSWORD || 'jwIV9xvBCigMpQLZiD6JWs9mtsmNHVNM',
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 15000, // 云服务需要更长的连接时间
  commandTimeout: 8000,  // 云服务需要更长的命令超时
  enableReadyCheck: true,
  maxLoadingTimeout: 5000,
  // 云服务通常需要TLS
  family: 4, // 强制使用IPv4
};

// 创建Redis实例
let redis = null;
let memoryStore = new Map(); // 临时内存存储，等Redis云服务配置完成后移除

// 获取Redis连接
export const getRedisClient = () => {
  if (!redis) {
    try {
      redis = new Redis(redisConfig);

      // 连接事件监听
      redis.on('connect', () => {
        console.log('✅ Redis云服务连接成功');
      });

      redis.on('error', (error) => {
        console.error('❌ Redis云服务连接错误:', error);
        console.log('⚠️ 切换到内存存储模式');
        redis = null; // 重置连接
      });

      redis.on('close', () => {
        console.log('⚠️ Redis云服务连接关闭');
      });

      redis.on('ready', () => {
        console.log('🚀 Redis云服务准备就绪');
      });

    } catch (error) {
      console.error('❌ Redis初始化失败:', error);
      redis = null;
    }
  }

  return redis;
};

// 状态管理相关的Redis操作
export class StateManager {
  constructor() {
    this.redis = getRedisClient();
    this.keyPrefix = 'financial_system:';
    this.memoryStore = memoryStore; // 备选内存存储
  }

  // 检查Redis是否可用
  async isRedisAvailable() {
    try {
      if (!this.redis) return false;
      await this.redis.ping();
      return true;
    } catch (error) {
      return false;
    }
  }
  
  // 生成Redis键名
  getKey(type, id) {
    return `${this.keyPrefix}${type}:${id}`;
  }
  
  // 保存公式配置状态
  async saveFormulaState(agentId, formulas) {
    try {
      const key = this.getKey('formulas', agentId);
      const value = JSON.stringify({
        formulas,
        timestamp: Date.now(),
        version: 1
      });

      // 尝试使用Redis云服务
      if (await this.isRedisAvailable()) {
        await this.redis.setex(key, 3600, value); // 1小时过期
        console.log('✅ 公式状态已保存到Redis云服务:', key);
        return true;
      } else {
        // 备选方案：使用内存存储
        this.memoryStore.set(key, {
          value,
          expiry: Date.now() + 3600000 // 1小时后过期
        });
        console.log('✅ 公式状态已保存到内存存储:', key);
        return true;
      }
    } catch (error) {
      console.error('❌ 保存公式状态失败:', error);
      // 最后的备选方案：内存存储
      try {
        const key = this.getKey('formulas', agentId);
        const value = JSON.stringify({ formulas, timestamp: Date.now(), version: 1 });
        this.memoryStore.set(key, { value, expiry: Date.now() + 3600000 });
        console.log('⚠️ 使用内存存储作为备选方案:', key);
        return true;
      } catch (memError) {
        console.error('❌ 内存存储也失败:', memError);
        return false;
      }
    }
  }
  
  // 获取公式配置状态
  async getFormulaState(agentId) {
    try {
      const key = this.getKey('formulas', agentId);

      // 尝试从Redis云服务获取
      if (await this.isRedisAvailable()) {
        const value = await this.redis.get(key);
        if (value) {
          const data = JSON.parse(value);
          console.log('✅ 从Redis云服务获取公式状态:', key);
          return data.formulas;
        }
      } else {
        // 备选方案：从内存存储获取
        const stored = this.memoryStore.get(key);
        if (stored) {
          // 检查是否过期
          if (Date.now() < stored.expiry) {
            const data = JSON.parse(stored.value);
            console.log('✅ 从内存存储获取公式状态:', key);
            return data.formulas;
          } else {
            // 已过期，删除
            this.memoryStore.delete(key);
            console.log('⚠️ 内存存储中的数据已过期:', key);
          }
        }
      }

      return null;
    } catch (error) {
      console.error('❌ 获取公式状态失败:', error);
      // 尝试从内存存储获取
      try {
        const key = this.getKey('formulas', agentId);
        const stored = this.memoryStore.get(key);
        if (stored && Date.now() < stored.expiry) {
          const data = JSON.parse(stored.value);
          console.log('⚠️ 从内存存储备选方案获取公式状态:', key);
          return data.formulas;
        }
      } catch (memError) {
        console.error('❌ 内存存储备选方案也失败:', memError);
      }
      return null;
    }
  }
  
  // 保存编辑状态
  async saveEditingState(agentId, editingData) {
    try {
      const key = this.getKey('editing', agentId);
      const value = JSON.stringify({
        editingData,
        timestamp: Date.now()
      });
      
      await this.redis.setex(key, 1800, value); // 30分钟过期
      console.log('✅ 编辑状态已保存到Redis:', key);
      return true;
    } catch (error) {
      console.error('❌ 保存编辑状态失败:', error);
      return false;
    }
  }
  
  // 获取编辑状态
  async getEditingState(agentId) {
    try {
      const key = this.getKey('editing', agentId);
      const value = await this.redis.get(key);
      
      if (value) {
        const data = JSON.parse(value);
        console.log('✅ 从Redis获取编辑状态:', key);
        return data.editingData;
      }
      
      return null;
    } catch (error) {
      console.error('❌ 获取编辑状态失败:', error);
      return null;
    }
  }
  
  // 清除状态
  async clearState(agentId, type) {
    try {
      const key = this.getKey(type, agentId);
      await this.redis.del(key);
      console.log('✅ 状态已清除:', key);
      return true;
    } catch (error) {
      console.error('❌ 清除状态失败:', error);
      return false;
    }
  }
  
  // 设置状态锁（防止并发冲突）
  async setLock(agentId, type, ttl = 30) {
    try {
      const key = this.getKey(`lock:${type}`, agentId);
      const result = await this.redis.set(key, Date.now(), 'EX', ttl, 'NX');
      return result === 'OK';
    } catch (error) {
      console.error('❌ 设置锁失败:', error);
      return false;
    }
  }
  
  // 释放状态锁
  async releaseLock(agentId, type) {
    try {
      const key = this.getKey(`lock:${type}`, agentId);
      await this.redis.del(key);
      return true;
    } catch (error) {
      console.error('❌ 释放锁失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const stateManager = new StateManager();

// 优雅关闭Redis连接
export const closeRedis = async () => {
  if (redis) {
    await redis.quit();
    redis = null;
    console.log('✅ Redis连接已关闭');
  }
};
