'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import { FormulaEngine } from '../utils/FormulaEngine';
import { DataManager } from '../utils/DataManager';

// 创建Context
const FormulaContext = createContext();

// 初始状态
const initialState = {
  formulas: {},
  customers: [],
  calculationResults: {},
  isCalculating: false,
  lastUpdated: null,
  selectedFormula: null
};

// Reducer函数
const formulaReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FORMULAS':
      return { ...state, formulas: action.payload };
    
    case 'UPDATE_FORMULA':
      const updatedFormulas = {
        ...state.formulas,
        [action.payload.id]: action.payload
      };
      return { 
        ...state, 
        formulas: updatedFormulas,
        lastUpdated: Date.now()
      };
    
    case 'DELETE_FORMULA':
      const { [action.payload]: deleted, ...remainingFormulas } = state.formulas;
      return { 
        ...state, 
        formulas: remainingFormulas,
        lastUpdated: Date.now()
      };
    
    case 'SET_CUSTOMERS':
      return { ...state, customers: action.payload };
    
    case 'ADD_CUSTOMER':
      return { 
        ...state, 
        customers: [...state.customers, action.payload],
        lastUpdated: Date.now()
      };
    
    case 'DELETE_CUSTOMER':
      return { 
        ...state, 
        customers: state.customers.filter(c => c.id !== action.payload),
        lastUpdated: Date.now()
      };
    
    case 'UPDATE_CUSTOMER_DATA':
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.customerId
            ? { ...customer, data: { ...customer.data, ...action.payload.data } }
            : customer
        ),
        lastUpdated: Date.now()
      };
    
    case 'SET_CALCULATION_RESULTS':
      return { ...state, calculationResults: action.payload };
    
    case 'SET_CALCULATING':
      return { ...state, isCalculating: action.payload };
    
    case 'SET_SELECTED_FORMULA':
      return { ...state, selectedFormula: action.payload };
    
    default:
      return state;
  }
};

// Provider组件
export const FormulaProvider = ({ children, initialCustomers, onCustomersChange }) => {
  const [state, dispatch] = useReducer(formulaReducer, initialState);

  // 加载初始数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const savedFormulas = DataManager.loadFormulas();
        const savedCustomers = initialCustomers || DataManager.loadCustomers();
        
        dispatch({ type: 'SET_FORMULAS', payload: savedFormulas });
        dispatch({ type: 'SET_CUSTOMERS', payload: savedCustomers });
        
        console.log('✅ 动态公式系统数据加载完成');
      } catch (error) {
        console.error('❌ 数据加载失败:', error);
        message.error('数据加载失败');
      }
    };
    
    loadData();
  }, [initialCustomers]);

  // 自动计算所有结果
  useEffect(() => {
    if (state.lastUpdated && Object.keys(state.formulas).length > 0 && state.customers.length > 0) {
      calculateAllResults();
    }
  }, [state.formulas, state.customers, state.lastUpdated]);

  // 自动保存数据
  useEffect(() => {
    if (state.lastUpdated) {
      DataManager.saveFormulas(state.formulas);
      DataManager.saveCustomers(state.customers);
      
      // 通知父组件客户数据变更
      if (onCustomersChange) {
        onCustomersChange(state.customers);
      }
    }
  }, [state.formulas, state.customers, state.lastUpdated, onCustomersChange]);

  // 计算所有结果
  const calculateAllResults = async () => {
    dispatch({ type: 'SET_CALCULATING', payload: true });
    
    try {
      const results = {};
      
      // 为每个客户计算所有公式
      for (const customer of state.customers) {
        results[customer.id] = {};
        
        for (const [formulaId, formula] of Object.entries(state.formulas)) {
          if (formula.isValid && formula.expression) {
            const calculation = FormulaEngine.calculateExpression(
              formula.expression, 
              customer.data
            );
            results[customer.id][formulaId] = calculation;
          } else {
            results[customer.id][formulaId] = {
              success: false,
              error: '公式无效',
              result: 0
            };
          }
        }
      }
      
      dispatch({ type: 'SET_CALCULATION_RESULTS', payload: results });
      console.log('🧮 所有公式计算完成');
    } catch (error) {
      console.error('❌ 计算错误:', error);
      message.error('计算过程中出现错误');
    } finally {
      dispatch({ type: 'SET_CALCULATING', payload: false });
    }
  };

  // 更新公式
  const updateFormula = (formula) => {
    try {
      // 验证公式
      if (formula.expression) {
        const validation = FormulaEngine.validateExpression(formula.expression);
        formula.isValid = validation.isValid;
        
        if (!validation.isValid) {
          message.warning(`公式 ${formula.name} 语法错误: ${validation.error}`);
        } else {
          message.success(`公式 ${formula.name} 更新成功`);
        }
      }
      
      dispatch({ type: 'UPDATE_FORMULA', payload: formula });
    } catch (error) {
      console.error('❌ 更新公式失败:', error);
      message.error('更新公式失败');
    }
  };

  // 删除公式
  const deleteFormula = (formulaId) => {
    try {
      dispatch({ type: 'DELETE_FORMULA', payload: formulaId });
      message.success('公式删除成功');
    } catch (error) {
      console.error('❌ 删除公式失败:', error);
      message.error('删除公式失败');
    }
  };

  // 添加客户
  const addCustomer = (customerName) => {
    try {
      const newCustomer = DataManager.createNewCustomer(customerName);
      dispatch({ type: 'ADD_CUSTOMER', payload: newCustomer });
      message.success(`客户 ${customerName} 添加成功`);
      return newCustomer;
    } catch (error) {
      console.error('❌ 添加客户失败:', error);
      message.error('添加客户失败');
      return null;
    }
  };

  // 删除客户
  const deleteCustomer = (customerId) => {
    try {
      const customer = state.customers.find(c => c.id === customerId);
      dispatch({ type: 'DELETE_CUSTOMER', payload: customerId });
      message.success(`客户 ${customer?.name || ''} 删除成功`);
    } catch (error) {
      console.error('❌ 删除客户失败:', error);
      message.error('删除客户失败');
    }
  };

  // 更新客户数据
  const updateCustomerData = (customerId, data) => {
    try {
      // 验证数据
      const validation = FormulaEngine.validateData(data);
      if (!validation.isValid) {
        message.error(`数据验证失败: ${validation.errors.join(', ')}`);
        return;
      }
      
      dispatch({ 
        type: 'UPDATE_CUSTOMER_DATA', 
        payload: { customerId, data } 
      });
    } catch (error) {
      console.error('❌ 更新客户数据失败:', error);
      message.error('更新客户数据失败');
    }
  };

  // 保存植入功能
  const savePlant = async () => {
    try {
      console.log('🌱 动态公式系统 - 保存植入功能执行');
      console.log('📊 当前公式配置:', state.formulas);
      console.log('👥 当前客户数据:', state.customers);
      console.log('🧮 计算结果:', state.calculationResults);
      
      // 执行保存逻辑
      const formulaSaved = DataManager.saveFormulas(state.formulas);
      const customerSaved = DataManager.saveCustomers(state.customers);
      
      if (formulaSaved && customerSaved) {
        message.success('保存植入成功！所有数据已保存');
        return true;
      } else {
        message.error('保存植入失败！');
        return false;
      }
    } catch (error) {
      console.error('❌ 保存植入失败:', error);
      message.error('保存植入失败！');
      return false;
    }
  };

  // 获取统计信息
  const getStats = () => {
    return FormulaEngine.calculatePerformanceStats(state.formulas, state.customers);
  };

  // Context值
  const contextValue = {
    ...state,
    updateFormula,
    deleteFormula,
    addCustomer,
    deleteCustomer,
    updateCustomerData,
    calculateAllResults,
    savePlant,
    getStats
  };

  return (
    <FormulaContext.Provider value={contextValue}>
      {children}
    </FormulaContext.Provider>
  );
};

// Hook
export const useFormula = () => {
  const context = useContext(FormulaContext);
  if (!context) {
    throw new Error('useFormula must be used within a FormulaProvider');
  }
  return context;
};

export default FormulaContext;
