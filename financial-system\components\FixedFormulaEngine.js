// 固定公式引擎组件
// 基于老板真实业务需求的固定公式结构，支持参数配置

import React, { useState, useEffect } from 'react';
import { Card, Select, InputNumber, Button, Typography, Space, Divider, message } from 'antd';
import { SettingOutlined, CalculatorOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

const FixedFormulaEngine = ({ 
  moduleConfig, 
  customerData, 
  onCalculationComplete,
  onFormulaParametersChange 
}) => {
  const [formulaParameters, setFormulaParameters] = useState({});
  const [calculationResults, setCalculationResults] = useState({});
  const [isConfiguring, setIsConfiguring] = useState(false);

  // 初始化公式参数
  useEffect(() => {
    if (moduleConfig?.fixedFormulas) {
      const initialParams = {};
      Object.values(moduleConfig.fixedFormulas).forEach(formula => {
        initialParams[formula.id] = { ...formula.parameters };
      });
      setFormulaParameters(initialParams);
    }
  }, [moduleConfig]);

  // 固定公式计算逻辑 - 修复版本
  const calculateFormula = (formulaId, parameters, data, currentResults = {}) => {
    try {
      console.log(`计算公式 ${formulaId}:`, { parameters, data, currentResults });

      switch (formulaId) {
        case 'income':
          // 收入 = 数量 × 单价 (如: 25656 × 3.22 = 82612.32)
          const incomeQuantity = parseFloat(data[parameters.quantityField]) || 0;
          const incomePrice = parseFloat(data[parameters.priceField]) || 0;
          const incomeResult = incomeQuantity * incomePrice;
          console.log(`收入计算: ${incomeQuantity} × ${incomePrice} = ${incomeResult}`);
          return incomeResult;

        case 'expense':
          // 付出 = 数量 × 单价 × (-1) (如: 5444 × 5.6 × (-1) = -30486.4)
          const expenseQuantity = parseFloat(data[parameters.quantityField]) || 0;
          const expensePrice = parseFloat(data[parameters.priceField]) || 0;
          const expenseResult = expenseQuantity * expensePrice * (-1);
          console.log(`付出计算: ${expenseQuantity} × ${expensePrice} × (-1) = ${expenseResult}`);
          return expenseResult;

        case 'balance':
          // 余额 = 上期余额 + 收入 - 付出 - 减免
          const previousBalance = parseFloat(parameters.previousBalance) || 0;
          const deduction = parseFloat(parameters.deduction) || 0;
          const income = currentResults.income || 0;
          const expense = currentResults.expense || 0;

          // 注意：expense已经是负数，所以这里用加法
          const balanceResult = previousBalance + income + expense - deduction;
          console.log(`余额计算: ${previousBalance} + ${income} + ${expense} - ${deduction} = ${balanceResult}`);
          return balanceResult;

        case 'finalSummary':
          // 最终汇总 = 收入 + 付出 + 余额 + 其他结果字段
          const finalIncome = currentResults.income || 0;
          const finalExpense = currentResults.expense || 0;
          const finalBalance = currentResults.balance || 0;

          // 加上其他结果字段的值
          const otherResults = (parseFloat(data.result1) || 0) +
                              (parseFloat(data.result2) || 0) +
                              (parseFloat(data.result3) || 0) +
                              (parseFloat(data.result4) || 0) +
                              (parseFloat(data.result5) || 0);

          const rawTotal = finalIncome + finalExpense + finalBalance + otherResults;
          const finalResult = Math.round(rawTotal); // 四舍五入到整数

          console.log(`最终汇总计算: ${finalIncome} + ${finalExpense} + ${finalBalance} + ${otherResults} = ${rawTotal} → ${finalResult}`);
          return finalResult;

        default:
          return 0;
      }
    } catch (error) {
      console.error(`公式计算错误 ${formulaId}:`, error);
      return 0;
    }
  };

  // 获取最新的DOM数据作为备用
  const getLatestDOMData = () => {
    const tableInputs = document.querySelectorAll('table input[type="number"]');
    const domData = {};

    tableInputs.forEach((input, index) => {
      if (index < 7) {
        const fieldKey = `data${index + 1}`;
        domData[fieldKey] = parseFloat(input.value) || 0;
      }
    });

    console.log('DOM数据获取:', domData);
    return domData;
  };

  // 执行所有公式计算 - 修复版本
  const executeAllCalculations = () => {
    if (!moduleConfig?.fixedFormulas) {
      message.error('缺少公式配置');
      return;
    }

    // 获取DOM数据作为备用
    const domData = getLatestDOMData();

    // 合并React状态数据和DOM数据，优先使用DOM数据
    const mergedData = { ...customerData, ...domData };

    console.log('开始执行公式计算:');
    console.log('React状态数据:', customerData);
    console.log('DOM数据:', domData);
    console.log('合并后数据:', mergedData);
    console.log('公式参数:', formulaParameters);

    const results = {};
    const formulas = moduleConfig.fixedFormulas;

    // 按依赖顺序计算公式，每次计算都传递当前结果
    const calculationOrder = ['income', 'expense', 'balance', 'finalSummary'];

    calculationOrder.forEach(formulaId => {
      if (formulas[formulaId] && formulaParameters[formulaId]) {
        const result = calculateFormula(
          formulaId,
          formulaParameters[formulaId],
          mergedData, // 使用合并后的数据
          results // 传递当前已计算的结果
        );
        results[formulaId] = result;

        console.log(`公式 ${formulaId} 计算完成:`, result);
      }
    });

    console.log('所有公式计算完成:', results);

    // 强制状态更新
    setCalculationResults({});
    setTimeout(() => {
      setCalculationResults(results);
      console.log('状态更新完成，calculationResults:', results);
    }, 100);

    // 通知父组件计算完成
    if (onCalculationComplete) {
      onCalculationComplete(results);
    }

    message.success(`公式计算完成！最终汇总: ${results.finalSummary?.toLocaleString() || 0}`);
  };

  // 更新公式参数
  const updateFormulaParameter = (formulaId, paramKey, value) => {
    setFormulaParameters(prev => ({
      ...prev,
      [formulaId]: {
        ...prev[formulaId],
        [paramKey]: value
      }
    }));

    // 通知父组件参数变更
    if (onFormulaParametersChange) {
      onFormulaParametersChange(formulaId, paramKey, value);
    }
  };

  // 获取可用的数据字段选项
  const getDataFieldOptions = () => {
    if (!moduleConfig?.fields) return [];
    
    return Object.entries(moduleConfig.fields)
      .filter(([key, field]) => field.type === 'number' && field.editable)
      .map(([key, field]) => ({
        value: key,
        label: `${field.title} (${key})`
      }));
  };

  // 渲染公式配置界面
  const renderFormulaConfig = (formulaId, formula) => {
    const params = formulaParameters[formulaId] || {};
    const result = calculationResults[formulaId] || 0;
    const dataFieldOptions = getDataFieldOptions();

    return (
      <Card 
        key={formulaId}
        size="small" 
        title={
          <Space>
            <CalculatorOutlined />
            <Text strong>{formula.name}</Text>
            <Text type="secondary">= {result.toLocaleString()}</Text>
          </Space>
        }
        extra={
          <Button 
            size="small" 
            icon={<SettingOutlined />}
            onClick={() => setIsConfiguring(!isConfiguring)}
          >
            配置
          </Button>
        }
        style={{ marginBottom: 8 }}
      >
        <div style={{ marginBottom: 8 }}>
          <Text type="secondary">{formula.description}</Text>
        </div>
        
        {isConfiguring && (
          <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
            {formulaId === 'income' || formulaId === 'expense' ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>数量字段：</Text>
                  <Select
                    style={{ width: 200, marginLeft: 8 }}
                    value={params.quantityField}
                    onChange={(value) => updateFormulaParameter(formulaId, 'quantityField', value)}
                    placeholder="选择数量字段"
                  >
                    {dataFieldOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </div>
                <div>
                  <Text>单价字段：</Text>
                  <Select
                    style={{ width: 200, marginLeft: 8 }}
                    value={params.priceField}
                    onChange={(value) => updateFormulaParameter(formulaId, 'priceField', value)}
                    placeholder="选择单价字段"
                  >
                    {dataFieldOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </div>
              </Space>
            ) : formulaId === 'balance' ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text>上期余额：</Text>
                  <InputNumber
                    style={{ width: 150, marginLeft: 8 }}
                    value={params.previousBalance}
                    onChange={(value) => updateFormulaParameter(formulaId, 'previousBalance', value)}
                    placeholder="输入上期余额"
                  />
                </div>
                <div>
                  <Text>减免金额：</Text>
                  <InputNumber
                    style={{ width: 150, marginLeft: 8 }}
                    value={params.deduction}
                    onChange={(value) => updateFormulaParameter(formulaId, 'deduction', value)}
                    placeholder="输入减免金额"
                  />
                </div>
              </Space>
            ) : (
              <Text type="secondary">此公式基于其他公式结果自动计算</Text>
            )}
          </div>
        )}
      </Card>
    );
  };

  if (!moduleConfig?.fixedFormulas) {
    return (
      <Card>
        <Text type="secondary">未配置固定公式</Text>
      </Card>
    );
  }

  return (
    <div style={{ marginBottom: 16 }}>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4} style={{ margin: 0 }}>
          固定公式计算引擎
        </Title>
        <Button 
          type="primary" 
          icon={<CalculatorOutlined />}
          onClick={executeAllCalculations}
        >
          执行计算
        </Button>
      </div>

      <Divider />

      <div>
        {Object.entries(moduleConfig.fixedFormulas).map(([formulaId, formula]) =>
          renderFormulaConfig(formulaId, formula)
        )}
      </div>

      {Object.keys(calculationResults).length > 0 && (
        <>
          <Divider />
          <Card size="small" title="计算结果汇总">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(calculationResults).map(([formulaId, result]) => {
                const formula = moduleConfig.fixedFormulas[formulaId];
                return (
                  <div key={formulaId} style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Text>{formula?.name || formulaId}：</Text>
                    <Text strong style={{ color: result >= 0 ? '#52c41a' : '#ff4d4f' }}>
                      {result.toLocaleString()}
                    </Text>
                  </div>
                );
              })}
              <Divider />
              <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '16px' }}>
                <Text strong>最终汇总：</Text>
                <Text strong style={{ 
                  color: calculationResults.finalSummary >= 0 ? '#52c41a' : '#ff4d4f',
                  fontSize: '18px'
                }}>
                  {(calculationResults.finalSummary || 0).toLocaleString()}
                </Text>
              </div>
            </Space>
          </Card>
        </>
      )}
    </div>
  );
};

export default FixedFormulaEngine;
