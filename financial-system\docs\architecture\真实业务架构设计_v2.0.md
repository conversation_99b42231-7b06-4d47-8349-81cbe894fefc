# 财务管理系统 - 真实业务架构设计 v2.0

## 1. 架构概览

### 1.1 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   主汇总表页面   │    │  客户详细表页面  │    │   周/月报表页面  │
│                │    │                │    │                │
│ 客户1: 300     │◄───┤ 数据录入+计算   │───►│ 统计分析显示    │
│ 客户2: 17400   │    │ 最终结果: 327000│    │ 颜色分类展示    │
│ 小李: 327000   │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据同步引擎   │
                    │                │
                    │ • 字段名称同步  │
                    │ • 计算结果更新  │
                    │ • 跨表数据流转  │
                    └─────────────────┘
```

### 1.2 核心组件重新设计

#### 1.2.1 客户详细表组件 (CustomerDetailTable)
```javascript
// 支持真实业务的数据结构
{
  customerId: "小李",
  inputData: {
    data1: 25656,    // 可自定义名称为"收入数量"
    data2: 3.22,     // 可自定义名称为"收入单价"  
    data6: -5444,    // 可自定义名称为"付出数量"
    data7: 5.6       // 可自定义名称为"付出单价"
  },
  calculatedResults: {
    result1: 25659.22,  // 收入计算结果
    result2: -5438.4,   // 付出计算结果
    result3: 500        // 上周余额
  },
  finalSummary: 327000  // 最终汇总值
}
```

#### 1.2.2 固定公式引擎 (FixedFormulaEngine)
```javascript
// 固定公式结构，但参数可配置
const FIXED_FORMULAS = {
  income: {
    name: "收入计算",
    structure: "(quantityField) * (priceField)",
    parameters: {
      quantityField: "data1",  // 可配置为任意输入字段
      priceField: "data2"      // 可配置为任意输入字段
    }
  },
  expense: {
    name: "付出计算", 
    structure: "(quantityField) * (priceField) * (-1)",
    parameters: {
      quantityField: "data6",
      priceField: "data7"
    }
  },
  balance: {
    name: "余额计算",
    structure: "(previousBalance) + (income) - (expense) - (deduction)",
    parameters: {
      previousBalance: 500,    // 固定值
      deduction: 1000         // 固定值
    }
  }
}
```

## 2. API接口重新设计

### 2.1 客户数据更新API
```javascript
// PUT /api/customers/[customerId]/update
{
  inputData: { data1: 25656, data2: 3.22, ... },
  calculatedResults: { result1: 25659.22, ... },
  finalSummary: 327000,
  updateMainSummary: true  // 是否更新主汇总表
}
```

### 2.2 主汇总表更新API  
```javascript
// PUT /api/main-summary/[customerId]
{
  customerId: "小李",
  summaryValue: 327000,
  updateTimestamp: "2025-07-30T10:00:00Z"
}
```

### 2.3 公式参数配置API
```javascript
// PUT /api/formulas/[formulaId]/parameters
{
  formulaId: "income",
  parameters: {
    quantityField: "data1",  // 用户可选择任意字段
    priceField: "data2"
  }
}
```

### 2.4 字段名称同步API
```javascript
// PUT /api/field-names/sync
{
  fieldId: "data1",
  newName: "收入数量",
  syncTargets: [
    "customerDetailTables",
    "mainSummaryTable", 
    "weeklyReports",
    "monthlyReports",
    "formulaEditor"
  ]
}
```

## 3. 数据库架构调整

### 3.1 客户数据表 (customers)
```sql
CREATE TABLE customers (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100),
  input_data JSON,           -- 存储所有输入数据
  calculated_results JSON,   -- 存储计算结果
  final_summary DECIMAL(15,2), -- 最终汇总值
  last_updated TIMESTAMP
);
```

### 3.2 字段名称映射表 (field_mappings)
```sql
CREATE TABLE field_mappings (
  field_id VARCHAR(50) PRIMARY KEY,
  display_name VARCHAR(100),
  field_type ENUM('input', 'calculated', 'formula'),
  module_id VARCHAR(50),
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### 3.3 公式配置表 (formula_configs)
```sql
CREATE TABLE formula_configs (
  formula_id VARCHAR(50) PRIMARY KEY,
  formula_name VARCHAR(100),
  formula_structure TEXT,
  parameters JSON,
  module_id VARCHAR(50),
  updated_at TIMESTAMP
);
```

### 3.4 主汇总表 (main_summary)
```sql
CREATE TABLE main_summary (
  customer_id VARCHAR(50) PRIMARY KEY,
  customer_name VARCHAR(100),
  summary_value DECIMAL(15,2),
  last_calculated TIMESTAMP,
  data_source VARCHAR(100)  -- 数据来源追踪
);
```

## 4. 组件交互流程

### 4.1 数据录入和计算流程
```
1. 用户在客户详细表输入数据 (data1=25656, data2=3.22)
2. 触发固定公式计算 (收入 = 25656 * 3.22 = 25659.22)
3. 计算所有结果并生成最终汇总值 (327000)
4. 调用主汇总表更新API
5. 更新主汇总表中对应客户行
6. 触发周/月报表重新计算
7. 显示更新成功提示
```

### 4.2 字段名称修改流程
```
1. 用户双击字段名称 (如"数据1")
2. 进入编辑模式，输入新名称 ("收入数量")
3. 调用字段名称同步API
4. 同步更新到所有相关表格和组件
5. 更新公式编辑器中的变量选择器
6. 显示同步完成状态
```

### 4.3 公式参数配置流程
```
1. 用户点击公式名称进入配置模式
2. 显示当前公式结构和参数配置
3. 用户选择新的参数字段
4. 实时预览计算结果
5. 保存配置并重新计算
6. 更新相关的计算结果
```

## 5. 性能优化策略

### 5.1 计算优化
- **增量计算**: 只重新计算变更的部分
- **缓存机制**: 缓存复杂计算结果
- **异步处理**: 大量数据更新使用后台任务

### 5.2 同步优化
- **批量更新**: 多个字段名称修改合并处理
- **事务保证**: 确保跨表更新的一致性
- **进度提示**: 长时间操作显示进度条

### 5.3 数据库优化
- **索引优化**: 为常用查询字段添加索引
- **分区策略**: 按客户或时间分区存储
- **连接池**: 优化数据库连接管理

## 6. 安全性设计

### 6.1 数据验证
- **输入验证**: 严格验证所有用户输入
- **计算验证**: 验证公式计算结果的合理性
- **权限控制**: 不同用户的操作权限控制

### 6.2 数据完整性
- **事务处理**: 确保数据更新的原子性
- **备份机制**: 重要操作前自动备份
- **回滚功能**: 支持错误操作的回滚

## 7. 监控和日志

### 7.1 操作日志
- 记录所有数据修改操作
- 记录字段名称变更历史
- 记录公式配置变更

### 7.2 性能监控
- API响应时间监控
- 数据库查询性能监控
- 用户操作行为分析

## 8. 部署架构

### 8.1 开发环境
- Next.js 14 开发服务器
- 本地SQLite数据库
- 热重载和实时调试

### 8.2 生产环境建议
- Next.js应用部署到Vercel或类似平台
- 数据库使用PostgreSQL或MySQL
- Redis缓存层
- CDN加速静态资源

---

**Bob完成时间**: 2025年7月30日  
**架构状态**: 已重新设计，支持真实业务流程  
**下一步**: 提交给Alex进行具体实现
