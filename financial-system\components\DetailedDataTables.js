'use client';

import React, { useState } from 'react';
import { Card, Table, InputNumber, Button, Space, Tooltip } from 'antd';
import { EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { useFormula } from '../contexts/FormulaContext';
import { FormulaEngine } from '../utils/FormulaEngine';

// 颜色编码单元格组件
const ColorCodedCell = ({ value, type, editable = false, onSave, customer, field }) => {
  const [editing, setEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);

  const getBackgroundColor = (type) => {
    const colorMap = {
      'customer': '#FFFFFF',      // 白色 - 客户名称
      'input': '#FFFFFF',         // 白色 - 输入数据
      'calculation': '#87CEEB',   // 蓝色 - 计算数据
      'result': '#FFA500',        // 橙色 - 结果数据
      'result5': '#FF6347',       // 红橙色 - 结果5数据
      'summary': '#90EE90',       // 绿色 - 汇总数据
      'formula': '#FFD700'        // 黄色 - 公式
    };
    return colorMap[type] || '#FFFFFF';
  };

  const cellStyle = {
    backgroundColor: getBackgroundColor(type),
    border: '1px solid #D3D3D3',
    padding: '4px 8px',
    textAlign: type === 'customer' ? 'left' : 'right',
    minHeight: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: type === 'customer' ? 'flex-start' : 'flex-end',
    borderRadius: '4px'
  };

  const handleSave = () => {
    if (onSave && customer && field) {
      onSave(customer.id, { [field]: currentValue });
    }
    setEditing(false);
  };

  const handleCancel = () => {
    setCurrentValue(value);
    setEditing(false);
  };

  if (editable && editing) {
    return (
      <div style={{ ...cellStyle, padding: '2px' }}>
        <InputNumber
          value={currentValue}
          onChange={setCurrentValue}
          onPressEnter={handleSave}
          size="small"
          style={{ width: '100%' }}
          controls={false}
        />
        <Space style={{ marginLeft: '4px' }}>
          <Button 
            type="text" 
            size="small" 
            icon={<SaveOutlined />}
            onClick={handleSave}
            style={{ color: '#52c41a' }}
          />
          <Button 
            type="text" 
            size="small" 
            icon={<CloseOutlined />}
            onClick={handleCancel}
            style={{ color: '#ff4d4f' }}
          />
        </Space>
      </div>
    );
  }

  return (
    <div 
      style={cellStyle}
      onClick={() => editable && setEditing(true)}
      className={editable ? 'editable-cell' : ''}
    >
      <span style={{ flex: 1 }}>
        {typeof value === 'number' ? FormulaEngine.formatResult(value) : value}
      </span>
      {editable && (
        <EditOutlined 
          style={{ 
            marginLeft: '4px', 
            color: '#1890ff', 
            fontSize: '12px',
            opacity: 0.6
          }} 
        />
      )}
    </div>
  );
};

// 详细数据表格组件
const DetailedDataTables = () => {
  const { customers, formulas, calculationResults, updateCustomerData, isCalculating } = useFormula();

  // 处理客户数据更新
  const handleCustomerDataUpdate = (customerId, data) => {
    updateCustomerData(customerId, data);
  };

  // 获取公式计算结果
  const getFormulaResult = (customerId, formulaId) => {
    const result = calculationResults[customerId]?.[formulaId];
    if (result && result.success) {
      return result.result;
    }
    return 0;
  };

  // 生成表格列定义
  const generateColumns = () => {
    return [
      {
        title: '客户',
        dataIndex: 'name',
        key: 'name',
        width: 100,
        fixed: 'left',
        render: (text) => <ColorCodedCell value={text} type="customer" />
      },
      {
        title: '数据1',
        dataIndex: 'data1',
        key: 'data1',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data1"
          />
        )
      },
      {
        title: '数据2',
        dataIndex: 'data2',
        key: 'data2',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data2"
          />
        )
      },
      {
        title: '数据3',
        dataIndex: 'data3',
        key: 'data3',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data3"
          />
        )
      },
      {
        title: '数据4',
        dataIndex: 'data4',
        key: 'data4',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data4"
          />
        )
      },
      {
        title: '数据5',
        dataIndex: 'data5',
        key: 'data5',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data5"
          />
        )
      },
      {
        title: '数据6',
        dataIndex: 'data6',
        key: 'data6',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data6"
          />
        )
      },
      {
        title: '数据7',
        dataIndex: 'data7',
        key: 'data7',
        width: 80,
        render: (value, record) => (
          <ColorCodedCell 
            value={value} 
            type="input" 
            editable={true}
            onSave={handleCustomerDataUpdate}
            customer={record}
            field="data7"
          />
        )
      },
      {
        title: '计算值',
        key: 'calculatedValue',
        width: 100,
        render: (_, record) => {
          // 显示第一个有效公式的计算结果
          const firstValidFormula = Object.keys(formulas).find(id => formulas[id].isValid);
          const result = firstValidFormula ? getFormulaResult(record.id, firstValidFormula) : 0;
          return <ColorCodedCell value={result} type="calculation" />;
        }
      },
      {
        title: '结果',
        key: 'result',
        width: 100,
        render: (_, record) => {
          // 显示第二个有效公式的计算结果
          const validFormulas = Object.keys(formulas).filter(id => formulas[id].isValid);
          const result = validFormulas[1] ? getFormulaResult(record.id, validFormulas[1]) : 0;
          return <ColorCodedCell value={result} type="result" />;
        }
      },
      {
        title: '结果5',
        key: 'result5',
        width: 100,
        render: (_, record) => {
          // 显示第五个有效公式的计算结果
          const validFormulas = Object.keys(formulas).filter(id => formulas[id].isValid);
          const result = validFormulas[4] ? getFormulaResult(record.id, validFormulas[4]) : 0;
          return <ColorCodedCell value={result} type="result5" />;
        }
      }
    ];
  };

  // 准备表格数据
  const tableData = customers.map(customer => ({
    key: customer.id,
    id: customer.id,
    name: customer.name,
    ...customer.data
  }));

  if (customers.length === 0) {
    return (
      <Card title="详细数据表格" style={{ textAlign: 'center', padding: '40px' }}>
        <div style={{ color: '#999', fontSize: '16px' }}>
          暂无客户数据，请先添加客户
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="详细数据表格" 
      className="detailed-data-tables"
      loading={isCalculating}
      extra={
        <Tooltip title="双击数据单元格可以编辑">
          <span style={{ fontSize: '12px', color: '#666' }}>
            💡 双击编辑数据
          </span>
        </Tooltip>
      }
    >
      <Table
        columns={generateColumns()}
        dataSource={tableData}
        pagination={false}
        size="small"
        scroll={{ x: 1000 }}
        bordered
        style={{
          '& .editable-cell': {
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          },
          '& .editable-cell:hover': {
            backgroundColor: '#f0f8ff'
          }
        }}
      />
      
      {/* 表格说明 */}
      <div style={{ 
        marginTop: '16px', 
        padding: '12px', 
        background: '#f6ffed',
        border: '1px solid #b7eb8f',
        borderRadius: '6px',
        fontSize: '12px'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
          颜色说明：
        </div>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px' }}>
          <span>🟦 蓝色 - 计算过程数据</span>
          <span>🟧 橙色 - 结果数据</span>
          <span>🟥 红橙色 - 结果5数据</span>
          <span>⚪ 白色 - 输入数据（可编辑）</span>
        </div>
      </div>
    </Card>
  );
};

export default DetailedDataTables;
