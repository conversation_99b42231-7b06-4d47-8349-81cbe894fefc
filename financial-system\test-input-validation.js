// 测试输入验证功能
// 验证数据3（3位小数）、数据4（8位小数）和负数限制

// 模拟validateNumberInput函数进行测试
function validateNumberInput(value, field = 'default') {
  // 移除所有非数字字符（除了小数点）
  const cleanValue = value.replace(/[^0-9.]/g, '');

  // 确保只有一个小数点
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }

  // 根据字段设置不同的小数位限制
  let maxDecimalPlaces = 2; // 默认2位小数
  if (field === 'data3') {
    maxDecimalPlaces = 3; // 数据3允许3位小数
  } else if (field === 'data4') {
    maxDecimalPlaces = 8; // 数据4允许8位小数
  }

  // 限制小数点后的位数
  if (parts.length === 2 && parts[1].length > maxDecimalPlaces) {
    return parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
  }

  // 确保不以小数点开头
  if (cleanValue.startsWith('.')) {
    return '0' + cleanValue;
  }

  return cleanValue;
}

// 测试用例
const testCases = [
  // 数据1和数据2测试（默认2位小数）
  { input: '123.456', field: 'data1', expected: '123.45', description: '数据1: 超过2位小数应被截断' },
  { input: '123.12', field: 'data1', expected: '123.12', description: '数据1: 2位小数应保持不变' },
  { input: '-123.45', field: 'data1', expected: '123.45', description: '数据1: 负号应被移除' },
  
  // 数据3测试（3位小数）
  { input: '123.4567', field: 'data3', expected: '123.456', description: '数据3: 超过3位小数应被截断' },
  { input: '123.123', field: 'data3', expected: '123.123', description: '数据3: 3位小数应保持不变' },
  { input: '123.12', field: 'data3', expected: '123.12', description: '数据3: 少于3位小数应保持不变' },
  { input: '-123.456', field: 'data3', expected: '123.456', description: '数据3: 负号应被移除' },
  
  // 数据4测试（8位小数）
  { input: '123.123456789', field: 'data4', expected: '123.12345678', description: '数据4: 超过8位小数应被截断' },
  { input: '123.12345678', field: 'data4', expected: '123.12345678', description: '数据4: 8位小数应保持不变' },
  { input: '123.123', field: 'data4', expected: '123.123', description: '数据4: 少于8位小数应保持不变' },
  { input: '-123.12345678', field: 'data4', expected: '123.12345678', description: '数据4: 负号应被移除' },
  
  // 边界情况测试
  { input: '.123', field: 'data3', expected: '0.123', description: '以小数点开头应添加0' },
  { input: '123.', field: 'data1', expected: '123.', description: '以小数点结尾应保持不变' },
  { input: '123..456', field: 'data1', expected: '123.456', description: '多个小数点应合并' },
  { input: 'abc123.45def', field: 'data1', expected: '123.45', description: '非数字字符应被移除' },
  { input: '', field: 'data1', expected: '', description: '空字符串应保持不变' },
  { input: '0', field: 'data1', expected: '0', description: '零值应保持不变' },
  { input: '0.000', field: 'data3', expected: '0.000', description: '零值小数应保持不变' },
];

function runTests() {
  console.log('🧪 开始测试输入验证功能...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    const result = validateNumberInput(testCase.input, testCase.field);
    const passed = result === testCase.expected;
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`  输入: "${testCase.input}" (字段: ${testCase.field})`);
    console.log(`  期望: "${testCase.expected}"`);
    console.log(`  实际: "${result}"`);
    console.log(`  结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`📊 测试总结:`);
  console.log(`  总测试数: ${totalTests}`);
  console.log(`  通过数: ${passedTests}`);
  console.log(`  失败数: ${totalTests - passedTests}`);
  console.log(`  通过率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！输入验证功能正常工作。');
  } else {
    console.log('\n⚠️ 部分测试失败，需要检查验证逻辑。');
  }
}

// 测试特定场景
function testSpecificScenarios() {
  console.log('\n🎯 测试特定使用场景...\n');
  
  const scenarios = [
    { description: '用户输入数据3为0.123', input: '0.123', field: 'data3' },
    { description: '用户输入数据3为1.2345', input: '1.2345', field: 'data3' },
    { description: '用户输入数据4为0.12345678', input: '0.12345678', field: 'data4' },
    { description: '用户输入数据4为1.123456789', input: '1.123456789', field: 'data4' },
    { description: '用户尝试输入负数-123.45', input: '-123.45', field: 'data1' },
    { description: '用户输入包含字母abc123.45def', input: 'abc123.45def', field: 'data2' },
  ];
  
  scenarios.forEach((scenario, index) => {
    const result = validateNumberInput(scenario.input, scenario.field);
    console.log(`场景 ${index + 1}: ${scenario.description}`);
    console.log(`  处理结果: "${result}"`);
    console.log(`  说明: ${getValidationExplanation(scenario.input, scenario.field, result)}\n`);
  });
}

function getValidationExplanation(input, field, result) {
  if (input.includes('-')) {
    return '负号被自动移除，确保只能输入正数';
  }
  
  if (field === 'data3' && input.includes('.') && input.split('.')[1]?.length > 3) {
    return '小数位被限制为3位';
  }
  
  if (field === 'data4' && input.includes('.') && input.split('.')[1]?.length > 8) {
    return '小数位被限制为8位';
  }
  
  if (input.includes('.') && input.split('.')[1]?.length > 2 && !['data3', 'data4'].includes(field)) {
    return '小数位被限制为2位（默认）';
  }
  
  if (/[^0-9.]/.test(input)) {
    return '非数字字符被自动移除';
  }
  
  if (input.startsWith('.')) {
    return '以小数点开头时自动添加0';
  }
  
  return '输入格式正确，无需修改';
}

// 运行所有测试
console.log('🚀 输入验证功能测试\n');
runTests();
testSpecificScenarios();

console.log('📝 使用说明:');
console.log('- 数据1, 数据2, 数据5, 数据6, 数据7: 最多2位小数');
console.log('- 数据3: 最多3位小数');
console.log('- 数据4: 最多8位小数');
console.log('- 所有字段都不允许输入负数');
console.log('- 非数字字符会被自动过滤');
console.log('- 以小数点开头会自动添加0');
