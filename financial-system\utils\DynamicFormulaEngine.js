/**
 * 动态公式计算引擎
 * 支持用户自定义公式的实时计算
 */
export class DynamicFormulaEngine {
  
  /**
   * 计算公式表达式
   * @param {string} expression - 公式表达式，如 "(data1+data2+data4)+data3"
   * @param {object} data - 数据对象，包含 data1, data2, data3 等字段
   * @returns {number} 计算结果
   */
  static calculateExpression(expression, data = {}) {
    if (!expression || typeof expression !== 'string') {
      return 0;
    }

    try {
      // 清理表达式，移除多余空格
      let cleanExpression = expression.trim();
      
      if (!cleanExpression) {
        return 0;
      }

      // 替换数据变量 - 修复负数处理问题
      const dataValues = {
        data1: data.data1 !== undefined ? data.data1 : 0,
        data2: data.data2 !== undefined ? data.data2 : 0,
        data3: data.data3 !== undefined ? data.data3 : 0,
        data4: data.data4 !== undefined ? data.data4 : 0,
        data5: data.data5 !== undefined ? data.data5 : 0,
        data6: data.data6 !== undefined ? data.data6 : 0,
        data7: data.data7 !== undefined ? data.data7 : 0
      };

      // 按照变量名长度排序，避免替换冲突（如data10被data1替换）
      const sortedKeys = Object.keys(dataValues).sort((a, b) => b.length - a.length);

      for (const key of sortedKeys) {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        // 用括号包围数值，确保负数正确处理
        const value = dataValues[key];
        // 特别处理负数，确保正确的括号包围
        if (value < 0) {
          cleanExpression = cleanExpression.replace(regex, `(${value})`);
        } else {
          cleanExpression = cleanExpression.replace(regex, `${value}`);
        }
      }

      // 处理幂运算 (^)
      cleanExpression = this.handlePowerOperations(cleanExpression);

      // 验证表达式安全性
      if (!this.isExpressionSafe(cleanExpression)) {
        console.warn('不安全的表达式:', cleanExpression);
        return 0;
      }

      // 计算结果
      const result = eval(cleanExpression);
      
      // 确保结果是数字
      if (isNaN(result) || !isFinite(result)) {
        return 0;
      }

      // 返回精确结果，保留小数位
      return Math.round(result * 100) / 100;
      
    } catch (error) {
      console.error('公式计算错误:', error, '表达式:', expression);
      return 0;
    }
  }

  /**
   * 处理幂运算
   * 将 a^b 转换为 Math.pow(a, b) - 修复负数支持
   */
  static handlePowerOperations(expression) {
    // 处理幂运算，支持括号、正数和负数
    return expression.replace(/(-?\d+(?:\.\d+)?|\([^)]+\))\s*\^\s*(-?\d+(?:\.\d+)?|\([^)]+\))/g,
      (match, base, exponent) => {
        return `Math.pow((${base}), (${exponent}))`;
      }
    );
  }

  /**
   * 验证表达式安全性
   * 只允许数字、基本运算符和Math函数
   */
  static isExpressionSafe(expression) {
    // 允许的字符：数字、小数点、基本运算符、括号、Math函数、空格
    const safePattern = /^[\d+\-*/().\s,Math.pow]+$/;
    return safePattern.test(expression);
  }

  /**
   * 根据公式名称和数据计算结果
   * @param {string} formulaName - 公式名称，如 "公式1.1"
   * @param {object} formulaExpressions - 公式表达式映射
   * @param {object} data - 数据对象
   * @returns {number} 计算结果
   */
  static calculateByFormulaName(formulaName, formulaExpressions, data) {
    const expression = formulaExpressions[formulaName];
    return this.calculateExpression(expression, data);
  }

  /**
   * 批量计算多个公式
   * @param {array} formulaNames - 公式名称数组
   * @param {object} formulaExpressions - 公式表达式映射
   * @param {object} data - 数据对象
   * @returns {object} 计算结果映射
   */
  static calculateMultiple(formulaNames, formulaExpressions, data) {
    const results = {};
    
    for (const formulaName of formulaNames) {
      results[formulaName] = this.calculateByFormulaName(formulaName, formulaExpressions, data);
    }
    
    return results;
  }

  /**
   * 验证公式表达式语法
   * @param {string} expression - 公式表达式
   * @returns {object} 验证结果 { isValid: boolean, error: string }
   */
  static validateExpression(expression) {
    if (!expression || typeof expression !== 'string') {
      return { isValid: true, error: '' }; // 空表达式视为有效
    }

    try {
      // 基本语法检查
      const trimmed = expression.trim();
      
      if (!trimmed) {
        return { isValid: true, error: '' };
      }

      // 检查是否包含不允许的字符
      const allowedPattern = /^[data\d+\-*/^().\s]+$/;
      if (!allowedPattern.test(trimmed)) {
        return { 
          isValid: false, 
          error: '表达式包含不允许的字符，只能使用 data1-data7、数字和基本运算符' 
        };
      }

      // 检查括号匹配
      let openParens = 0;
      for (const char of trimmed) {
        if (char === '(') openParens++;
        if (char === ')') openParens--;
        if (openParens < 0) {
          return { isValid: false, error: '括号不匹配' };
        }
      }
      
      if (openParens !== 0) {
        return { isValid: false, error: '括号不匹配' };
      }

      // 尝试用测试数据计算
      const testData = {
        data1: 1, data2: 2, data3: 3, data4: 4, 
        data5: 5, data6: 6, data7: 7
      };
      
      this.calculateExpression(trimmed, testData);
      
      return { isValid: true, error: '' };
      
    } catch (error) {
      return { 
        isValid: false, 
        error: `语法错误: ${error.message}` 
      };
    }
  }

  /**
   * 获取表达式中使用的变量
   * @param {string} expression - 公式表达式
   * @returns {array} 使用的变量数组
   */
  static getUsedVariables(expression) {
    if (!expression) return [];
    
    const variables = [];
    const variablePattern = /\bdata[1-7]\b/g;
    let match;
    
    while ((match = variablePattern.exec(expression)) !== null) {
      if (!variables.includes(match[0])) {
        variables.push(match[0]);
      }
    }
    
    return variables.sort();
  }

  /**
   * 格式化表达式显示
   * @param {string} expression - 公式表达式
   * @returns {string} 格式化后的表达式
   */
  static formatExpression(expression) {
    if (!expression) return '';
    
    return expression
      .replace(/\s+/g, ' ') // 规范化空格
      .replace(/\(\s+/g, '(') // 移除左括号后的空格
      .replace(/\s+\)/g, ')') // 移除右括号前的空格
      .trim();
  }
}
