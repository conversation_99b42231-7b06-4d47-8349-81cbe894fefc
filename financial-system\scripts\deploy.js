#!/usr/bin/env node

// 路由侠部署脚本
// 财务管理系统 - JavaScript版本

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始部署财务管理系统到路由侠...\n');

// 检查环境
function checkEnvironment() {
  console.log('📋 检查部署环境...');
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  console.log(`✅ Node.js版本: ${nodeVersion}`);
  
  // 检查npm版本
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    console.log(`✅ npm版本: ${npmVersion}`);
  } catch (error) {
    console.error('❌ npm未安装或不可用');
    process.exit(1);
  }
  
  // 检查必要文件
  const requiredFiles = [
    'package.json',
    'next.config.js',
    'prisma/schema.prisma',
    '.env'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ 缺少必要文件: ${file}`);
      process.exit(1);
    }
  }
  
  console.log('✅ 环境检查通过\n');
}

// 安装依赖
function installDependencies() {
  console.log('📦 安装项目依赖...');
  
  try {
    execSync('npm ci', { stdio: 'inherit' });
    console.log('✅ 依赖安装完成\n');
  } catch (error) {
    console.error('❌ 依赖安装失败');
    process.exit(1);
  }
}

// 生成Prisma客户端
function generatePrismaClient() {
  console.log('🗄️ 生成Prisma客户端...');
  
  try {
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma客户端生成完成\n');
  } catch (error) {
    console.error('❌ Prisma客户端生成失败');
    process.exit(1);
  }
}

// 运行数据库迁移
function runDatabaseMigration() {
  console.log('🗃️ 运行数据库迁移...');
  
  try {
    // 检查是否是生产环境
    if (process.env.NODE_ENV === 'production') {
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
    } else {
      execSync('npx prisma db push', { stdio: 'inherit' });
    }
    console.log('✅ 数据库迁移完成\n');
  } catch (error) {
    console.error('❌ 数据库迁移失败');
    process.exit(1);
  }
}

// 构建项目
function buildProject() {
  console.log('🔨 构建项目...');
  
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ 项目构建完成\n');
  } catch (error) {
    console.error('❌ 项目构建失败');
    process.exit(1);
  }
}

// 创建部署包
function createDeploymentPackage() {
  console.log('📦 创建部署包...');
  
  const deploymentFiles = [
    '.next',
    'public',
    'prisma',
    'package.json',
    'package-lock.json',
    'next.config.js',
    '.env.production'
  ];
  
  // 创建部署目录
  const deployDir = 'deployment';
  if (fs.existsSync(deployDir)) {
    fs.rmSync(deployDir, { recursive: true });
  }
  fs.mkdirSync(deployDir);
  
  // 复制文件
  for (const file of deploymentFiles) {
    if (fs.existsSync(file)) {
      const destPath = path.join(deployDir, file);
      if (fs.statSync(file).isDirectory()) {
        fs.cpSync(file, destPath, { recursive: true });
      } else {
        fs.copyFileSync(file, destPath);
      }
      console.log(`✅ 复制: ${file}`);
    }
  }
  
  console.log('✅ 部署包创建完成\n');
}

// 生成路由侠配置
function generateRouterConfig() {
  console.log('⚙️ 生成路由侠配置...');
  
  const config = {
    name: '财务管理系统',
    type: 'nodejs',
    version: '18',
    buildCommand: 'npm ci && npm run build',
    startCommand: 'npm start',
    port: 3000,
    env: {
      NODE_ENV: 'production',
      DATABASE_URL: 'file:./prod.db'
    },
    routes: [
      {
        src: '/(.*)',
        dest: '/$1'
      }
    ],
    headers: [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          }
        ]
      }
    ]
  };
  
  fs.writeFileSync('deployment/router.json', JSON.stringify(config, null, 2));
  console.log('✅ 路由侠配置生成完成\n');
}

// 健康检查
function healthCheck() {
  console.log('🏥 执行健康检查...');
  
  try {
    // 检查构建产物
    if (!fs.existsSync('.next/BUILD_ID')) {
      throw new Error('构建产物不完整');
    }
    
    // 检查数据库文件
    if (!fs.existsSync('prisma/schema.prisma')) {
      throw new Error('数据库配置文件缺失');
    }
    
    console.log('✅ 健康检查通过\n');
  } catch (error) {
    console.error(`❌ 健康检查失败: ${error.message}`);
    process.exit(1);
  }
}

// 显示部署信息
function showDeploymentInfo() {
  console.log('📋 部署信息:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('🎯 项目名称: 财务管理系统');
  console.log('🏷️ 版本: 1.0.0');
  console.log('🔧 技术栈: Next.js 14 + React 18 + Ant Design 5');
  console.log('🗄️ 数据库: SQLite + Prisma ORM');
  console.log('📦 部署包: ./deployment/');
  console.log('⚙️ 配置文件: ./deployment/router.json');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('\n🎉 部署准备完成！');
  console.log('\n📝 下一步操作:');
  console.log('1. 将 deployment 目录上传到路由侠');
  console.log('2. 使用 router.json 配置文件');
  console.log('3. 启动服务并验证功能');
  console.log('\n🔗 访问地址: https://your-domain.com');
}

// 主函数
function main() {
  try {
    checkEnvironment();
    installDependencies();
    generatePrismaClient();
    runDatabaseMigration();
    buildProject();
    createDeploymentPackage();
    generateRouterConfig();
    healthCheck();
    showDeploymentInfo();
  } catch (error) {
    console.error(`\n❌ 部署失败: ${error.message}`);
    process.exit(1);
  }
}

// 执行部署
if (require.main === module) {
  main();
}

module.exports = {
  checkEnvironment,
  installDependencies,
  generatePrismaClient,
  runDatabaseMigration,
  buildProject,
  createDeploymentPackage,
  generateRouterConfig,
  healthCheck,
  showDeploymentInfo
};
