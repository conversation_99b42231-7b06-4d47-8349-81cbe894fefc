# 动态公式编辑系统技术架构 v4.0

## 1. 架构概述

### 1.1 设计目标
构建一个高度灵活的动态公式编辑和计算系统，支持：
- **实时公式编辑**：用户可以随时修改任何公式
- **动态计算引擎**：公式变更后立即重新计算
- **多客户管理**：支持客户的增删改操作
- **数据持久化**：公式配置和客户数据的本地存储

### 1.2 核心技术栈
- **前端框架**：React 18 + Next.js 14
- **状态管理**：React Context + useReducer
- **公式解析**：自定义表达式解析引擎
- **数据存储**：localStorage + IndexedDB
- **UI组件**：Ant Design 5.x + 自定义组件

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────┐
│                   用户界面层 (UI Layer)                    │
├─────────────────────────────────────────────────────────┤
│  DynamicFormulaSystem (主组件)                           │
│  ├── FormulaConfigGrid (公式配置网格)                      │
│  ├── CustomerManagement (客户管理)                       │
│  └── DetailedDataTables (详细数据表格)                    │
├─────────────────────────────────────────────────────────┤
│                   业务逻辑层 (Logic Layer)                 │
├─────────────────────────────────────────────────────────┤
│  FormulaContext (状态管理)                               │
│  ├── FormulaState (公式状态)                             │
│  ├── CustomerState (客户状态)                           │
│  └── CalculationState (计算状态)                         │
├─────────────────────────────────────────────────────────┤
│                   计算引擎层 (Engine Layer)                │
├─────────────────────────────────────────────────────────┤
│  FormulaEngine (公式引擎)                                │
│  ├── ExpressionParser (表达式解析器)                      │
│  ├── VariableResolver (变量解析器)                        │
│  └── CalculationProcessor (计算处理器)                    │
├─────────────────────────────────────────────────────────┤
│                   数据持久层 (Data Layer)                  │
├─────────────────────────────────────────────────────────┤
│  DataManager (数据管理器)                                │
│  ├── FormulaStorage (公式存储)                           │
│  ├── CustomerStorage (客户存储)                         │
│  └── CacheManager (缓存管理)                            │
└─────────────────────────────────────────────────────────┘
```

## 3. 核心组件设计

### 3.1 DynamicFormulaSystem (主组件)
```javascript
// components/DynamicFormulaSystem.jsx
import React, { useContext, useEffect } from 'react';
import { Layout, message } from 'antd';
import FormulaConfigGrid from './FormulaConfigGrid';
import CustomerManagement from './CustomerManagement';
import DetailedDataTables from './DetailedDataTables';
import { FormulaProvider, useFormula } from '../contexts/FormulaContext';

const DynamicFormulaSystem = () => {
  return (
    <FormulaProvider>
      <Layout className="dynamic-formula-system">
        <Layout.Content>
          {/* 公式配置网格 */}
          <FormulaConfigGrid />
          
          {/* 客户管理区域 */}
          <CustomerManagement />
          
          {/* 详细数据表格 */}
          <DetailedDataTables />
        </Layout.Content>
      </Layout>
    </FormulaProvider>
  );
};
```

### 3.2 FormulaConfigGrid (公式配置网格)
```javascript
// components/FormulaConfigGrid.jsx
import React, { useState } from 'react';
import { Card, Row, Col, Button } from 'antd';
import EditableFormulaCard from './EditableFormulaCard';
import FormulaEditor from './FormulaEditor';
import { useFormula } from '../contexts/FormulaContext';

const FormulaConfigGrid = () => {
  const { formulas, updateFormula, addFormula, deleteFormula } = useFormula();
  const [editingFormula, setEditingFormula] = useState(null);

  // 确保有9个公式位置
  const formulaSlots = Array.from({ length: 9 }, (_, index) => {
    const formulaId = `formula_${Math.floor(index / 3) + 1}_${(index % 3) + 1}`;
    return formulas[formulaId] || { 
      id: formulaId, 
      name: `公式${Math.floor(index / 3) + 1}.${(index % 3) + 1}`, 
      expression: '', 
      isValid: false 
    };
  });

  return (
    <Card title="动态公式配置" className="formula-config-grid">
      <div className="formula-grid">
        {[0, 1, 2].map(row => (
          <Row key={row} gutter={[16, 16]} className="formula-row">
            {[0, 1, 2].map(col => {
              const index = row * 3 + col;
              const formula = formulaSlots[index];
              
              return (
                <Col key={col} span={8}>
                  <EditableFormulaCard
                    formula={formula}
                    onEdit={() => setEditingFormula(formula)}
                    onDelete={() => deleteFormula(formula.id)}
                  />
                </Col>
              );
            })}
          </Row>
        ))}
      </div>

      {/* 公式编辑器弹窗 */}
      {editingFormula && (
        <FormulaEditor
          formula={editingFormula}
          visible={!!editingFormula}
          onSave={(updatedFormula) => {
            updateFormula(updatedFormula);
            setEditingFormula(null);
          }}
          onCancel={() => setEditingFormula(null)}
        />
      )}
    </Card>
  );
};
```

### 3.3 EditableFormulaCard (可编辑公式卡片)
```javascript
// components/EditableFormulaCard.jsx
import React from 'react';
import { Card, Tag, Typography, Button, Space } from 'antd';
import { EditOutlined, DeleteOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

const EditableFormulaCard = ({ formula, onEdit, onDelete }) => {
  const hasExpression = formula.expression && formula.expression.trim() !== '';
  
  return (
    <Card
      size="small"
      hoverable
      className={`editable-formula-card ${formula.isValid ? 'valid' : 'invalid'}`}
      actions={[
        <EditOutlined key="edit" onClick={onEdit} />,
        hasExpression && <DeleteOutlined key="delete" onClick={onDelete} />
      ].filter(Boolean)}
    >
      <div className="formula-content">
        <div className="formula-header">
          <Tag 
            color={formula.isValid ? "gold" : "red"}
            className="formula-name-tag"
          >
            {formula.name}
          </Tag>
          {hasExpression && (
            formula.isValid ? 
              <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
          )}
        </div>
        
        <div className="formula-expression">
          {hasExpression ? (
            <Text code className="expression-text">
              {formula.expression}
            </Text>
          ) : (
            <Text type="secondary" className="placeholder-text">
              点击编辑添加公式
            </Text>
          )}
        </div>
        
        {!formula.isValid && hasExpression && (
          <Text type="danger" className="error-text">
            公式语法错误
          </Text>
        )}
      </div>
    </Card>
  );
};
```

### 3.4 FormulaEditor (公式编辑器)
```javascript
// components/FormulaEditor.jsx
import React, { useState, useEffect } from 'react';
import { Modal, Input, Form, Typography, Alert, Space, Tag } from 'antd';
import { FormulaEngine } from '../utils/FormulaEngine';

const { TextArea } = Input;
const { Text } = Typography;

const FormulaEditor = ({ formula, visible, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const [expression, setExpression] = useState(formula.expression || '');
  const [validationResult, setValidationResult] = useState(null);
  const [previewResult, setPreviewResult] = useState(null);

  // 可用变量
  const availableVariables = ['data1', 'data2', 'data3', 'data4', 'data5', 'data6', 'data7'];
  
  // 示例数据用于预览
  const sampleData = {
    data1: 1000, data2: 2.5, data3: 3.0, data4: 1.2, 
    data5: 2.0, data6: 800, data7: 1.5
  };

  useEffect(() => {
    if (expression.trim()) {
      // 验证表达式
      const validation = FormulaEngine.validateExpression(expression);
      setValidationResult(validation);
      
      // 预览计算结果
      if (validation.isValid) {
        const preview = FormulaEngine.calculateExpression(expression, sampleData);
        setPreviewResult(preview);
      } else {
        setPreviewResult(null);
      }
    } else {
      setValidationResult(null);
      setPreviewResult(null);
    }
  }, [expression]);

  const handleSave = () => {
    const updatedFormula = {
      ...formula,
      name: form.getFieldValue('name'),
      expression: expression.trim(),
      isValid: validationResult?.isValid || false
    };
    onSave(updatedFormula);
  };

  return (
    <Modal
      title="编辑公式"
      open={visible}
      onOk={handleSave}
      onCancel={onCancel}
      width={600}
      okButtonProps={{ 
        disabled: !validationResult?.isValid 
      }}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: formula.name,
          expression: formula.expression
        }}
      >
        <Form.Item
          label="公式名称"
          name="name"
          rules={[{ required: true, message: '请输入公式名称' }]}
        >
          <Input placeholder="例如：公式1.1" />
        </Form.Item>

        <Form.Item label="公式表达式">
          <TextArea
            value={expression}
            onChange={(e) => setExpression(e.target.value)}
            placeholder="例如：(data1+data2*data4)*data3"
            rows={3}
          />
        </Form.Item>

        <div className="variable-hints">
          <Text strong>可用变量：</Text>
          <Space wrap style={{ marginTop: 8 }}>
            {availableVariables.map(variable => (
              <Tag 
                key={variable}
                color="blue"
                style={{ cursor: 'pointer' }}
                onClick={() => setExpression(prev => prev + variable)}
              >
                {variable}
              </Tag>
            ))}
          </Space>
        </div>

        {validationResult && (
          <Alert
            type={validationResult.isValid ? 'success' : 'error'}
            message={validationResult.isValid ? '公式语法正确' : '公式语法错误'}
            description={validationResult.error || '公式可以正常使用'}
            style={{ marginTop: 16 }}
          />
        )}

        {previewResult && (
          <Alert
            type="info"
            message="预览计算结果"
            description={
              <div>
                <Text>使用示例数据计算：</Text>
                <br />
                <Text code>{previewResult.calculationExpression}</Text>
                <br />
                <Text strong>结果：{previewResult.result.toLocaleString()}</Text>
              </div>
            }
            style={{ marginTop: 16 }}
          />
        )}
      </Form>
    </Modal>
  );
};
```

## 4. 状态管理设计

### 4.1 FormulaContext (状态管理)
```javascript
// contexts/FormulaContext.js
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { FormulaEngine } from '../utils/FormulaEngine';
import { DataManager } from '../utils/DataManager';

const FormulaContext = createContext();

const initialState = {
  formulas: {},
  customers: [],
  calculationResults: {},
  isCalculating: false,
  lastUpdated: null
};

const formulaReducer = (state, action) => {
  switch (action.type) {
    case 'SET_FORMULAS':
      return { ...state, formulas: action.payload };
    
    case 'UPDATE_FORMULA':
      const updatedFormulas = {
        ...state.formulas,
        [action.payload.id]: action.payload
      };
      return { 
        ...state, 
        formulas: updatedFormulas,
        lastUpdated: Date.now()
      };
    
    case 'DELETE_FORMULA':
      const { [action.payload]: deleted, ...remainingFormulas } = state.formulas;
      return { 
        ...state, 
        formulas: remainingFormulas,
        lastUpdated: Date.now()
      };
    
    case 'SET_CUSTOMERS':
      return { ...state, customers: action.payload };
    
    case 'ADD_CUSTOMER':
      return { 
        ...state, 
        customers: [...state.customers, action.payload],
        lastUpdated: Date.now()
      };
    
    case 'DELETE_CUSTOMER':
      return { 
        ...state, 
        customers: state.customers.filter(c => c.id !== action.payload),
        lastUpdated: Date.now()
      };
    
    case 'UPDATE_CUSTOMER_DATA':
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.customerId
            ? { ...customer, data: { ...customer.data, ...action.payload.data } }
            : customer
        ),
        lastUpdated: Date.now()
      };
    
    case 'SET_CALCULATION_RESULTS':
      return { ...state, calculationResults: action.payload };
    
    case 'SET_CALCULATING':
      return { ...state, isCalculating: action.payload };
    
    default:
      return state;
  }
};

export const FormulaProvider = ({ children }) => {
  const [state, dispatch] = useReducer(formulaReducer, initialState);

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      const savedFormulas = await DataManager.loadFormulas();
      const savedCustomers = await DataManager.loadCustomers();
      
      dispatch({ type: 'SET_FORMULAS', payload: savedFormulas });
      dispatch({ type: 'SET_CUSTOMERS', payload: savedCustomers });
    };
    
    loadData();
  }, []);

  // 自动计算
  useEffect(() => {
    if (state.lastUpdated) {
      calculateAllResults();
    }
  }, [state.formulas, state.customers, state.lastUpdated]);

  // 自动保存
  useEffect(() => {
    if (state.lastUpdated) {
      DataManager.saveFormulas(state.formulas);
      DataManager.saveCustomers(state.customers);
    }
  }, [state.formulas, state.customers, state.lastUpdated]);

  const calculateAllResults = async () => {
    dispatch({ type: 'SET_CALCULATING', payload: true });
    
    try {
      const results = {};
      
      for (const customer of state.customers) {
        results[customer.id] = {};
        
        for (const [formulaId, formula] of Object.entries(state.formulas)) {
          if (formula.isValid && formula.expression) {
            const calculation = FormulaEngine.calculateExpression(
              formula.expression, 
              customer.data
            );
            results[customer.id][formulaId] = calculation;
          }
        }
      }
      
      dispatch({ type: 'SET_CALCULATION_RESULTS', payload: results });
    } catch (error) {
      console.error('计算错误:', error);
    } finally {
      dispatch({ type: 'SET_CALCULATING', payload: false });
    }
  };

  const updateFormula = (formula) => {
    dispatch({ type: 'UPDATE_FORMULA', payload: formula });
  };

  const deleteFormula = (formulaId) => {
    dispatch({ type: 'DELETE_FORMULA', payload: formulaId });
  };

  const addCustomer = (customerName) => {
    const newCustomer = {
      id: `customer_${Date.now()}`,
      name: customerName,
      data: {
        data1: 0, data2: 0, data3: 0, data4: 0,
        data5: 0, data6: 0, data7: 0
      }
    };
    dispatch({ type: 'ADD_CUSTOMER', payload: newCustomer });
  };

  const deleteCustomer = (customerId) => {
    dispatch({ type: 'DELETE_CUSTOMER', payload: customerId });
  };

  const updateCustomerData = (customerId, data) => {
    dispatch({ 
      type: 'UPDATE_CUSTOMER_DATA', 
      payload: { customerId, data } 
    });
  };

  return (
    <FormulaContext.Provider value={{
      ...state,
      updateFormula,
      deleteFormula,
      addCustomer,
      deleteCustomer,
      updateCustomerData,
      calculateAllResults
    }}>
      {children}
    </FormulaContext.Provider>
  );
};

export const useFormula = () => {
  const context = useContext(FormulaContext);
  if (!context) {
    throw new Error('useFormula must be used within a FormulaProvider');
  }
  return context;
};
```

## 5. 公式引擎设计

### 5.1 FormulaEngine (公式引擎)
```javascript
// utils/FormulaEngine.js
export class FormulaEngine {
  // 验证表达式
  static validateExpression(expression) {
    try {
      if (!expression || expression.trim() === '') {
        return { isValid: false, error: '表达式不能为空' };
      }

      // 检查允许的字符
      const allowedPattern = /^[data1-7+\-*/.() ]+$/;
      if (!allowedPattern.test(expression)) {
        return { isValid: false, error: '表达式包含不允许的字符' };
      }

      // 检查变量格式
      const variablePattern = /data[1-7]/g;
      const variables = expression.match(variablePattern) || [];
      const validVariables = ['data1', 'data2', 'data3', 'data4', 'data5', 'data6', 'data7'];
      
      for (const variable of variables) {
        if (!validVariables.includes(variable)) {
          return { isValid: false, error: `无效的变量: ${variable}` };
        }
      }

      // 尝试解析表达式
      const testData = { data1: 1, data2: 1, data3: 1, data4: 1, data5: 1, data6: 1, data7: 1 };
      this.calculateExpression(expression, testData);

      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: error.message };
    }
  }

  // 计算表达式
  static calculateExpression(expression, data) {
    try {
      let calculationExpression = expression;
      
      // 替换变量
      Object.keys(data).forEach(key => {
        const regex = new RegExp(key, 'g');
        calculationExpression = calculationExpression.replace(regex, data[key]);
      });

      // 安全计算
      const result = Function(`"use strict"; return (${calculationExpression})`)();
      
      if (!isFinite(result)) {
        throw new Error('计算结果无效');
      }

      return {
        success: true,
        result: result,
        calculationExpression: calculationExpression
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        calculationExpression: calculationExpression || expression
      };
    }
  }
}
```

---

**Bob完成时间**: 2025年7月30日  
**下一步**: 提交给Alex进行具体实现  
**关键目标**: 实现完全动态的公式编辑和计算系统
