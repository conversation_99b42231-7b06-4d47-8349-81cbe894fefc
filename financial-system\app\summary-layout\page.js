'use client';

// 新布局汇总表页面 - 根据截图要求实现的汇总表页面

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Layout, 
  Button, 
  Space,
  message,
  Spin
} from 'antd';
import { 
  ArrowLeftOutlined,
  ExportOutlined
} from '@ant-design/icons';
import NewLayoutSummaryTable from '../../components/NewLayoutSummaryTable';

const { Header, Content } = Layout;

export default function SummaryLayoutPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [customerData, setCustomerData] = useState({});

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    
    // 加载数据
    loadSummaryData();
  }, []);

  // 加载汇总表数据
  const loadSummaryData = async () => {
    console.log('🔄 开始加载汇总表数据...');
    setLoading(true);
    try {
      let dataLoaded = false;

      // 1. 优先尝试从API加载数据
      try {
        console.log('📡 尝试从API加载数据...');
        const response = await fetch('/api/summary-layout');
        console.log('📡 API响应状态:', response.status, response.ok);

        if (response.ok) {
          const apiData = await response.json();
          console.log('📡 API返回数据:', apiData);

          if (apiData.success && apiData.customerData && Object.keys(apiData.customerData).length > 0) {
            setCustomerData(apiData.customerData);
            console.log('✅ 从API加载数据成功:', apiData.customerData);
            dataLoaded = true;
          }
        }
      } catch (apiError) {
        console.log('⚠️ API加载失败:', apiError);
      }

      // 2. 如果API没有数据，尝试从本地存储加载
      if (!dataLoaded) {
        console.log('💾 尝试从本地存储加载数据...');
        const localData = localStorage.getItem('summaryTableData');
        if (localData) {
          const parsedData = JSON.parse(localData);
          console.log('💾 本地存储数据:', parsedData);

          if (parsedData.customerData && Object.keys(parsedData.customerData).length > 0) {
            setCustomerData(parsedData.customerData);
            console.log('✅ 从本地存储加载数据成功:', parsedData.customerData);
            dataLoaded = true;
          }
        }
      }

      // 3. 如果没有任何数据，显示提示
      if (!dataLoaded) {
        console.log('⚠️ 没有找到任何数据');
        message.info('没有找到汇总数据，请先在公式配置页面保存数据');
        setCustomerData({});
      }

    } catch (error) {
      console.error('❌ 加载汇总数据失败:', error);
      message.error('加载数据失败，请重试');
      setCustomerData({});
    } finally {
      setLoading(false);
      console.log('🔄 数据加载完成');
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    loadSummaryData();
    message.success('数据已刷新');
  };

  // 导出数据
  const handleExport = () => {
    try {
      const exportData = {
        customerData,
        exportTime: new Date().toISOString(),
        totalCustomers: Object.values(customerData).reduce((sum, group) => sum + group.length, 0)
      };
      
      // 创建下载链接
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `汇总表数据_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      message.success('数据导出成功！');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  // 返回公式配置页面
  const handleGoBack = () => {
    router.push('/formula-config');
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Header style={{ 
        background: 'white', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={handleGoBack}
            style={{ marginRight: '16px' }}
          >
            返回公式配置
          </Button>
          <h2 style={{ margin: 0, color: '#1890ff' }}>
            📊 汇总表 - 新布局视图
          </h2>
        </div>

        <Space>
          <Button 
            type="primary"
            icon={<ExportOutlined />}
            onClick={handleExport}
          >
            导出数据
          </Button>
        </Space>
      </Header>

      <Content>
        {loading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px'
          }}>
            <Spin size="large" tip="正在加载汇总数据..." />
          </div>
        ) : (
          <NewLayoutSummaryTable
            customerData={customerData}
            loading={loading}
            onRefresh={handleRefresh}
            onExport={handleExport}
          />
        )}
      </Content>
    </Layout>
  );
}
