// 自动化测试脚本 - 测试新布局汇总表功能
// 使用Playwright进行端到端测试

const { chromium } = require('playwright');

async function testSummaryLayoutFeature() {
  console.log('🚀 开始测试新布局汇总表功能...');
  
  const browser = await chromium.launch({ 
    headless: false, // 显示浏览器窗口以便观察
    slowMo: 1500 // 减慢操作速度以便观察
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // 1. 访问登录页面
    console.log('📝 步骤1: 访问登录页面');
    await page.goto('http://localhost:3001/login');
    await page.waitForTimeout(2000);

    // 2. 登录系统
    console.log('🔐 步骤2: 登录系统');
    await page.fill('input[id="username"]', 'admin');
    await page.fill('input[id="password"]', '123456');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(3000);

    // 3. 进入公式配置页面
    console.log('⚙️ 步骤3: 进入公式配置页面');
    await page.goto('http://localhost:3001/formula-config');
    await page.waitForTimeout(3000);

    // 4. 添加测试数据
    console.log('📊 步骤4: 添加测试客户数据');
    
    // 点击添加客户按钮
    const addCustomerButton = page.locator('button:has-text("添加客户")').first();
    await addCustomerButton.click();
    await page.waitForTimeout(1000);

    // 填写客户信息
    const customerInputs = page.locator('input');
    await customerInputs.nth(0).fill('测试客户A'); // 客户名
    await customerInputs.nth(1).fill('测试用户A'); // 用户名
    
    // 填写一些数据字段
    await customerInputs.nth(2).fill('1000'); // 数据1
    await customerInputs.nth(3).fill('2000'); // 数据2
    await customerInputs.nth(4).fill('0.1');  // 数据3
    await customerInputs.nth(5).fill('500');  // 数据4

    await page.waitForTimeout(2000);

    // 5. 点击保存植入汇总表按钮
    console.log('💾 步骤5: 点击保存植入汇总表');
    const saveButton = page.locator('button:has-text("保存值入汇总表")');
    await saveButton.click();
    await page.waitForTimeout(3000);

    // 6. 验证是否跳转到新的汇总表页面
    console.log('🔍 步骤6: 验证页面跳转');
    await page.waitForURL('**/summary-layout', { timeout: 10000 });
    console.log('✓ 成功跳转到汇总表页面');

    // 7. 验证新布局汇总表页面元素
    console.log('📋 步骤7: 验证页面元素');
    
    // 检查页面标题
    const pageTitle = page.locator('h2:has-text("汇总表 - 新布局视图")');
    await pageTitle.waitFor({ timeout: 5000 });
    console.log('✓ 页面标题显示正确');

    // 检查右上角标签切换
    const weeklyTab = page.locator('span:has-text("周报表")');
    const monthlyTab = page.locator('span:has-text("月报表")');
    await weeklyTab.waitFor({ timeout: 5000 });
    await monthlyTab.waitFor({ timeout: 5000 });
    console.log('✓ 周报表/月报表标签显示正确');

    // 检查左侧客户列表
    const customerListCard = page.locator('.ant-card:has(.ant-card-head-title:has-text("客户名"))');
    await customerListCard.waitFor({ timeout: 5000 });
    console.log('✓ 左侧客户列表显示正确');

    // 检查中间备注区域
    const remarksCard = page.locator('.ant-card:has(.ant-card-head-title:has-text("备注"))');
    await remarksCard.waitFor({ timeout: 5000 });
    console.log('✓ 中间备注区域显示正确');

    // 检查右侧详细表
    const detailCard = page.locator('.ant-card:has(.ant-card-head-title:has-text("详细表"))');
    await detailCard.waitFor({ timeout: 5000 });
    console.log('✓ 右侧详细表显示正确');

    // 8. 测试标签切换功能
    console.log('🔄 步骤8: 测试标签切换功能');
    await monthlyTab.click();
    await page.waitForTimeout(1000);
    console.log('✓ 月报表标签切换成功');

    await weeklyTab.click();
    await page.waitForTimeout(1000);
    console.log('✓ 周报表标签切换成功');

    // 9. 测试备注编辑功能
    console.log('✏️ 步骤9: 测试备注编辑功能');
    const editRemarksButton = page.locator('button:has-text("编辑")');
    await editRemarksButton.click();
    await page.waitForTimeout(1000);

    const remarksTextarea = page.locator('textarea');
    await remarksTextarea.fill('这是一个测试备注\n用于验证备注功能是否正常工作');
    
    const saveRemarksButton = page.locator('button:has-text("保存")');
    await saveRemarksButton.click();
    await page.waitForTimeout(1000);
    console.log('✓ 备注编辑功能正常');

    // 10. 测试返回功能
    console.log('🔙 步骤10: 测试返回功能');
    const backButton = page.locator('button:has-text("返回公式配置")');
    await backButton.click();
    await page.waitForTimeout(2000);

    // 验证是否返回到公式配置页面
    await page.waitForURL('**/formula-config', { timeout: 5000 });
    console.log('✓ 返回公式配置页面成功');

    // 11. 再次测试保存植入功能
    console.log('🔄 步骤11: 再次测试保存植入功能');
    const saveButtonAgain = page.locator('button:has-text("保存值入汇总表")');
    await saveButtonAgain.click();
    await page.waitForTimeout(2000);

    await page.waitForURL('**/summary-layout', { timeout: 5000 });
    console.log('✓ 再次跳转到汇总表页面成功');

    // 12. 测试导出功能
    console.log('📤 步骤12: 测试导出功能');
    const exportButton = page.locator('button:has-text("导出数据")');
    await exportButton.click();
    await page.waitForTimeout(2000);
    console.log('✓ 导出功能触发成功');

    console.log('✅ 所有测试完成！新布局汇总表功能验证成功');
    
    // 等待一段时间以便观察结果
    await page.waitForTimeout(5000);

  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    // 截图保存错误状态
    try {
      await page.screenshot({ path: 'test-error.png', fullPage: true });
      console.log('📸 错误截图已保存为 test-error.png');
    } catch (screenshotError) {
      console.error('截图失败:', screenshotError);
    }
    
    throw error;
  } finally {
    await browser.close();
  }
}

// 运行测试
if (require.main === module) {
  testSummaryLayoutFeature()
    .then(() => {
      console.log('🎉 所有测试通过！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testSummaryLayoutFeature };
