// 使用实际DynamicFormulaEngine测试负数计算

import { DynamicFormulaEngine } from './utils/DynamicFormulaEngine.js';

class RealEngineTester {
  constructor() {
    this.testResults = [];
    this.formulaExpressions = {
      '公式1.1': '(data1+data2+data4)+data3',
      '公式1.2': '(data1+data2+data4)+data3',
      '公式2.1': 'data1*data2',
      '公式2.2': 'data2*data4',
      '公式3.1': 'data1',
      '公式4.1': 'data1/data2',
      '公式4.2': 'data2/data4',
      '公式5.1': 'data1-data2',
      '公式5.2': 'data2-data4',
      '公式6.1': 'data1^2',
      '公式6.2': 'data2^2',
      '公式7.1': 'data1',
      '公式7.2': 'data2*data4',
      '公式7.3': 'data3+data5'
    };
  }

  logResult(testName, passed, details = '') {
    const result = {
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
  }

  // 手动计算预期结果
  calculateExpected(expression, data) {
    try {
      // 安全的表达式计算
      let safeExpression = expression
        .replace(/data1/g, `(${data.data1})`)
        .replace(/data2/g, `(${data.data2})`)
        .replace(/data3/g, `(${data.data3})`)
        .replace(/data4/g, `(${data.data4})`)
        .replace(/data5/g, `(${data.data5})`)
        .replace(/data6/g, `(${data.data6})`)
        .replace(/data7/g, `(${data.data7})`)
        .replace(/\^/g, '**'); // 处理幂运算
      
      const result = eval(safeExpression);
      return Math.round(result); // 四舍五入到整数
    } catch (error) {
      console.log(`❌ 计算预期结果失败: ${expression}, 错误: ${error.message}`);
      return 0;
    }
  }

  // 测试所有公式的负数计算
  testAllFormulasWithNegatives() {
    console.log('🧮 开始测试所有公式的负数计算准确性...\n');
    
    const testCases = [
      {
        name: '全正数',
        data: { data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 }
      },
      {
        name: '全负数',
        data: { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 }
      },
      {
        name: '混合正负数',
        data: { data1: 100, data2: -2, data3: 50, data4: -1, data5: 25, data6: -80, data7: 3 }
      },
      {
        name: '零值混合',
        data: { data1: 0, data2: -2, data3: 0, data4: 1, data5: 0, data6: -80, data7: 0 }
      }
    ];

    for (const testCase of testCases) {
      console.log(`📊 测试场景: ${testCase.name}`);
      console.log(`输入数据: ${JSON.stringify(testCase.data)}`);
      
      let scenarioErrors = 0;
      
      // 测试每个公式
      for (const [formulaName, expression] of Object.entries(this.formulaExpressions)) {
        // 计算预期结果
        const expected = this.calculateExpected(expression, testCase.data);
        
        // 使用实际DynamicFormulaEngine计算
        const actual = DynamicFormulaEngine.calculateExpression(expression, testCase.data);
        
        // 比较结果
        const passed = expected === actual;
        if (!passed) {
          scenarioErrors++;
          this.logResult(
            `${testCase.name}-${formulaName}`, 
            false, 
            `表达式: ${expression}, 预期: ${expected}, 实际: ${actual}`
          );
        } else {
          console.log(`  ✅ ${formulaName}: ${actual} (正确)`);
        }
      }
      
      if (scenarioErrors === 0) {
        this.logResult(`${testCase.name}-整体`, true, '所有公式计算正确');
      } else {
        this.logResult(`${testCase.name}-整体`, false, `${scenarioErrors} 个公式计算错误`);
      }
      
      console.log(''); // 空行分隔
    }
  }

  // 测试特定问题公式
  testProblematicFormulas() {
    console.log('🔍 专项测试问题公式...\n');
    
    const problematicCases = [
      {
        name: '减法运算-负数',
        expression: 'data1-data2',
        data: { data1: -100, data2: -2 },
        expected: -98
      },
      {
        name: '幂运算-负数底数',
        expression: 'data1^2',
        data: { data1: -10 },
        expected: 100
      },
      {
        name: '幂运算-负数底数小数',
        expression: 'data2^2',
        data: { data2: -2.5 },
        expected: 6 // Math.round(6.25)
      }
    ];

    for (const testCase of problematicCases) {
      console.log(`🧪 ${testCase.name}:`);
      console.log(`  表达式: ${testCase.expression}`);
      console.log(`  数据: ${JSON.stringify(testCase.data)}`);
      console.log(`  预期: ${testCase.expected}`);
      
      const actual = DynamicFormulaEngine.calculateExpression(testCase.expression, testCase.data);
      console.log(`  实际: ${actual}`);
      
      const passed = actual === testCase.expected;
      this.logResult(
        testCase.name, 
        passed, 
        `结果: ${actual}, 预期: ${testCase.expected}`
      );
      
      console.log('');
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📋 实际引擎测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log('='.repeat(50));
    
    // 失败测试详情
    const failedTestsList = this.testResults.filter(r => !r.passed);
    if (failedTestsList.length > 0) {
      console.log('\n🔴 失败测试详情:');
      failedTestsList.forEach((result, index) => {
        console.log(`${index + 1}. ${result.test}`);
        console.log(`   ${result.details}`);
      });
    } else {
      console.log('\n✅ 所有测试通过！实际引擎负数处理正确。');
    }
    
    console.log('\n' + '='.repeat(50));
  }

  // 运行所有测试
  runAllTests() {
    console.log('🎯 开始实际引擎测试...\n');
    
    this.testAllFormulasWithNegatives();
    this.testProblematicFormulas();
    this.generateReport();
  }
}

// 执行测试
function main() {
  const tester = new RealEngineTester();
  tester.runAllTests();
}

main();
