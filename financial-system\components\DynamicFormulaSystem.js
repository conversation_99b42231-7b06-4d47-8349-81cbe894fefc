'use client';

import React from 'react';
import { Layout } from 'antd';
import { FormulaProvider } from '../contexts/FormulaContext';
import FormulaConfigGrid from './FormulaConfigGrid';
import CustomerManagement from './CustomerManagement';
import DetailedDataTables from './DetailedDataTables';

const { Content } = Layout;

// 动态公式系统主组件
const DynamicFormulaSystem = ({ customers, setCustomers }) => {
  return (
    <FormulaProvider initialCustomers={customers} onCustomersChange={setCustomers}>
      <Layout className="dynamic-formula-system" style={{ background: '#f5f5f5' }}>
        <Content style={{ padding: '24px' }}>
          {/* 公式配置网格区域 */}
          <FormulaConfigGrid />
          
          {/* 客户管理区域 */}
          <CustomerManagement />
          
          {/* 详细数据表格区域 */}
          <DetailedDataTables />
        </Content>
      </Layout>
    </FormulaProvider>
  );
};

export default DynamicFormulaSystem;
