'use client';

// 公式配置页面专用的客户归纳汇总表组件
// 适配公式配置页面的数据结构（group1-group7格式）

import { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  InputNumber,
  Typography,
  Row,
  Col,
  Divider,
  message
} from 'antd';
import { 
  ExportOutlined, 
  ReloadOutlined, 
  EditOutlined,
  SaveOutlined,
  CalculatorOutlined
} from '@ant-design/icons';
import { FormulaPageConsolidationCalculations } from '../lib/formulaPageConsolidationCalculations';

const { Title, Text } = Typography;

export default function FormulaPageSummaryTable({
  customerData = {},
  loading = false,
  onRefresh,
  onExport
}) {
  // 状态管理
  const [consolidatedData, setConsolidatedData] = useState([]);
  const [consolidatedByUser, setConsolidatedByUser] = useState({});
  const [manualInputs, setManualInputs] = useState({
    收入数量: 0,
    收入价格: 0,
    付出数量: 0,
    付出价格: 0,
    上周余额1: 0,
    上周余额2: 0,
    减免1: 0,
    减免2: 0
  });
  const [isEditing, setIsEditing] = useState(false);
  const [rightSummary, setRightSummary] = useState({
    收入: { 数量: 0, 价格: 0, 金额: 0, 显示金额: 0 },
    付出: { 数量: 0, 价格: 0, 金额: 0, 显示金额: 0 },
    上周余额: { 数量: null, 价格: null, 金额: 0, 显示金额: 0 },
    减免: { 数量: null, 价格: null, 金额: 0, 显示金额: 0 },
    总计: { 数量: null, 价格: null, 金额: 0, 显示金额: 0 }
  });

  // 计算归纳数据
  const calculatedData = useMemo(() => {
    try {
      const result = FormulaPageConsolidationCalculations.consolidateFormulaPageData(
        customerData,
        manualInputs
      );
      return result;
    } catch (error) {
      console.error('FormulaPageSummaryTable: 计算归纳数据时出错', error);
      return FormulaPageConsolidationCalculations.getEmptyConsolidatedData();
    }
  }, [customerData, manualInputs]);

  // 初始化手动输入数据（只在组件挂载时执行一次）
  useEffect(() => {
    const savedInputs = FormulaPageConsolidationCalculations.getManualInputs();
    setManualInputs(savedInputs);
  }, []); // 空依赖数组，只在组件挂载时执行一次

  // 更新计算结果数据
  useEffect(() => {
    setConsolidatedData(calculatedData.consolidatedCustomers);
    setConsolidatedByUser(calculatedData.consolidatedByUser || {});
    setRightSummary(calculatedData.rightSummary);
  }, [calculatedData]);

  // 将分组数据转换为平铺数组，按客户名排序
  const flatConsolidatedData = useMemo(() => {
    const flatData = [];

    // 将按客户名分组的数据平铺
    Object.entries(consolidatedByUser).forEach(([customerName, records]) => {
      records.forEach(record => {
        flatData.push({
          ...record,
          // 确保有totalSum和roundedSum字段
          totalSum: (record.result1 || 0) + (record.result2 || 0) + (record.result3 || 0),
          roundedSum: Math.round((record.result1 || 0) + (record.result2 || 0) + (record.result3 || 0))
        });
      });
    });

    // 按客户名排序，相同客户名的按用户名排序
    return flatData.sort((a, b) => {
      const customerCompare = (a.customerName || '').localeCompare(b.customerName || '');
      if (customerCompare !== 0) return customerCompare;
      return (a.userName || '').localeCompare(b.userName || '');
    });
  }, [consolidatedByUser]);

  // 计算左侧表格的总和
  const leftTableTotal = useMemo(() => {
    return consolidatedData.reduce((sum, customer) => {
      return sum + (customer.roundedSum || 0);
    }, 0);
  }, [consolidatedData]);

  // 保存手动输入数据
  const handleSaveManualInputs = async () => {
    try {
      await FormulaPageConsolidationCalculations.saveManualInputs(manualInputs);
      setIsEditing(false);
      message.success('手动输入数据保存成功！');
      
      // 重新计算
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      message.error('保存失败：' + error.message);
    }
  };

  // 更新手动输入值
  const updateManualInput = (key, value) => {
    setManualInputs(prev => ({
      ...prev,
      [key]: value || 0
    }));
  };

  // 表格列定义
  const getTableColumns = () => {
    return [
      {
        title: '客户名',
        dataIndex: 'customerName',
        key: 'customerName',
        width: 120,
        fixed: 'left',
        render: (text, record) => (
          <div>
            <Text strong style={{ color: '#1890ff' }}>
              {text}
            </Text>
            {record.isMerged && record.mergeInfo && (
              <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
                客户:{record.mergeInfo.customerCount}条
                {record.mergeInfo.shareholderCount > 0 && (
                  <span style={{ color: '#52c41a' }}>
                    + 股东:{record.mergeInfo.shareholderCount}条
                  </span>
                )}
              </div>
            )}
            {!record.isMerged && record.originalCount > 1 && (
              <div style={{ fontSize: '12px', color: '#999' }}>
                (合并了{record.originalCount}个客户)
              </div>
            )}
          </div>
        )
      },
      {
        title: '用户名',
        dataIndex: 'userName',
        key: 'userName',
        width: 120,
        render: (text, record) => (
          <div>
            <Text style={{ color: '#52c41a' }}>
              {text}
            </Text>
            {record.isMerged && record.userNames && record.userNames.length > 1 && (
              <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
                (涉及{record.userNames.length}个用户)
              </div>
            )}
          </div>
        )
      },
      {
        title: '数据1',
        dataIndex: 'data1',
        key: 'data1',
        width: 100,
        render: (value) => (
          <Text>{FormulaPageConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '数据2', 
        dataIndex: 'data2',
        key: 'data2',
        width: 100,
        render: (value) => (
          <Text>{FormulaPageConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '数据6',
        dataIndex: 'data6',
        key: 'data6', 
        width: 100,
        render: (value) => (
          <Text>{FormulaPageConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '数据7',
        dataIndex: 'data7',
        key: 'data7',
        width: 100,
        render: (value) => (
          <Text>{FormulaPageConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '结果1',
        dataIndex: 'result1',
        key: 'result1',
        width: 100,
        render: (value) => (
          <Text strong style={{ color: '#52c41a' }}>
            {FormulaPageConsolidationCalculations.formatNumber(value, 0)}
          </Text>
        )
      },
      {
        title: '结果2',
        dataIndex: 'result2', 
        key: 'result2',
        width: 100,
        render: (value) => (
          <Text strong style={{ color: '#52c41a' }}>
            {FormulaPageConsolidationCalculations.formatNumber(value, 0)}
          </Text>
        )
      },
      {
        title: '结果3',
        dataIndex: 'result3',
        key: 'result3',
        width: 100,
        render: (value) => (
          <Text strong style={{ color: '#52c41a' }}>
            {value ? FormulaPageConsolidationCalculations.formatNumber(value, 0) : '-'}
          </Text>
        )
      },
      {
        title: '总和',
        dataIndex: 'totalSum',
        key: 'totalSum',
        width: 120,
        render: (value) => (
          <Text strong style={{ 
            color: '#fa8c16',
            backgroundColor: '#fff7e6',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
          </Text>
        )
      },
      {
        title: '总和(四舍五入)',
        dataIndex: 'roundedSum',
        key: 'roundedSum', 
        width: 140,
        render: (value) => (
          <Text strong style={{ 
            color: '#722ed1',
            backgroundColor: '#f9f0ff',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
          </Text>
        )
      },
      {
        title: '股东信息',
        dataIndex: 'shareholderSummary',
        key: 'shareholderSummary',
        width: 220,
        render: (_, record) => (
          <div style={{ fontSize: '12px' }}>
            {record.isMerged && record.mergeInfo && record.mergeInfo.shareholderCount > 0 && (
              <div style={{
                backgroundColor: '#f6ffed',
                padding: '4px 8px',
                borderRadius: '4px',
                marginBottom: '8px',
                border: '1px solid #b7eb8f'
              }}>
                <Text strong style={{ color: '#52c41a', fontSize: '11px' }}>
                  ✓ 包含同名股东数据 ({record.mergeInfo.shareholderCount}条)
                </Text>
              </div>
            )}
            {record.consolidatedShareholders && record.consolidatedShareholders.length > 0 ? (
              record.consolidatedShareholders.slice(0, 3).map((shareholder, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  <Text strong style={{
                    color: shareholder.name === record.customerName ? '#52c41a' : '#333'
                  }}>
                    {shareholder.name}
                    {shareholder.name === record.customerName && (
                      <span style={{ color: '#52c41a', fontSize: '10px' }}> (同名)</span>
                    )}
                  </Text>
                  <Text style={{ color: '#999', marginLeft: '8px' }}>
                    比例: {shareholder.ratio ? shareholder.ratio.toFixed(2) : '0.00'}
                  </Text>
                </div>
              ))
            ) : (
              <Text type="secondary">无股东信息</Text>
            )}
            {record.consolidatedShareholders && record.consolidatedShareholders.length > 3 && (
              <Text type="secondary" style={{ fontSize: '11px' }}>
                ...还有{record.consolidatedShareholders.length - 3}个股东
              </Text>
            )}
          </div>
        )
      }
    ];
  };

  // 渲染单个客户的汇总统计
  const renderCustomerSummary = (customerName, customerRecords) => {
    // 获取或初始化该客户的手动输入数据
    const customerKey = customerName.replace(/\s+/g, '_');
    const customerManualInputs = {
      收入数量: manualInputs[`${customerKey}_收入数量`] || 0,
      收入价格: manualInputs[`${customerKey}_收入价格`] || 0,
      付出数量: manualInputs[`${customerKey}_付出数量`] || 0,
      付出价格: manualInputs[`${customerKey}_付出价格`] || 0,
      上周余额1: manualInputs[`${customerKey}_上周余额1`] || 0,
      上周余额2: manualInputs[`${customerKey}_上周余额2`] || 0,
      减免1: manualInputs[`${customerKey}_减免1`] || 0,
      减免2: manualInputs[`${customerKey}_减免2`] || 0,
    };

    // 计算各项金额
    const 收入金额 = customerManualInputs.收入数量 * customerManualInputs.收入价格;
    const 付出金额 = customerManualInputs.付出数量 * customerManualInputs.付出价格;
    const 上周余额金额 = customerManualInputs.上周余额1 + customerManualInputs.上周余额2;
    const 减免金额 = customerManualInputs.减免1 + customerManualInputs.减免2;

    // 计算该客户在左侧表格中的总和
    const 客户总和 = customerRecords.reduce((sum, record) => {
      return sum + (record.roundedSum || 0);
    }, 0);

    // 计算汇总统计的总金额
    const 汇总统计总金额 = 收入金额 + 付出金额 + 上周余额金额 + 减免金额;

    // 计算最终总和
    const 最终总和 = 客户总和 + 汇总统计总金额;

    return (
      <Card
        title={`${customerName} - 汇总统计`}
        size="small"
        style={{
          backgroundColor: '#fafafa',
          border: '2px solid #1890ff'
        }}
      >
        {/* 表格式布局 */}
        <div style={{ marginBottom: '16px' }}>
          {/* 表头 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '8px',
            backgroundColor: '#f0f8ff',
            borderRadius: '6px',
            marginBottom: '12px',
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#666',
            textAlign: 'center'
          }}>
            <div>项目</div>
            <div>数量</div>
            <div>价格</div>
            <div>金额</div>
          </div>

          {/* 收入行 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '8px',
            backgroundColor: '#ffffff',
            borderRadius: '6px',
            border: '1px solid #d9d9d9',
            marginBottom: '8px',
            alignItems: 'center'
          }}>
            <div><Text strong>收入</Text></div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.收入数量}
                  onChange={(val) => updateManualInput(`${customerKey}_收入数量`, val)}
                  size="small"
                  style={{ width: '70px' }}
                  min={0}
                />
              ) : (
                <Text>{customerManualInputs.收入数量}</Text>
              )}
            </div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.收入价格}
                  onChange={(val) => updateManualInput(`${customerKey}_收入价格`, val)}
                  size="small"
                  style={{ width: '70px' }}
                  min={0}
                  step={0.01}
                />
              ) : (
                <Text>{FormulaPageConsolidationCalculations.formatNumber(customerManualInputs.收入价格, 2)}</Text>
              )}
            </div>
            <div style={{ textAlign: 'right' }}>
              <Text strong style={{ color: '#52c41a' }}>
                {FormulaPageConsolidationCalculations.formatNumber(收入金额, 2)}
              </Text>
            </div>
          </div>

          {/* 付出行 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '8px',
            backgroundColor: '#ffffff',
            borderRadius: '6px',
            border: '1px solid #d9d9d9',
            marginBottom: '8px',
            alignItems: 'center'
          }}>
            <div><Text strong>付出</Text></div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.付出数量}
                  onChange={(val) => updateManualInput(`${customerKey}_付出数量`, val)}
                  size="small"
                  style={{ width: '70px' }}
                  min={0}
                />
              ) : (
                <Text>{customerManualInputs.付出数量}</Text>
              )}
            </div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.付出价格}
                  onChange={(val) => updateManualInput(`${customerKey}_付出价格`, val)}
                  size="small"
                  style={{ width: '70px' }}
                  min={0}
                  step={0.01}
                />
              ) : (
                <Text>{FormulaPageConsolidationCalculations.formatNumber(customerManualInputs.付出价格, 2)}</Text>
              )}
            </div>
            <div style={{ textAlign: 'right' }}>
              <Text strong style={{ color: '#f5222d' }}>
                {FormulaPageConsolidationCalculations.formatNumber(付出金额, 2)}
              </Text>
            </div>
          </div>

          {/* 上周余额行 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '8px',
            backgroundColor: '#ffffff',
            borderRadius: '6px',
            border: '1px solid #d9d9d9',
            marginBottom: '8px',
            alignItems: 'center'
          }}>
            <div><Text strong>上周余额</Text></div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.上周余额1}
                  onChange={(val) => updateManualInput(`${customerKey}_上周余额1`, val)}
                  size="small"
                  style={{ width: '70px' }}
                />
              ) : (
                <Text>{FormulaPageConsolidationCalculations.formatNumber(customerManualInputs.上周余额1, 2)}</Text>
              )}
            </div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.上周余额2}
                  onChange={(val) => updateManualInput(`${customerKey}_上周余额2`, val)}
                  size="small"
                  style={{ width: '70px' }}
                />
              ) : (
                <Text>{FormulaPageConsolidationCalculations.formatNumber(customerManualInputs.上周余额2, 2)}</Text>
              )}
            </div>
            <div style={{ textAlign: 'right' }}>
              <Text strong style={{ color: '#1890ff' }}>
                {FormulaPageConsolidationCalculations.formatNumber(上周余额金额, 2)}
              </Text>
            </div>
          </div>

          {/* 减免行 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '8px',
            backgroundColor: '#ffffff',
            borderRadius: '6px',
            border: '1px solid #d9d9d9',
            marginBottom: '8px',
            alignItems: 'center'
          }}>
            <div><Text strong>减免</Text></div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.减免1}
                  onChange={(val) => updateManualInput(`${customerKey}_减免1`, val)}
                  size="small"
                  style={{ width: '70px' }}
                />
              ) : (
                <Text>{FormulaPageConsolidationCalculations.formatNumber(customerManualInputs.减免1, 2)}</Text>
              )}
            </div>
            <div style={{ textAlign: 'center' }}>
              {isEditing ? (
                <InputNumber
                  value={customerManualInputs.减免2}
                  onChange={(val) => updateManualInput(`${customerKey}_减免2`, val)}
                  size="small"
                  style={{ width: '70px' }}
                />
              ) : (
                <Text>{FormulaPageConsolidationCalculations.formatNumber(customerManualInputs.减免2, 2)}</Text>
              )}
            </div>
            <div style={{ textAlign: 'right' }}>
              <Text strong style={{ color: '#fa8c16' }}>
                {FormulaPageConsolidationCalculations.formatNumber(减免金额, 2)}
              </Text>
            </div>
          </div>

          {/* 分隔线 */}
          <div style={{
            height: '2px',
            backgroundColor: '#e8e8e8',
            margin: '16px 0',
            borderRadius: '1px'
          }} />

          {/* 客户总和行 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '8px',
            backgroundColor: '#e6f7ff',
            borderRadius: '6px',
            border: '2px solid #1890ff',
            marginBottom: '8px',
            alignItems: 'center'
          }}>
            <div><Text strong style={{ color: '#1890ff' }}>客户总和</Text></div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">-</Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">-</Text>
            </div>
            <div style={{ textAlign: 'right' }}>
              <Text strong style={{
                color: '#1890ff',
                fontSize: '14px'
              }}>
                {FormulaPageConsolidationCalculations.formatNumber(客户总和, 0, true)}
              </Text>
            </div>
          </div>

          {/* 最终总和行 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 80px 80px 100px',
            gap: '8px',
            padding: '12px 8px',
            backgroundColor: '#fffbe6',
            borderRadius: '6px',
            border: '2px solid #faad14',
            marginBottom: '8px',
            alignItems: 'center',
            boxShadow: '0 2px 8px rgba(250, 173, 20, 0.2)'
          }}>
            <div><Text strong style={{ color: '#faad14', fontSize: '15px' }}>最终总和</Text></div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">-</Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">-</Text>
            </div>
            <div style={{ textAlign: 'right' }}>
              <Text strong style={{
                color: '#faad14',
                fontSize: '16px',
                fontWeight: 'bold'
              }}>
                {FormulaPageConsolidationCalculations.formatNumber(最终总和, 0, true)}
              </Text>
            </div>
          </div>
        </div>

        {/* 编辑按钮 */}
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          {isEditing ? (
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveManualInputs}
                size="small"
              >
                保存
              </Button>
              <Button
                onClick={() => setIsEditing(false)}
                size="small"
              >
                取消
              </Button>
            </Space>
          ) : (
            <Button
              type="dashed"
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
              size="small"
            >
              编辑手动输入
            </Button>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 标题和操作按钮 */}
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              📊 客户归纳汇总表 (公式配置页面)
            </Title>
            <Space>
              <Button 
                type="primary" 
                icon={<ExportOutlined />} 
                onClick={onExport}
                size="small"
              >
                导出Excel
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={onRefresh}
                loading={loading}
                size="small"
              >
                刷新数据
              </Button>
            </Space>
          </div>
        }
        style={{ marginBottom: '24px' }}
      >
        <Text type="secondary">
          将所有公式组中的同名客户和股东数据归纳汇总，按照特定计算逻辑显示收入、付出、余额等信息
        </Text>
      </Card>

      {/* 按客户名分组显示，每个客户有自己的归纳汇总表和汇总统计 */}
      {Object.keys(consolidatedByUser).length > 0 ? (
        Object.entries(consolidatedByUser).map(([customerName, customerRecords]) => (
          <div key={customerName} style={{ marginBottom: '32px' }}>
            {/* 客户归纳汇总表 */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{
                    background: '#52c41a',
                    color: 'white',
                    padding: '4px 12px',
                    borderRadius: '16px',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}>
                    🏢 客户: {customerName}
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    共 {customerRecords.length} 条记录
                  </Text>
                  <Text type="secondary" style={{ fontSize: '11px', color: '#666' }}>
                    (来自用户: {[...new Set(customerRecords.map(r => r.userName))].join(', ')})
                  </Text>
                </div>
              }
              style={{
                backgroundColor: '#fafafa',
                marginBottom: '16px'
              }}
              size="small"
            >
              <Table
                columns={getTableColumns()}
                dataSource={customerRecords}
                loading={loading}
                rowKey={(record) => `${customerName}_${record.userName}_${record.groupKey || record.originalKey}`}
                scroll={{ x: 'max-content' }}
                pagination={false}
                size="small"
                bordered
              />
            </Card>

            {/* 该客户的汇总统计 */}
            {renderCustomerSummary(customerName, customerRecords)}
          </div>
        ))
      ) : (
        <Card
          title="客户归纳汇总表"
          style={{ backgroundColor: '#fafafa' }}
        >
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#999'
          }}>
            暂无数据
          </div>
        </Card>
      )}
    </div>
  );
}
