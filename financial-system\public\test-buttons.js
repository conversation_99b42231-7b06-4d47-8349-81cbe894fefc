// 汇总表按钮功能测试脚本
// 在浏览器控制台中运行此脚本来测试按钮功能

console.log('🧪 开始测试汇总表按钮功能...');

// 测试函数：查找并点击汇总表按钮
function testSummaryTableButton() {
    console.log('🔍 查找汇总表按钮...');
    
    // 查找包含"查看汇总表"或"返回模块视图"文本的按钮
    const buttons = Array.from(document.querySelectorAll('button'));
    const summaryButton = buttons.find(btn => 
        btn.textContent.includes('查看汇总表') || 
        btn.textContent.includes('返回模块视图')
    );
    
    if (summaryButton) {
        console.log('✅ 找到汇总表按钮:', summaryButton.textContent);
        console.log('🖱️ 模拟点击按钮...');
        
        // 模拟点击
        summaryButton.click();
        
        // 等待一下再检查结果
        setTimeout(() => {
            console.log('📊 检查按钮状态变化...');
            console.log('当前按钮文本:', summaryButton.textContent);
            
            // 查找版本切换按钮
            const versionButton = buttons.find(btn => 
                btn.textContent.includes('切换到') || 
                btn.textContent.includes('v2.0') ||
                btn.textContent.includes('客户归纳')
            );
            
            if (versionButton) {
                console.log('✅ 找到版本切换按钮:', versionButton.textContent);
                return true;
            } else {
                console.log('❌ 未找到版本切换按钮');
                return false;
            }
        }, 500);
        
    } else {
        console.log('❌ 未找到汇总表按钮');
        console.log('页面上的所有按钮:');
        buttons.forEach((btn, index) => {
            console.log(`  ${index + 1}. "${btn.textContent.trim()}"`);
        });
        return false;
    }
}

// 测试函数：查找并点击版本切换按钮
function testVersionSwitchButton() {
    console.log('🔍 查找版本切换按钮...');
    
    const buttons = Array.from(document.querySelectorAll('button'));
    const versionButton = buttons.find(btn => 
        btn.textContent.includes('切换到') || 
        btn.textContent.includes('v2.0') ||
        btn.textContent.includes('客户归纳')
    );
    
    if (versionButton) {
        console.log('✅ 找到版本切换按钮:', versionButton.textContent);
        console.log('🖱️ 模拟点击版本切换按钮...');
        
        const originalText = versionButton.textContent;
        versionButton.click();
        
        setTimeout(() => {
            console.log('📊 检查版本切换结果...');
            console.log('原始文本:', originalText);
            console.log('当前文本:', versionButton.textContent);
            
            if (originalText !== versionButton.textContent) {
                console.log('✅ 版本切换成功！');
                return true;
            } else {
                console.log('❌ 版本切换可能失败');
                return false;
            }
        }, 500);
        
    } else {
        console.log('❌ 未找到版本切换按钮');
        return false;
    }
}

// 测试函数：检查页面内容变化
function checkPageContent() {
    console.log('🔍 检查页面内容...');
    
    // 查找汇总表相关的内容
    const summaryElements = [
        document.querySelector('[title*="汇总"]'),
        document.querySelector('[class*="summary"]'),
        document.querySelector('[class*="Summary"]'),
        document.querySelector('div:contains("汇总")')
    ].filter(el => el !== null);
    
    console.log(`📊 找到 ${summaryElements.length} 个汇总相关元素`);
    
    // 查找表格元素
    const tables = document.querySelectorAll('table');
    console.log(`📋 找到 ${tables.length} 个表格元素`);
    
    // 查找Ant Design组件
    const antdElements = document.querySelectorAll('[class*="ant-"]');
    console.log(`🐜 找到 ${antdElements.length} 个Ant Design元素`);
    
    return {
        summaryElements: summaryElements.length,
        tables: tables.length,
        antdElements: antdElements.length
    };
}

// 主测试函数
function runFullTest() {
    console.log('🚀 开始完整测试...');
    
    // 1. 检查初始页面状态
    console.log('\n1️⃣ 检查初始页面状态');
    const initialState = checkPageContent();
    console.log('初始状态:', initialState);
    
    // 2. 测试汇总表按钮
    console.log('\n2️⃣ 测试汇总表按钮');
    setTimeout(() => {
        testSummaryTableButton();
        
        // 3. 等待一下再测试版本切换
        setTimeout(() => {
            console.log('\n3️⃣ 测试版本切换按钮');
            testVersionSwitchButton();
            
            // 4. 检查最终状态
            setTimeout(() => {
                console.log('\n4️⃣ 检查最终页面状态');
                const finalState = checkPageContent();
                console.log('最终状态:', finalState);
                
                console.log('\n🎉 测试完成！');
            }, 1000);
        }, 1000);
    }, 500);
}

// 导出测试函数供控制台使用
window.testSummaryTable = {
    runFullTest,
    testSummaryTableButton,
    testVersionSwitchButton,
    checkPageContent
};

console.log('✅ 测试脚本已加载！');
console.log('💡 使用方法:');
console.log('  - testSummaryTable.runFullTest() - 运行完整测试');
console.log('  - testSummaryTable.testSummaryTableButton() - 测试汇总表按钮');
console.log('  - testSummaryTable.testVersionSwitchButton() - 测试版本切换按钮');
console.log('  - testSummaryTable.checkPageContent() - 检查页面内容');
