// 客户数据状态管理 - Zustand JavaScript版本
// 避免TypeScript版本冲突

import { create } from 'zustand';
import { FormulaEngine } from '../lib/calculations';

export const useCustomerStore = create((set, get) => ({
  // 状态
  customers: [],
  loading: false,
  error: null,
  selectedAgent: null,
  
  // Actions
  setCustomers: (customers) => set({ customers }),
  
  setLoading: (loading) => set({ loading }),
  
  setError: (error) => set({ error }),
  
  setSelectedAgent: (agent) => set({ selectedAgent: agent }),
  
  // 添加客户
  addCustomer: async (customerData) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
      
      if (response.ok) {
        const newCustomer = await response.json();
        set(state => ({ 
          customers: [...state.customers, newCustomer],
          loading: false 
        }));
        return newCustomer;
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || '添加客户失败');
      }
    } catch (error) {
      console.error('添加客户失败:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },
  
  // 更新客户
  updateCustomer: async (id, updates) => {
    // 乐观更新
    set(state => ({
      customers: state.customers.map(c => 
        c.id === id ? { ...c, ...updates } : c
      )
    }));
    
    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      
      if (response.ok) {
        const updatedCustomer = await response.json();
        set(state => ({
          customers: state.customers.map(c => 
            c.id === id ? updatedCustomer : c
          )
        }));
        return updatedCustomer;
      } else {
        // 回滚乐观更新
        const { customers } = get();
        const originalCustomer = customers.find(c => c.id === id);
        if (originalCustomer) {
          set(state => ({
            customers: state.customers.map(c => 
              c.id === id ? originalCustomer : c
            )
          }));
        }
        throw new Error('更新客户失败');
      }
    } catch (error) {
      console.error('更新客户失败:', error);
      set({ error: error.message });
      throw error;
    }
  },
  
  // 删除客户
  deleteCustomer: async (id) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        set(state => ({
          customers: state.customers.filter(c => c.id !== id),
          loading: false
        }));
      } else {
        throw new Error('删除客户失败');
      }
    } catch (error) {
      console.error('删除客户失败:', error);
      set({ error: error.message, loading: false });
      throw error;
    }
  },
  
  // 重新计算客户数据
  recalculateCustomer: async (id) => {
    try {
      const response = await fetch(`/api/customers/${id}/calculate`, {
        method: 'POST',
      });
      
      if (response.ok) {
        const calculatedCustomer = await response.json();
        set(state => ({
          customers: state.customers.map(c => 
            c.id === id ? calculatedCustomer : c
          )
        }));
        return calculatedCustomer;
      } else {
        throw new Error('重新计算失败');
      }
    } catch (error) {
      console.error('重新计算失败:', error);
      set({ error: error.message });
      throw error;
    }
  },
  
  // 本地计算客户数据（不保存到服务器）
  calculateCustomerLocal: (id, updates) => {
    set(state => {
      const customers = state.customers.map(customer => {
        if (customer.id === id) {
          const updatedCustomer = { ...customer, ...updates };
          const calculations = FormulaEngine.calculateCustomerData(updatedCustomer);
          return { ...updatedCustomer, ...calculations };
        }
        return customer;
      });
      return { customers };
    });
  },
  
  // 获取客户列表 - 临时修复版本，使用本地存储
  fetchCustomers: async (agentId = null) => {
    set({ loading: true, error: null });
    try {
      // 首先尝试API调用
      const url = agentId ? `/api/customers?agentId=${agentId}` : '/api/customers';
      const response = await fetch(url);

      if (response.ok) {
        const customers = await response.json();
        set({ customers, loading: false });
        return customers;
      } else {
        throw new Error('API调用失败，使用本地数据');
      }
    } catch (error) {
      console.warn('API调用失败，使用本地模拟数据:', error);

      // 使用本地模拟数据作为后备方案
      const mockCustomers = [
        {
          id: 1,
          name: '客户A',
          userName: '用户A',
          agentId: agentId || 5,
          moduleId: 'module1_1',
          data1: 10000,
          data2: 20000,
          data3: 0.15,
          data4: 0.012,
          data5: 15.5,
          data6: 1000,
          data7: 500,
          result1: 3180,
          result2: 8000,
          result3: 4500,
          result4: -3.5,
          result5: 1550,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 2,
          name: '客户B',
          userName: '用户B',
          agentId: agentId || 5,
          moduleId: 'module1_2',
          data1: 15000,
          data2: 25000,
          data3: 0.20,
          data4: 0.015,
          data5: 18.0,
          data6: 1200,
          data7: 600,
          result1: 4800,
          result2: 10000,
          result3: 8000,
          result4: -2.5,
          result5: 2700,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 3,
          name: '客户C',
          userName: '用户C',
          agentId: agentId || 5,
          moduleId: 'module1_3',
          data1: 8000,
          data2: 12000,
          data3: 0.10,
          data4: 0.008,
          data5: 12.0,
          data6: 800,
          data7: 400,
          result1: 2000,
          result2: 4800,
          result3: 2000,
          result4: -4.0,
          result5: 960,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      set({ customers: mockCustomers, loading: false, error: null });
      return mockCustomers;
    }
  },
  
  // 批量更新客户数据 - 临时修复版本，支持本地存储
  batchUpdateCustomers: async (updates) => {
    set({ loading: true, error: null });
    try {
      const response = await fetch('/api/customers/batch', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates }),
      });

      if (response.ok) {
        const updatedCustomers = await response.json();
        set({ customers: updatedCustomers, loading: false });
        return updatedCustomers;
      } else {
        throw new Error('API批量更新失败，使用本地更新');
      }
    } catch (error) {
      console.warn('API批量更新失败，使用本地更新:', error);

      // 本地批量更新作为后备方案
      set(state => {
        let updatedCustomers = [...state.customers];

        updates.forEach(update => {
          const index = updatedCustomers.findIndex(c => c.id === update.id);
          if (index !== -1) {
            updatedCustomers[index] = {
              ...updatedCustomers[index],
              ...update,
              updatedAt: new Date()
            };
          }
        });

        return {
          customers: updatedCustomers,
          loading: false,
          error: null
        };
      });

      // 返回更新后的客户数据
      const state = get();
      return state.customers;
    }
  },
  
  // 清除错误
  clearError: () => set({ error: null }),
  
  // 重置状态
  reset: () => set({
    customers: [],
    loading: false,
    error: null,
    selectedAgent: null
  }),
}));
