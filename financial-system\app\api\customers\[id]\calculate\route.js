// 客户计算API路由 - JavaScript版本
// 避免TypeScript版本冲突

import { NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { FormulaEngine, GlobalFormulaManager } from '../../../../../lib/calculations';

// 重新计算客户数据
export async function POST(request, { params }) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的客户ID' }, 
        { status: 400 }
      );
    }
    
    // 获取客户数据
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    if (!customer) {
      return NextResponse.json(
        { error: '客户不存在' }, 
        { status: 404 }
      );
    }
    
    // 获取请求体中的公式配置（可选）
    let customFormulas = {};
    try {
      const body = await request.json();
      customFormulas = body.formulas || {};
    } catch (error) {
      // 如果没有请求体或解析失败，使用默认公式
    }
    
    // 合并默认公式和自定义公式
    const formulas = {
      ...GlobalFormulaManager.defaultFormulas,
      ...customFormulas
    };
    
    // 执行基础计算
    const basicCalculations = FormulaEngine.calculateCustomerData(customer);
    
    // 执行公式计算
    const formulaResults = GlobalFormulaManager.applyFormulas(customer, formulas);
    
    // 合并所有计算结果
    const allCalculations = {
      ...basicCalculations,
      ...formulaResults
    };
    
    // 更新数据库
    const updatedCustomer = await prisma.customer.update({
      where: { id },
      data: {
        data6: allCalculations.data6 || basicCalculations.data6,
        data7: allCalculations.data7 || basicCalculations.data7,
        data8: allCalculations.data8 || basicCalculations.data8,
      },
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    // 如果有财务数据，也更新财务数据表
    if (customer.financialData.length > 0) {
      await prisma.financialData.updateMany({
        where: { customerId: id },
        data: {
          formulas: JSON.stringify(formulas),
          results: JSON.stringify(allCalculations),
        }
      });
    } else {
      // 如果没有财务数据，创建一条
      await prisma.financialData.create({
        data: {
          customerId: id,
          formulas: JSON.stringify(formulas),
          results: JSON.stringify(allCalculations),
        }
      });
    }
    
    // 返回更新后的客户数据和计算结果
    return NextResponse.json({
      customer: updatedCustomer,
      calculations: allCalculations,
      formulas: formulas,
      message: '计算完成'
    });
    
  } catch (error) {
    console.error('重新计算客户数据失败:', error);
    return NextResponse.json(
      { error: '重新计算失败' }, 
      { status: 500 }
    );
  }
}

// 获取客户计算结果
export async function GET(request, { params }) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的客户ID' }, 
        { status: 400 }
      );
    }
    
    // 获取客户数据和财务数据
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    if (!customer) {
      return NextResponse.json(
        { error: '客户不存在' }, 
        { status: 404 }
      );
    }
    
    // 获取最新的计算结果
    let calculations = {};
    let formulas = {};
    
    if (customer.financialData.length > 0) {
      const latestFinancialData = customer.financialData[customer.financialData.length - 1];
      try {
        calculations = JSON.parse(latestFinancialData.results || '{}');
        formulas = JSON.parse(latestFinancialData.formulas || '{}');
      } catch (error) {
        console.warn('解析财务数据失败:', error);
      }
    }
    
    // 如果没有计算结果，执行一次计算
    if (Object.keys(calculations).length === 0) {
      calculations = FormulaEngine.calculateCustomerData(customer);
      formulas = GlobalFormulaManager.defaultFormulas;
    }
    
    return NextResponse.json({
      customer,
      calculations,
      formulas,
      summary: FormulaEngine.calculateSummary([customer])
    });
    
  } catch (error) {
    console.error('获取客户计算结果失败:', error);
    return NextResponse.json(
      { error: '获取计算结果失败' }, 
      { status: 500 }
    );
  }
}
