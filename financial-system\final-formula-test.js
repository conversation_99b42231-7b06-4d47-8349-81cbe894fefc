// 最终公式计算验证测试
const { chromium } = require('playwright');

async function finalFormulaTest() {
  console.log('🎯 开始最终公式计算验证测试...\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 3000 // 更慢的速度便于观察
  });
  
  const page = await browser.newPage();
  
  try {
    // 访问页面
    console.log('📊 访问公式配置页面...');
    await page.goto('http://localhost:3000/formula-config');
    await page.waitForTimeout(5000);
    
    console.log('✅ 页面加载完成');
    
    // 添加客户行
    console.log('🔘 添加客户行...');
    const addButton = page.locator('button:has-text("添加客户")').first();
    await addButton.click();
    await page.waitForTimeout(3000);
    console.log('✅ 客户行添加成功');
    
    // 测试用例1: 正数测试
    console.log('\n🧪 测试用例1: 正数测试');
    console.log('📝 输入数据: data1=100, data2=2, data3=50, data4=1');
    
    // 查找并填写输入框
    const inputs = await page.$$('table input');
    console.log(`📋 找到 ${inputs.length} 个输入框`);
    
    if (inputs.length >= 6) { // 确保有足够的输入框
      try {
        // 跳过前两个输入框（客户名、用户名），从第3个开始是data1
        await inputs[2].fill('100'); // data1
        await page.waitForTimeout(1000);
        await inputs[3].fill('2');   // data2
        await page.waitForTimeout(1000);
        await inputs[4].fill('50');  // data3
        await page.waitForTimeout(1000);
        await inputs[5].fill('1');   // data4
        await page.waitForTimeout(1000);
        
        console.log('✅ 数据输入完成');
        
        // 触发计算
        await page.keyboard.press('Tab');
        await page.waitForTimeout(3000);
        console.log('⚡ 计算触发完成');
        
        // 获取计算结果
        const results = await page.evaluate(() => {
          const resultData = [];
          const strongElements = document.querySelectorAll('strong');
          strongElements.forEach((element, index) => {
            const text = element.textContent.trim();
            const num = parseFloat(text);
            if (!isNaN(num) && isFinite(num)) {
              resultData.push({ index, value: num, text });
            }
          });
          return resultData;
        });
        
        console.log('\n📈 页面计算结果:');
        results.forEach(result => {
          console.log(`   位置${result.index}: ${result.value}`);
        });
        
        // 验证公式1.1: (data1+data2+data4)+data3 = (100+2+1)+50 = 153
        const expected1_1 = 153;
        const found1_1 = results.find(r => Math.abs(r.value - expected1_1) < 0.01);
        
        if (found1_1) {
          console.log(`✅ 公式1.1验证成功: 预期=${expected1_1}, 实际=${found1_1.value} (位置${found1_1.index})`);
        } else {
          console.log(`❌ 公式1.1验证失败: 预期=${expected1_1}, 未找到匹配结果`);
        }
        
        // 验证公式2.1: data1*data2 = 100*2 = 200
        const expected2_1 = 200;
        const found2_1 = results.find(r => Math.abs(r.value - expected2_1) < 0.01);
        
        if (found2_1) {
          console.log(`✅ 公式2.1验证成功: 预期=${expected2_1}, 实际=${found2_1.value} (位置${found2_1.index})`);
        } else {
          console.log(`❌ 公式2.1验证失败: 预期=${expected2_1}, 未找到匹配结果`);
        }
        
        // 验证公式3.1: data1 = 100
        const expected3_1 = 100;
        const found3_1 = results.find(r => Math.abs(r.value - expected3_1) < 0.01);
        
        if (found3_1) {
          console.log(`✅ 公式3.1验证成功: 预期=${expected3_1}, 实际=${found3_1.value} (位置${found3_1.index})`);
        } else {
          console.log(`❌ 公式3.1验证失败: 预期=${expected3_1}, 未找到匹配结果`);
        }
        
      } catch (error) {
        console.log(`❌ 输入数据时出错: ${error.message}`);
      }
    } else {
      console.log('❌ 输入框数量不足');
    }
    
    // 等待一段时间再进行负数测试
    await page.waitForTimeout(3000);
    
    // 测试用例2: 负数测试
    console.log('\n🧪 测试用例2: 负数测试');
    console.log('📝 输入数据: data1=-100, data2=-2, data3=-50, data4=-1');
    
    try {
      // 清空并输入负数
      await inputs[2].fill('-100'); // data1
      await page.waitForTimeout(1000);
      await inputs[3].fill('-2');   // data2
      await page.waitForTimeout(1000);
      await inputs[4].fill('-50');  // data3
      await page.waitForTimeout(1000);
      await inputs[5].fill('-1');   // data4
      await page.waitForTimeout(1000);
      
      console.log('✅ 负数数据输入完成');
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(3000);
      console.log('⚡ 负数计算触发完成');
      
      // 获取负数计算结果
      const negativeResults = await page.evaluate(() => {
        const resultData = [];
        const strongElements = document.querySelectorAll('strong');
        strongElements.forEach((element, index) => {
          const text = element.textContent.trim();
          const num = parseFloat(text);
          if (!isNaN(num) && isFinite(num)) {
            resultData.push({ index, value: num, text });
          }
        });
        return resultData;
      });
      
      console.log('\n📈 负数测试页面计算结果:');
      negativeResults.forEach(result => {
        console.log(`   位置${result.index}: ${result.value}`);
      });
      
      // 验证负数公式1.1: (-100+-2+-1)+(-50) = -153
      const expectedNeg1_1 = -153;
      const foundNeg1_1 = negativeResults.find(r => Math.abs(r.value - expectedNeg1_1) < 0.01);
      
      if (foundNeg1_1) {
        console.log(`✅ 负数公式1.1验证成功: 预期=${expectedNeg1_1}, 实际=${foundNeg1_1.value} (位置${foundNeg1_1.index})`);
      } else {
        console.log(`❌ 负数公式1.1验证失败: 预期=${expectedNeg1_1}, 未找到匹配结果`);
      }
      
      // 验证负数公式2.1: (-100)*(-2) = 200
      const expectedNeg2_1 = 200;
      const foundNeg2_1 = negativeResults.find(r => Math.abs(r.value - expectedNeg2_1) < 0.01);
      
      if (foundNeg2_1) {
        console.log(`✅ 负数公式2.1验证成功: 预期=${expectedNeg2_1}, 实际=${foundNeg2_1.value} (位置${foundNeg2_1.index})`);
      } else {
        console.log(`❌ 负数公式2.1验证失败: 预期=${expectedNeg2_1}, 未找到匹配结果`);
      }
      
    } catch (error) {
      console.log(`❌ 负数测试时出错: ${error.message}`);
    }
    
    console.log('\n🎉 测试完成！');
    console.log('📊 测试总结:');
    console.log('   - 正数计算测试: 验证公式1.1, 2.1, 3.1');
    console.log('   - 负数计算测试: 验证负数处理的准确性');
    console.log('   - 页面交互测试: 验证数据输入和结果显示');
    
    console.log('\n🔍 浏览器保持打开状态，您可以手动验证结果...');
    console.log('📋 建议手动输入不同的数据来进一步测试公式计算');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 执行测试
finalFormulaTest().catch(console.error);
