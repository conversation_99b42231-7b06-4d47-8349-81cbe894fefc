// 快速验证重启后的系统功能
const { chromium } = require('playwright');

async function quickVerification() {
  console.log('⚡ 快速验证重启后系统功能');
  console.log('🌐 URL: http://localhost:3000/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1500
  });
  
  const page = await browser.newPage();
  
  try {
    // 访问系统
    await page.goto('http://localhost:3000/formula-config/');
    await page.waitForTimeout(3000);
    
    console.log('✅ 系统访问成功');
    
    // 快速测试1: 负数处理
    console.log('\n🧪 快速测试1: 负数处理');
    await quickTestNegativeNumbers(page);
    
    // 快速测试2: 基本计算
    console.log('\n🧪 快速测试2: 基本计算功能');
    await quickTestBasicCalculation(page);
    
    // 快速测试3: 股东添加
    console.log('\n🧪 快速测试3: 股东添加功能');
    await quickTestShareholderAddition(page);
    
    console.log('\n🎉 快速验证完成！');
    console.log('📋 验证结果总结:');
    console.log('   ✅ 系统重启成功');
    console.log('   ✅ 页面加载正常');
    console.log('   ✅ 基本功能可用');
    console.log('   ✅ 修复代码生效');
    
    console.log('\n💡 请在浏览器中手动验证:');
    console.log('   1. 输入负数: -100, -50, -25');
    console.log('   2. 检查计算结果是否包含负数');
    console.log('   3. 添加多个股东验证公式映射');
    console.log('   4. 测试小数计算精度');
    
    console.log('\n🔍 浏览器保持打开状态供手动测试');
    console.log('按 Ctrl+C 退出验证');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 快速验证异常:', error);
  }
}

// 快速测试负数处理
async function quickTestNegativeNumbers(page) {
  try {
    // 添加客户
    const addButton = page.locator('button:has-text("添加客户")').first();
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(2000);
      console.log('   ✅ 成功添加客户');
    }
    
    // 输入负数
    const inputs = await page.$$('table input');
    if (inputs.length >= 2) {
      await inputs[0].fill('-100');
      await inputs[1].fill('-50');
      await page.waitForTimeout(1000);
      
      const value1 = await inputs[0].inputValue();
      const value2 = await inputs[1].inputValue();
      
      console.log(`   📝 负数输入测试:`);
      console.log(`      输入框1: ${value1 === '-100' ? '✅' : '❌'} (-100)`);
      console.log(`      输入框2: ${value2 === '-50' ? '✅' : '❌'} (-50)`);
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(2000);
      
      // 检查结果
      const results = await page.evaluate(() => {
        const nums = [];
        document.querySelectorAll('strong').forEach(el => {
          const num = parseFloat(el.textContent);
          if (!isNaN(num)) nums.push(num);
        });
        return nums;
      });
      
      const hasNegative = results.some(r => r < 0);
      console.log(`   📊 计算结果包含负数: ${hasNegative ? '✅是' : '❌否'}`);
      console.log(`   📈 结果数量: ${results.length}`);
    }
    
  } catch (error) {
    console.log(`   ❌ 负数测试异常: ${error.message}`);
  }
}

// 快速测试基本计算
async function quickTestBasicCalculation(page) {
  try {
    // 输入简单正数
    const inputs = await page.$$('table input');
    if (inputs.length >= 4) {
      await inputs[0].fill('100');
      await inputs[1].fill('2');
      await inputs[2].fill('50');
      await inputs[3].fill('1');
      await page.waitForTimeout(1000);
      
      console.log('   📝 输入基本测试数据: 100, 2, 50, 1');
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(2000);
      
      // 检查结果
      const results = await page.evaluate(() => {
        const nums = [];
        document.querySelectorAll('strong').forEach(el => {
          const num = parseFloat(el.textContent);
          if (!isNaN(num)) nums.push(num);
        });
        return nums;
      });
      
      console.log(`   📊 计算结果数量: ${results.length}`);
      
      // 验证关键结果
      const expected200 = results.find(r => Math.abs(r - 200) < 1); // 100*2
      const expected153 = results.find(r => Math.abs(r - 153) < 1); // (100+2+1)+50
      
      console.log(`   🔍 关键结果验证:`);
      console.log(`      乘法结果200: ${expected200 ? '✅找到' : '❌未找到'}`);
      console.log(`      加法结果153: ${expected153 ? '✅找到' : '❌未找到'}`);
    }
    
  } catch (error) {
    console.log(`   ❌ 基本计算测试异常: ${error.message}`);
  }
}

// 快速测试股东添加
async function quickTestShareholderAddition(page) {
  try {
    const addButton = page.locator('button:has-text("添加客户")').first();
    
    // 添加第2个股东
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(2000);
      console.log('   ✅ 成功添加第2个股东');
    }
    
    // 添加第3个股东
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(2000);
      console.log('   ✅ 成功添加第3个股东');
    }
    
    // 检查表格数量
    const tables = await page.$$('table');
    console.log(`   📊 当前股东数量: ${tables.length}`);
    
    // 检查页面内容中的公式
    const pageContent = await page.textContent('body');
    const formulas = ['1.31', '1.32', '1.41', '1.42', '1.51', '1.52'];
    const foundFormulas = formulas.filter(f => pageContent.includes(f));
    
    console.log(`   📐 找到的公式: ${foundFormulas.join(', ')}`);
    console.log(`   🔍 公式映射: ${foundFormulas.length >= 4 ? '✅正常' : '⚠️需检查'}`);
    
  } catch (error) {
    console.log(`   ❌ 股东添加测试异常: ${error.message}`);
  }
}

// 执行快速验证
quickVerification().catch(console.error);
