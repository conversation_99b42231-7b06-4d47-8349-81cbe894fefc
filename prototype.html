<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理商盈亏跟踪系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page {
            display: none;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .page.active {
            display: block;
        }
        
        /* 登录页面样式 */
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            text-align: center;
        }
        
        .login-form {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .login-form h2 {
            margin-bottom: 30px;
            color: #2c3e50;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        /* 导航栏样式 */
        .navbar {
            background: #2c3e50;
            color: white;
            padding: 15px 0;
            margin-bottom: 20px;
        }
        
        .navbar .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navbar h1 {
            font-size: 24px;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 15px 8px;
            text-align: center;
            vertical-align: middle;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .data-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .data-table input {
            border: none;
            background: transparent;
            width: 100%;
            text-align: center;
            padding: 5px;
        }
        
        .data-table input:focus {
            background: #fff;
            border: 1px solid #3498db;
            border-radius: 3px;
        }
        
        /* 日期选择器样式 */
        .date-selector {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-selector label {
            font-weight: bold;
        }
        
        .date-selector input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        /* 报告链接样式 */
        .report-links {
            margin: 20px 0;
            display: flex;
            gap: 15px;
        }
        
        .report-link {
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .report-link:hover {
            background: #229954;
        }
        
        /* 总计样式 */
        .total-row {
            background: #e8f5e8 !important;
            font-weight: bold;
        }
        
        .summary-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .summary-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .summary-item label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-item input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
        
        /* 操作按钮样式 */
        .action-buttons {
            margin: 20px 0;
            display: flex;
            gap: 10px;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        /* 表格按钮样式优化 */
        .data-table .btn {
            padding: 8px 12px;
            font-size: 14px;
            margin: 2px;
        }

        /* 备注输入框样式 */
        .data-table input[type="text"][placeholder="备注"] {
            max-width: 200px;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="page active">
        <div class="login-container">
            <div class="login-form">
                <h2>代理商盈亏跟踪系统</h2>
                <div class="form-group">
                    <label for="username">账号:</label>
                    <input type="text" id="username" placeholder="请输入账号">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" placeholder="请输入密码">
                </div>
                <button class="btn" onclick="login()">进去</button>
            </div>
        </div>
    </div>

    <!-- 主仪表盘页面 -->
    <div id="dashboardPage" class="page">
        <div class="navbar">
            <div class="container">
                <h1>代理商盈亏跟踪系统</h1>
                <div class="nav-links">
                    <a href="#" onclick="showPage('dashboardPage')">主页</a>
                    <a href="#" onclick="showPage('weeklyProfitPage')">周利润表</a>
                    <a href="#" onclick="showPage('yearlyProfitPage')">年利润表</a>
                    <a href="#" onclick="logout()">退出</a>
                </div>
            </div>
        </div>
        
        <div class="container">
            <div class="date-selector">
                <label>日期:</label>
                <input type="date" id="currentDate" value="2025-07-24">
            </div>
            
            <h3>代理商数据管理</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>代理名/下客户</th>
                        <th>上周余额</th>
                        <th>减免</th>
                        <th>客户数据编辑表</th>
                        <th>备注</th>
                        <th>删除代理</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="text" value="代理A"></td>
                        <td><input type="number" value="10000"></td>
                        <td><input type="number" value="500"></td>
                        <td><button class="btn btn-secondary" onclick="showSummary('代理A')">客户数据编辑表</button></td>
                        <td><input type="text" placeholder="备注" maxlength="30"></td>
                        <td><button class="btn btn-danger" onclick="deleteAgent(this)">删除</button></td>
                    </tr>
                    <tr>
                        <td><input type="text" value="代理B"></td>
                        <td><input type="number" value="15000"></td>
                        <td><input type="number" value="800"></td>
                        <td><button class="btn btn-secondary" onclick="showSummary('代理B')">客户数据编辑表</button></td>
                        <td><input type="text" placeholder="备注" maxlength="30"></td>
                        <td><button class="btn btn-danger" onclick="deleteAgent(this)">删除</button></td>
                    </tr>
                    <tr>
                        <td><input type="text" value="代理C"></td>
                        <td><input type="number" value="8000"></td>
                        <td><input type="number" value="300"></td>
                        <td><button class="btn btn-secondary" onclick="showSummary('代理C')">客户数据编辑表</button></td>
                        <td><input type="text" placeholder="备注" maxlength="30"></td>
                        <td><button class="btn btn-danger" onclick="deleteAgent(this)">删除</button></td>
                    </tr>
                </tbody>
            </table>
            
            <div class="action-buttons">
                <button class="btn btn-success" onclick="addRow()">添加行</button>
                <button class="btn btn-secondary" onclick="saveData()">保存数据</button>
            </div>
            
            <div class="report-links">
                <a href="#" class="report-link" onclick="showPage('weeklyProfitPage')">公司周利润表</a>
                <a href="#" class="report-link" onclick="showPage('yearlyProfitPage')">公司年利润表</a>
            </div>
        </div>
    </div>

    <!-- 公司上周利润表页面 -->
    <div id="weeklyProfitPage" class="page">
        <div class="navbar">
            <div class="container">
                <h1>公司周利润表</h1>
                <div class="nav-links">
                    <a href="#" onclick="showPage('dashboardPage')">主页</a>
                    <a href="#" onclick="showPage('weeklyProfitPage')">周利润表</a>
                    <a href="#" onclick="showPage('yearlyProfitPage')">年利润表</a>
                    <a href="#" onclick="logout()">退出</a>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="date-selector">
                <label>日期范围:</label>
                <input type="text" value="7.22-7.28" readonly>
            </div>

            <h3>公司周利润表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>代理名/下客户</th>
                        <th>第1期</th>
                        <th>第2期</th>
                        <th>第3期</th>
                        <th>第4期</th>
                        <th>第5期</th>
                        <th>第6期</th>
                        <th>第7期</th>
                        <th>第8期</th>
                        <th>第9期</th>
                        <th>第10期</th>
                        <th>第11期</th>
                        <th>第12期</th>
                        <th>合计</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>代理A</td>
                        <td>1200</td>
                        <td>1500</td>
                        <td>800</td>
                        <td>2000</td>
                        <td>1800</td>
                        <td>1600</td>
                        <td>1400</td>
                        <td>1700</td>
                        <td>1300</td>
                        <td>1900</td>
                        <td>1100</td>
                        <td>1600</td>
                        <td class="total-row">18900</td>
                    </tr>
                    <tr>
                        <td>代理B</td>
                        <td>2200</td>
                        <td>2500</td>
                        <td>1800</td>
                        <td>3000</td>
                        <td>2800</td>
                        <td>2600</td>
                        <td>2400</td>
                        <td>2700</td>
                        <td>2300</td>
                        <td>2900</td>
                        <td>2100</td>
                        <td>2600</td>
                        <td class="total-row">29900</td>
                    </tr>
                    <tr>
                        <td>代理C</td>
                        <td>800</td>
                        <td>1000</td>
                        <td>600</td>
                        <td>1200</td>
                        <td>1100</td>
                        <td>900</td>
                        <td>700</td>
                        <td>1000</td>
                        <td>800</td>
                        <td>1100</td>
                        <td>600</td>
                        <td>900</td>
                        <td class="total-row">10700</td>
                    </tr>
                    <tr class="total-row">
                        <td><strong>总计</strong></td>
                        <td><strong>4200</strong></td>
                        <td><strong>5000</strong></td>
                        <td><strong>3200</strong></td>
                        <td><strong>6200</strong></td>
                        <td><strong>5700</strong></td>
                        <td><strong>5100</strong></td>
                        <td><strong>4500</strong></td>
                        <td><strong>5400</strong></td>
                        <td><strong>4400</strong></td>
                        <td><strong>5900</strong></td>
                        <td><strong>3800</strong></td>
                        <td><strong>5100</strong></td>
                        <td><strong>59500</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 公司年利润表页面 -->
    <div id="yearlyProfitPage" class="page">
        <div class="navbar">
            <div class="container">
                <h1>公司年利润表</h1>
                <div class="nav-links">
                    <a href="#" onclick="showPage('dashboardPage')">主页</a>
                    <a href="#" onclick="showPage('weeklyProfitPage')">周利润表</a>
                    <a href="#" onclick="showPage('yearlyProfitPage')">年利润表</a>
                    <a href="#" onclick="logout()">退出</a>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="date-selector">
                <label>日期范围:</label>
                <input type="text" value="7.22-7.28" readonly>
            </div>

            <h3>公司年利润表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>代理名/下客户</th>
                        <th>余额</th>
                        <th>减免</th>
                        <th>本周利润</th>
                        <th>合计</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>代理A</td>
                        <td>10000</td>
                        <td>500</td>
                        <td>18900</td>
                        <td class="total-row">28400</td>
                    </tr>
                    <tr>
                        <td>代理B</td>
                        <td>15000</td>
                        <td>800</td>
                        <td>29900</td>
                        <td class="total-row">44100</td>
                    </tr>
                    <tr>
                        <td>代理C</td>
                        <td>8000</td>
                        <td>300</td>
                        <td>10700</td>
                        <td class="total-row">18400</td>
                    </tr>
                    <tr class="total-row">
                        <td><strong>总计</strong></td>
                        <td><strong>33000</strong></td>
                        <td><strong>1600</strong></td>
                        <td><strong>59500</strong></td>
                        <td><strong>90900</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 汇总表页面 -->
    <div id="summaryPage" class="page">
        <div class="navbar">
            <div class="container">
                <h1 id="summaryTitle">客户数据编辑表 - 代理名：下</h1>
                <div class="nav-links">
                    <a href="#" onclick="showPage('dashboardPage')">主页</a>
                    <a href="#" onclick="showPage('weeklyProfitPage')">周利润表</a>
                    <a href="#" onclick="showPage('yearlyProfitPage')">年利润表</a>
                    <a href="#" onclick="logout()">退出</a>
                </div>
            </div>
        </div>

        <div class="container">
            <h3>详细客户数据编辑表</h3>

            <div class="action-buttons">
                <button class="btn btn-success" onclick="autoGenerate()">自动生成</button>
                <button class="btn btn-success" onclick="addSummaryRow()">+ 添加行</button>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>客户名</th>
                        <th>用户名</th>
                        <th>数据填写1</th>
                        <th>数据填写2</th>
                        <th>数据填写3</th>
                        <th>百分比</th>
                        <th>公式填写1</th>
                        <th>公式填写2</th>
                        <th>计算结果1</th>
                        <th>计算结果2</th>
                        <th>总和</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="text" value="啊"></td>
                        <td><input type="text" value="用户1"></td>
                        <td><input type="number" value="1000" onchange="calculateRow(this)"></td>
                        <td><input type="number" value="2000" onchange="calculateRow(this)"></td>
                        <td><input type="number" value="1500" onchange="calculateRow(this)"></td>
                        <td><input type="text" value="15%" readonly></td>
                        <td><input type="text" value="A*B" placeholder="输入公式"></td>
                        <td><input type="text" value="C*0.1" placeholder="输入公式"></td>
                        <td><input type="number" value="2000000" readonly></td>
                        <td><input type="number" value="150" readonly></td>
                        <td><input type="number" value="4500" readonly></td>
                    </tr>
                    <tr>
                        <td><input type="text" value="伯"></td>
                        <td><input type="text" value="用户2"></td>
                        <td><input type="number" value="800" onchange="calculateRow(this)"></td>
                        <td><input type="number" value="1200" onchange="calculateRow(this)"></td>
                        <td><input type="number" value="900" onchange="calculateRow(this)"></td>
                        <td><input type="text" value="12%" readonly></td>
                        <td><input type="text" value="A*B" placeholder="输入公式"></td>
                        <td><input type="text" value="C*0.1" placeholder="输入公式"></td>
                        <td><input type="number" value="960000" readonly></td>
                        <td><input type="number" value="90" readonly></td>
                        <td><input type="number" value="2900" readonly></td>
                    </tr>
                    <tr>
                        <td><input type="text" value="丙"></td>
                        <td><input type="text" value="用户3"></td>
                        <td><input type="number" value="1200" onchange="calculateRow(this)"></td>
                        <td><input type="number" value="1800" onchange="calculateRow(this)"></td>
                        <td><input type="number" value="1000" onchange="calculateRow(this)"></td>
                        <td><input type="text" value="18%" readonly></td>
                        <td><input type="text" value="A*B" placeholder="输入公式"></td>
                        <td><input type="text" value="C*0.1" placeholder="输入公式"></td>
                        <td><input type="number" value="2160000" readonly></td>
                        <td><input type="number" value="100" readonly></td>
                        <td><input type="number" value="4000" readonly></td>
                    </tr>
                </tbody>
            </table>

            <div class="summary-section">
                <h4>公式填入口</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <label>公式1</label>
                        <input type="text" id="formula1" placeholder="输入公式，如：A*B" value="A*B">
                    </div>
                    <div class="summary-item">
                        <label>公式2</label>
                        <input type="text" id="formula2" placeholder="输入公式，如：C*0.1" value="C*0.1">
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-success" onclick="generateResults()">点击生成结果，不点为空白</button>
                <button class="btn btn-secondary" onclick="generateSummaryReport()">点击出来汇总表</button>
                <button class="btn" onclick="addSummaryRow()">添加客户行</button>
            </div>

            <div class="summary-section">
                <h4>汇总统计</h4>
                <div class="summary-grid">
                    <div class="summary-item">
                        <label>收入</label>
                        <input type="number" id="totalIncome" value="50000" readonly>
                    </div>
                    <div class="summary-item">
                        <label>付出</label>
                        <input type="number" id="totalExpense" value="15000" readonly>
                    </div>
                    <div class="summary-item">
                        <label>收付现金</label>
                        <input type="number" id="cashFlow" value="35000" readonly>
                    </div>
                    <div class="summary-item">
                        <label>上周余额</label>
                        <input type="number" id="lastWeekBalance" value="10000" readonly>
                    </div>
                    <div class="summary-item">
                        <label>数量</label>
                        <input type="number" id="quantity" value="100">
                    </div>
                    <div class="summary-item">
                        <label>价格</label>
                        <input type="number" id="price" value="500">
                    </div>
                    <div class="summary-item">
                        <label>总和</label>
                        <input type="number" id="totalSum" value="50000" readonly>
                    </div>
                    <div class="summary-item">
                        <label>所有总和</label>
                        <input type="number" id="grandTotal" value="95000" readonly>
                    </div>
                </div>
            </div>

            <div class="summary-section">
                <h4>月度/年度明细</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>月份总计</th>
                            <th>年总</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>计算结果1</td>
                            <td>5120000</td>
                            <td>61440000</td>
                        </tr>
                        <tr>
                            <td>计算结果2</td>
                            <td>340</td>
                            <td>4080</td>
                        </tr>
                        <tr class="total-row">
                            <td><strong>总计</strong></td>
                            <td><strong>5120340</strong></td>
                            <td><strong>61444080</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示指定页面
            document.getElementById(pageId).classList.add('active');
        }

        // 登录功能
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username && password) {
                // 简单验证，实际应用中需要后端验证
                if (username === 'admin' && password === '123456') {
                    showPage('dashboardPage');
                    alert('登录成功！');
                } else {
                    alert('账号或密码错误！');
                }
            } else {
                alert('请输入账号和密码！');
            }
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                showPage('loginPage');
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
            }
        }

        // 显示汇总表
        function showSummary(agentName) {
            document.getElementById('summaryTitle').textContent = `客户数据编辑表 - 代理名：${agentName}`;
            showPage('summaryPage');
        }

        // 删除代理功能
        function deleteAgent(button) {
            if (confirm('确定要删除这个代理吗？')) {
                const row = button.closest('tr');
                row.remove();
                alert('代理已删除！');
            }
        }

        // 自动生成功能
        function autoGenerate() {
            alert('自动生成功能已执行！数据已自动填充到第一页。');
            // 这里可以添加自动生成逻辑
        }

        // 添加行功能
        function addRow() {
            const table = document.querySelector('#dashboardPage .data-table tbody');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="text" placeholder="代理名"></td>
                <td><input type="number" placeholder="0"></td>
                <td><input type="number" placeholder="0"></td>
                <td><button class="btn btn-secondary" onclick="showSummary('新代理')">客户数据编辑表</button></td>
                <td><input type="text" placeholder="备注" maxlength="30"></td>
                <td><button class="btn btn-danger" onclick="deleteAgent(this)">删除</button></td>
            `;
        }

        // 保存数据功能
        function saveData() {
            alert('数据已保存！（这里应该连接到后端API）');
        }

        // 计算行数据
        function calculateRow(element) {
            const row = element.closest('tr');
            const inputs = row.querySelectorAll('input[type="number"]');

            // 简单的计算示例
            if (inputs.length >= 3) {
                const val1 = parseFloat(inputs[0].value) || 0;
                const val2 = parseFloat(inputs[1].value) || 0;
                const val3 = parseFloat(inputs[2].value) || 0;

                // 计算百分比
                const percentage = ((val1 + val2 + val3) / 10000 * 100).toFixed(1) + '%';
                row.querySelector('input[readonly]').value = percentage;

                // 更新计算结果
                const result1Input = row.querySelectorAll('input[readonly]')[1];
                const result2Input = row.querySelectorAll('input[readonly]')[2];
                const totalInput = row.querySelectorAll('input[readonly]')[3];

                if (result1Input) result1Input.value = val1 * val2;
                if (result2Input) result2Input.value = val3 * 0.1;
                if (totalInput) totalInput.value = val1 + val2 + val3;
            }
        }

        // 生成结果
        function generateResults() {
            alert('结果已生成！所有计算已完成。');
            // 这里可以添加更复杂的计算逻辑
        }

        // 生成汇总报告
        function generateSummaryReport() {
            alert('汇总报告已生成！');
            // 这里可以添加报告生成逻辑
        }

        // 添加汇总行
        function addSummaryRow() {
            const table = document.querySelector('#summaryPage .data-table tbody');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="text" placeholder="客户名"></td>
                <td><input type="text" placeholder="用户名"></td>
                <td><input type="number" placeholder="0" onchange="calculateRow(this)"></td>
                <td><input type="number" placeholder="0" onchange="calculateRow(this)"></td>
                <td><input type="number" placeholder="0" onchange="calculateRow(this)"></td>
                <td><input type="text" readonly></td>
                <td><input type="text" placeholder="输入公式"></td>
                <td><input type="text" placeholder="输入公式"></td>
                <td><input type="number" readonly></td>
                <td><input type="number" readonly></td>
                <td><input type="number" readonly></td>
            `;
        }

        // 键盘事件处理
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const currentPage = document.querySelector('.page.active');
                if (currentPage.id === 'loginPage') {
                    login();
                }
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前日期
            const today = new Date().toISOString().split('T')[0];
            const dateInput = document.getElementById('currentDate');
            if (dateInput) {
                dateInput.value = today;
            }

            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
