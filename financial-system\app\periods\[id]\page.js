'use client';

// 日期编辑页面 - JavaScript版本
// 避免TypeScript版本冲突

import { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { 
  Layout, 
  Card, 
  DatePicker, 
  Button, 
  Space,
  Row,
  Col,
  message,
  Divider,
  Typography,
  Form,
  Input
} from 'antd';
import { 
  ArrowLeftOutlined,
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useAgentStore } from '../../../store/agentStore';
import dayjs from 'dayjs';

const { Header, Content } = Layout;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

export default function PeriodEditPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const agentId = parseInt(params.id);
  const periodType = searchParams.get('type') || '1-3'; // 1-3期 或 4-7期
  
  const { getAgentById, updateAgent } = useAgentStore();
  const [loading, setLoading] = useState(false);
  const [periods, setPeriods] = useState([]);

  // 获取代理商信息
  const agent = getAgentById(agentId);

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    
    // 初始化期间数据
    initializePeriods();
  }, [router, periodType]);

  // 初始化期间数据
  const initializePeriods = () => {
    const periodCount = periodType === '1-3' ? 3 : 4; // 1-3期有3个子期，4-7期有4个子期
    const startPeriod = periodType === '1-3' ? 1 : 4;
    
    const initialPeriods = Array.from({ length: periodCount }, (_, index) => ({
      id: startPeriod + index,
      name: `第${startPeriod + index}期`,
      dateRange: [dayjs(), dayjs().add(7, 'day')], // 默认一周
      description: `${periodType}期间的第${index + 1}个子期`,
      isActive: true,
    }));
    
    setPeriods(initialPeriods);
  };

  // 添加新期间
  const handleAddPeriod = () => {
    const newPeriod = {
      id: periods.length + 1,
      name: `第${periods.length + 1}期`,
      dateRange: [dayjs(), dayjs().add(7, 'day')],
      description: `新增期间`,
      isActive: true,
    };
    setPeriods([...periods, newPeriod]);
  };

  // 删除期间
  const handleDeletePeriod = (periodId) => {
    setPeriods(periods.filter(p => p.id !== periodId));
    message.success('删除期间成功！');
  };

  // 更新期间数据
  const handleUpdatePeriod = (periodId, field, value) => {
    setPeriods(periods.map(p => 
      p.id === periodId ? { ...p, [field]: value } : p
    ));
  };

  // 保存期间配置
  const handleSavePeriods = async () => {
    setLoading(true);
    try {
      // 这里应该调用API保存期间配置
      // 暂时更新到代理商数据中
      const periodData = {
        [`${periodType}Periods`]: periods
      };
      
      await updateAgent(agentId, periodData);
      message.success('期间配置保存成功！');
    } catch (error) {
      message.error('保存失败！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => router.push('/dashboard')}
            style={{ marginRight: '16px' }}
          >
            返回
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            日期编辑 - {agent?.name || '未知代理商'} ({periodType}期)
          </Title>
        </div>
        <Space>
          <Button 
            icon={<PlusOutlined />}
            onClick={handleAddPeriod}
          >
            添加期间
          </Button>
          <Button 
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={handleSavePeriods}
          >
            保存配置
          </Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        {/* 期间说明 */}
        <Card title="期间配置说明" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Text strong>当前配置类型：</Text>
              <Text style={{ marginLeft: '8px', color: '#1890ff' }}>
                {periodType === '1-3' ? '1-3期（通常包含3个子期）' : '4-7期（通常包含4个子期）'}
              </Text>
            </Col>
            <Col span={12}>
              <Text strong>配置说明：</Text>
              <Text style={{ marginLeft: '8px' }}>
                每个期间对应一个具体的日期区间，用于数据计算和汇总
              </Text>
            </Col>
          </Row>
          <Divider />
          <Text type="secondary">
            • 每一次就是一个周期，每个日期范围为一个独立的计算上下文<br/>
            • 期间的日期配置将影响汇总表中的数据计算<br/>
            • 可以根据业务需要自定义期间数量和日期范围
          </Text>
        </Card>

        {/* 期间配置列表 */}
        <Row gutter={[16, 16]}>
          {periods.map((period, index) => (
            <Col span={24} key={period.id}>
              <Card 
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{period.name}</span>
                    <Button 
                      size="small" 
                      danger 
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeletePeriod(period.id)}
                    >
                      删除
                    </Button>
                  </div>
                }
                style={{ 
                  border: period.isActive ? '2px solid #1890ff' : '1px solid #d9d9d9',
                  backgroundColor: period.isActive ? '#f6ffed' : '#fff'
                }}
              >
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Form.Item label="期间名称">
                      <Input 
                        value={period.name}
                        onChange={(e) => handleUpdatePeriod(period.id, 'name', e.target.value)}
                        placeholder="输入期间名称"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="日期范围">
                      <RangePicker 
                        value={period.dateRange}
                        onChange={(dates) => handleUpdatePeriod(period.id, 'dateRange', dates)}
                        format="YYYY-MM-DD"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label="期间描述">
                      <Input 
                        value={period.description}
                        onChange={(e) => handleUpdatePeriod(period.id, 'description', e.target.value)}
                        placeholder="输入期间描述"
                      />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Divider />
                
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Text strong>计算周期：</Text>
                    <Text style={{ marginLeft: '8px' }}>
                      {period.dateRange[0]?.format('YYYY-MM-DD')} 至 {period.dateRange[1]?.format('YYYY-MM-DD')}
                    </Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>天数：</Text>
                    <Text style={{ marginLeft: '8px' }}>
                      {period.dateRange[1]?.diff(period.dateRange[0], 'day') + 1} 天
                    </Text>
                  </Col>
                </Row>
              </Card>
            </Col>
          ))}
        </Row>

        {/* 操作说明 */}
        <Card title="操作说明" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <h4>期间配置功能：</h4>
              <ul>
                <li><strong>添加期间</strong>：点击"添加期间"按钮新增配置</li>
                <li><strong>编辑期间</strong>：直接修改期间名称、日期范围和描述</li>
                <li><strong>删除期间</strong>：点击期间卡片右上角的删除按钮</li>
                <li><strong>保存配置</strong>：点击"保存配置"按钮保存所有更改</li>
              </ul>
            </Col>
            <Col span={12}>
              <h4>注意事项：</h4>
              <ul>
                <li><strong>日期范围</strong>：确保日期范围合理，不要重叠</li>
                <li><strong>期间顺序</strong>：期间将按照创建顺序进行计算</li>
                <li><strong>数据影响</strong>：修改期间配置会影响汇总表的计算结果</li>
                <li><strong>保存提醒</strong>：修改后请及时保存配置</li>
              </ul>
            </Col>
          </Row>
        </Card>
      </Content>
    </Layout>
  );
}
