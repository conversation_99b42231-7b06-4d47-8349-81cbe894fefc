# Page snapshot

```yaml
- button "arrow-left 返回仪表盘":
  - img "arrow-left"
  - text: 返回仪表盘
- heading "🧮 界面3 - 公式配置与数据展示" [level=2]
- text: 动态公式编辑器 | 实时计算 | 股东信息管理 系统运行中 📊 公式配置网格 点击任意公式卡片进行编辑 公式1.1
- code: (data1+data2+data4)+...
- text: 公式1.2
- code: (data1+data2+data4)+...
- text: 公式1.31
- code: data1+data2+data3
- text: 公式1.32
- code: data1+data2+data4
- text: 公式1.41
- code: data1+data2+data3+da...
- text: 公式1.42
- code: data1+data2+data3+da...
- text: 公式1.51
- code: data1+data2+data3+da...
- text: 公式1.52
- code: data1+data2+data3+da...
- text: 公式2.1
- code: data1*data2
- text: 公式2.2
- code: data2*data4
- text: 公式2.31
- code: data1*data2*data3
- text: 公式2.32
- code: data1*data2*data4
- text: 公式2.41
- code: data1*data2*data3*da...
- text: 公式2.42
- code: data1*data2*data3*da...
- text: 公式2.51
- code: data1*data2*data3*da...
- text: 公式2.52
- code: data1*data2*data3*da...
- text: 公式3.1
- code: data1
- text: 公式3.2
- code: data2*data4
- text: 公式3.3 点击编辑公式 ✏️ 公式3.4 点击编辑公式 ✏️ 公式4.1
- code: data1/data2
- text: 公式4.2
- code: data2/data4
- text: 公式4.31
- code: data1/data2/data3
- text: 公式4.32
- code: data1/data2/data4
- text: 公式4.41
- code: data1/data2/data3/da...
- text: 公式4.42
- code: data1/data2/data3/da...
- text: 公式4.51
- code: data1/data2/data3/da...
- text: 公式4.52
- code: data1/data2/data3/da...
- text: 公式5.1
- code: data1-data2
- text: 公式5.2
- code: data2-data4
- text: 公式5.31
- code: data1-data2-data3
- text: 公式5.32
- code: data1-data2-data4
- text: 公式6.1
- code: data1^2
- text: 公式6.2
- code: data2^2
- text: 公式6.3
- code: data3^2
- text: 公式6.41
- code: data1^data2
- text: 公式6.42
- code: data2^data3
- text: 公式6.43
- code: data3^data4
- text: 公式7.1
- code: data1
- text: 公式7.2
- code: data2*data4
- text: 公式7.3
- code: data3+data5
- text: "📈 总公式数: 41 ✅ 已配置: 39 ⚪ 空白: 2"
- button "save 💾 保存值入汇总表":
  - img "save"
  - text: 💾 保存值入汇总表
- button "📊 汇总表"
- text: 📋 公式组 1 3 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 数据5 数据6 数据7 结果1 结果2 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "数据5"
      - columnheader "数据6"
      - columnheader "数据7"
      - columnheader "结果1"
      - columnheader "结果2"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "张三公司 用户A 100 2 50 1 25 80 3 200 150 股东信息 plus 添加股东 张三公司 delete 删除 Increase Value Decrease Value 0.5 152 103 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "张三公司":
        - textbox "输入客户名称": 张三公司
      - cell "用户A":
        - textbox "输入用户名称": 用户A
      - cell "100":
        - textbox "0": "100"
      - cell "2":
        - textbox "0": "2"
      - cell "50":
        - textbox "支持3位小数": "50"
      - cell "1":
        - textbox "支持8位小数": "1"
      - cell "25":
        - textbox "0": "25"
      - cell "80":
        - textbox "0": "80"
      - cell "3":
        - textbox "0": "3"
      - cell "200":
        - strong: "200"
      - cell "150":
        - strong: "150"
      - cell "股东信息 plus 添加股东 张三公司 delete 删除 Increase Value Decrease Value 0.5 152 103 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - table:
          - rowgroup:
            - row "张三公司 delete 删除":
              - cell "张三公司":
                - textbox "输入股东姓名": 张三公司
              - cell "delete 删除":
                - button "delete 删除":
                  - img "delete"
                  - text: 删除
            - row "Increase Value Decrease Value 0.5 152 103":
              - cell "Increase Value Decrease Value 0.5":
                - button "Increase Value":
                  - img "up"
                - button "Decrease Value":
                  - img "down"
                - spinbutton "0.1": "0.50"
              - cell "152":
                - strong: "152"
              - cell "103":
                - strong: "103"
              - cell
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
    - row "0 0 0 0 0 0 0 0 0 股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell:
        - textbox "输入客户名称"
      - cell:
        - textbox "输入用户名称"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "支持3位小数": "0"
      - cell "0":
        - textbox "支持8位小数": "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - strong: "0"
      - cell "0":
        - strong: "0"
      - cell "股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - text: 👥 暂无股东信息 点击上方"添加股东"按钮开始添加
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
    - row "0 0 0 0 0 0 0 0 0 股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell:
        - textbox "输入客户名称"
      - cell:
        - textbox "输入用户名称"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "支持3位小数": "0"
      - cell "0":
        - textbox "支持8位小数": "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - strong: "0"
      - cell "0":
        - strong: "0"
      - cell "股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - text: 👥 暂无股东信息 点击上方"添加股东"按钮开始添加
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- text: 📋 公式组 2 1 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 数据5 数据6 数据7 结果1 结果2 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "数据5"
      - columnheader "数据6"
      - columnheader "数据7"
      - columnheader "结果1"
      - columnheader "结果2"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "张三公司 用户A 150 1.5 30 2 15 60 2 300 120 股东信息 plus 添加股东 张三公司 delete 删除 Increase Value Decrease Value 0.3 181.5 153.5 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "张三公司":
        - textbox "输入客户名称": 张三公司
      - cell "用户A":
        - textbox "输入用户名称": 用户A
      - cell "150":
        - textbox "0": "150"
      - cell "1.5":
        - textbox "0": "1.5"
      - cell "30":
        - textbox "支持3位小数": "30"
      - cell "2":
        - textbox "支持8位小数": "2"
      - cell "15":
        - textbox "0": "15"
      - cell "60":
        - textbox "0": "60"
      - cell "2":
        - textbox "0": "2"
      - cell "300":
        - strong: "300"
      - cell "120":
        - strong: "120"
      - cell "股东信息 plus 添加股东 张三公司 delete 删除 Increase Value Decrease Value 0.3 181.5 153.5 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - table:
          - rowgroup:
            - row "张三公司 delete 删除":
              - cell "张三公司":
                - textbox "输入股东姓名": 张三公司
              - cell "delete 删除":
                - button "delete 删除":
                  - img "delete"
                  - text: 删除
            - row "Increase Value Decrease Value 0.3 181.5 153.5":
              - cell "Increase Value Decrease Value 0.3":
                - button "Increase Value":
                  - img "up"
                - button "Decrease Value":
                  - img "down"
                - spinbutton "0.1": "0.30"
              - cell "181.5":
                - strong: "181.5"
              - cell "153.5":
                - strong: "153.5"
              - cell
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- text: 📋 公式组 3 1 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 结果1 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "结果1"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "李四公司 用户B 200 3 40 1.5 450 股东信息 plus 添加股东 李四公司 delete 删除 Increase Value Decrease Value 0.8 243 204.5 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "李四公司":
        - textbox "输入客户名称": 李四公司
      - cell "用户B":
        - textbox "输入用户名称": 用户B
      - cell "200":
        - textbox "0": "200"
      - cell "3":
        - textbox "0": "3"
      - cell "40":
        - textbox "支持3位小数": "40"
      - cell "1.5":
        - textbox "支持8位小数": "1.5"
      - cell "450":
        - strong: "450"
      - cell "股东信息 plus 添加股东 李四公司 delete 删除 Increase Value Decrease Value 0.8 243 204.5 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - table:
          - rowgroup:
            - row "李四公司 delete 删除":
              - cell "李四公司":
                - textbox "输入股东姓名": 李四公司
              - cell "delete 删除":
                - button "delete 删除":
                  - img "delete"
                  - text: 删除
            - row "Increase Value Decrease Value 0.8 243 204.5":
              - cell "Increase Value Decrease Value 0.8":
                - button "Increase Value":
                  - img "up"
                - button "Decrease Value":
                  - img "down"
                - spinbutton "0.1": "0.80"
              - cell "243":
                - strong: "243"
              - cell "204.5":
                - strong: "204.5"
              - cell
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- text: 📋 公式组 4 1 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 数据5 数据6 数据7 结果1 结果2 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "数据5"
      - columnheader "数据6"
      - columnheader "数据7"
      - columnheader "结果1"
      - columnheader "结果2"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "王五公司 用户B 80 2.5 35 1 18 70 4 200 280 股东信息 plus 添加股东 王五公司 delete 删除 Increase Value Decrease Value 0.4 117.5 83.5 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "王五公司":
        - textbox "输入客户名称": 王五公司
      - cell "用户B":
        - textbox "输入用户名称": 用户B
      - cell "80":
        - textbox "0": "80"
      - cell "2.5":
        - textbox "0": "2.5"
      - cell "35":
        - textbox "支持3位小数": "35"
      - cell "1":
        - textbox "支持8位小数": "1"
      - cell "18":
        - textbox "0": "18"
      - cell "70":
        - textbox "0": "70"
      - cell "4":
        - textbox "0": "4"
      - cell "200":
        - strong: "200"
      - cell "280":
        - strong: "280"
      - cell "股东信息 plus 添加股东 王五公司 delete 删除 Increase Value Decrease Value 0.4 117.5 83.5 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - table:
          - rowgroup:
            - row "王五公司 delete 删除":
              - cell "王五公司":
                - textbox "输入股东姓名": 王五公司
              - cell "delete 删除":
                - button "delete 删除":
                  - img "delete"
                  - text: 删除
            - row "Increase Value Decrease Value 0.4 117.5 83.5":
              - cell "Increase Value Decrease Value 0.4":
                - button "Increase Value":
                  - img "up"
                - button "Decrease Value":
                  - img "down"
                - spinbutton "0.1": "0.40"
              - cell "117.5":
                - strong: "117.5"
              - cell "83.5":
                - strong: "83.5"
              - cell
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- text: 📋 公式组 5 1 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 数据5 数据6 数据7 结果1 结果2 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "数据5"
      - columnheader "数据6"
      - columnheader "数据7"
      - columnheader "结果1"
      - columnheader "结果2"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "客户E 用户E 0 0 0 0 0 0 0 0 0 股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "客户E":
        - textbox "输入客户名称": 客户E
      - cell "用户E":
        - textbox "输入用户名称": 用户E
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "支持3位小数": "0"
      - cell "0":
        - textbox "支持8位小数": "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - strong: "0"
      - cell "0":
        - strong: "0"
      - cell "股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - text: 👥 暂无股东信息 点击上方"添加股东"按钮开始添加
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- text: 📋 公式组 6 1 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 数据5 数据6 数据7 结果1 结果2 结果3 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "数据5"
      - columnheader "数据6"
      - columnheader "数据7"
      - columnheader "结果1"
      - columnheader "结果2"
      - columnheader "结果3"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "客户F 用户F 0 0 0 0 0 0 0 0 0 0 股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "客户F":
        - textbox "输入客户名称": 客户F
      - cell "用户F":
        - textbox "输入用户名称": 用户F
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "支持3位小数": "0"
      - cell "0":
        - textbox "支持8位小数": "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - strong: "0"
      - cell "0":
        - strong: "0"
      - cell "0":
        - strong: "0"
      - cell "股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - text: 👥 暂无股东信息 点击上方"添加股东"按钮开始添加
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- text: 📋 公式组 7 1 个客户记录
- button "plus 添加客户":
  - img "plus"
  - text: 添加客户
- table:
  - rowgroup:
    - row "客户 用户 数据1 数据2 数据3 数据4 数据5 数据6 数据7 结果1 结果2 结果3 股东信息 操作":
      - columnheader "客户"
      - columnheader "用户"
      - columnheader "数据1"
      - columnheader "数据2"
      - columnheader "数据3"
      - columnheader "数据4"
      - columnheader "数据5"
      - columnheader "数据6"
      - columnheader "数据7"
      - columnheader "结果1"
      - columnheader "结果2"
      - columnheader "结果3"
      - columnheader "股东信息"
      - columnheader "操作"
  - rowgroup:
    - row "-1000 -500 250 100 0 0 0 0 0 250 0 0 股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52 删 除":
      - cell "-1000":
        - textbox "输入客户名称": "-1000"
      - cell "-500":
        - textbox "输入用户名称": "-500"
      - cell "250":
        - textbox "0": "250"
      - cell "100":
        - textbox "0": "100"
      - cell "0":
        - textbox "支持3位小数": "0"
      - cell "0":
        - textbox "支持8位小数": "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "0":
        - textbox "0"
      - cell "250":
        - strong: "250"
      - cell "0":
        - strong: "0"
      - cell "0":
        - strong: "0"
      - cell "股东信息 plus 添加股东 👥 暂无股东信息 点击上方\"添加股东\"按钮开始添加 公式映射说明： 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52":
        - strong: 股东信息
        - button "plus 添加股东":
          - img "plus"
          - text: 添加股东
        - text: 👥 暂无股东信息 点击上方"添加股东"按钮开始添加
        - strong: 公式映射说明：
        - text: 第1个股东：公式1.31 & 公式1.32 第2个股东：公式1.41 & 公式1.42 第3个及以上股东：公式1.51 & 公式1.52
      - cell "删 除":
        - button "删 除"
- alert
```