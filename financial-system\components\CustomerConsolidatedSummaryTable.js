'use client';

// 客户归纳汇总表组件
// 将同名客户和股东的数据归纳到一起显示

import { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  InputNumber,
  Typography,
  Row,
  Col,
  Divider,
  message
} from 'antd';
import { 
  ExportOutlined, 
  ReloadOutlined, 
  EditOutlined,
  SaveOutlined,
  CalculatorOutlined
} from '@ant-design/icons';
import { CustomerConsolidationCalculations } from '../lib/customerConsolidationCalculations';

const { Title, Text } = Typography;

export default function CustomerConsolidatedSummaryTable({
  customers = [],
  shareholderData = [],
  agentId = null,
  loading = false,
  onRefresh,
  onExport
}) {
  // 状态管理
  const [consolidatedData, setConsolidatedData] = useState([]);
  const [manualInputs, setManualInputs] = useState({});
  const [isEditing, setIsEditing] = useState(false);
  const [rightSummary, setRightSummary] = useState({
    收入: 0,
    付出: 0,
    上周余额: 0,
    减免: 0,
    总计: 0
  });

  // 计算归纳数据
  const calculatedData = useMemo(() => {
    return CustomerConsolidationCalculations.consolidateCustomerData(
      customers, 
      shareholderData,
      manualInputs
    );
  }, [customers, shareholderData, manualInputs]);

  // 更新数据
  useEffect(() => {
    setConsolidatedData(calculatedData.consolidatedCustomers);
    setRightSummary(calculatedData.rightSummary);
    
    // 加载手动输入数据
    const savedInputs = CustomerConsolidationCalculations.getManualInputs();
    setManualInputs(savedInputs);
  }, [calculatedData]);

  // 保存手动输入数据
  const handleSaveManualInputs = async () => {
    try {
      await CustomerConsolidationCalculations.saveManualInputs(manualInputs, agentId);
      setIsEditing(false);
      message.success('手动输入数据保存成功！');

      // 重新计算
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      message.error('保存失败：' + error.message);
    }
  };

  // 更新手动输入值
  const updateManualInput = (key, value) => {
    setManualInputs(prev => ({
      ...prev,
      [key]: value || 0
    }));
  };

  // 表格列定义
  const getTableColumns = () => {
    return [
      {
        title: '客户名',
        dataIndex: 'customerName',
        key: 'customerName',
        width: 100,
        fixed: 'left',
        render: (text, record) => (
          <Text strong style={{ color: '#1890ff' }}>
            {text}
          </Text>
        )
      },
      {
        title: '用户名',
        dataIndex: 'userName',
        key: 'userName',
        width: 100,
        render: (text) => <Text>{text}</Text>
      },
      {
        title: '数据1',
        dataIndex: 'data1',
        key: 'data1',
        width: 100,
        render: (value) => (
          <Text>{CustomerConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '数据2', 
        dataIndex: 'data2',
        key: 'data2',
        width: 100,
        render: (value) => (
          <Text>{CustomerConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '数据6',
        dataIndex: 'data6',
        key: 'data6', 
        width: 100,
        render: (value) => (
          <Text>{CustomerConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '数据7',
        dataIndex: 'data7',
        key: 'data7',
        width: 100,
        render: (value) => (
          <Text>{CustomerConsolidationCalculations.formatNumber(value, 0)}</Text>
        )
      },
      {
        title: '结果1',
        dataIndex: 'result1',
        key: 'result1',
        width: 100,
        render: (value) => (
          <Text strong style={{ color: '#52c41a' }}>
            {CustomerConsolidationCalculations.formatNumber(value, 0)}
          </Text>
        )
      },
      {
        title: '结果2',
        dataIndex: 'result2', 
        key: 'result2',
        width: 100,
        render: (value) => (
          <Text strong style={{ color: '#52c41a' }}>
            {CustomerConsolidationCalculations.formatNumber(value, 0)}
          </Text>
        )
      },
      {
        title: '结果3',
        dataIndex: 'result3',
        key: 'result3',
        width: 100,
        render: (value) => (
          <Text strong style={{ color: '#52c41a' }}>
            {value ? CustomerConsolidationCalculations.formatNumber(value, 0) : '-'}
          </Text>
        )
      },
      {
        title: '总和',
        dataIndex: 'totalSum',
        key: 'totalSum',
        width: 120,
        render: (value) => (
          <Text strong style={{ 
            color: '#fa8c16',
            backgroundColor: '#fff7e6',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            {CustomerConsolidationCalculations.formatNumber(value, 0)}
          </Text>
        )
      },
      {
        title: '总和(四舍五入)',
        dataIndex: 'roundedSum',
        key: 'roundedSum', 
        width: 140,
        render: (value) => (
          <Text strong style={{ 
            color: '#722ed1',
            backgroundColor: '#f9f0ff',
            padding: '4px 8px',
            borderRadius: '4px'
          }}>
            {CustomerConsolidationCalculations.formatNumber(value, 0)}
          </Text>
        )
      }
    ];
  };

  // 渲染右侧汇总区域
  const renderRightSummary = () => {
    return (
      <Card 
        title="汇总统计"
        size="small"
        style={{ 
          backgroundColor: '#fafafa',
          border: '2px solid #1890ff'
        }}
      >
        {/* 收入、付出、上周余额、减免 */}
        <div style={{ marginBottom: '16px' }}>
          {Object.entries(rightSummary).map(([key, value]) => (
            <div key={key} style={{ 
              marginBottom: '12px',
              padding: '8px 12px',
              backgroundColor: key === '总计' ? '#fff7e6' : '#ffffff',
              borderRadius: '6px',
              border: key === '总计' ? '2px solid #faad14' : '1px solid #d9d9d9'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong style={{ 
                  color: key === '总计' ? '#fa8c16' : '#333',
                  fontSize: key === '总计' ? '16px' : '14px'
                }}>
                  {key}:
                </Text>
                {(key === '上周余额' || key === '减免') ? (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {isEditing ? (
                      <InputNumber
                        value={manualInputs[key] || 0}
                        onChange={(val) => updateManualInput(key, val)}
                        size="small"
                        style={{ width: '120px' }}
                        formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value.replace(/\$\s?|(,*)/g, '')}
                      />
                    ) : (
                      <Text strong style={{ 
                        color: key === '总计' ? '#fa8c16' : '#1890ff',
                        fontSize: key === '总计' ? '16px' : '14px'
                      }}>
                        {CustomerConsolidationCalculations.formatNumber(value, 0)}
                      </Text>
                    )}
                  </div>
                ) : (
                  <Text strong style={{ 
                    color: key === '总计' ? '#fa8c16' : '#1890ff',
                    fontSize: key === '总计' ? '16px' : '14px'
                  }}>
                    {CustomerConsolidationCalculations.formatNumber(value, 0)}
                  </Text>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 编辑按钮 */}
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          {isEditing ? (
            <Space>
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={handleSaveManualInputs}
                size="small"
              >
                保存
              </Button>
              <Button 
                onClick={() => setIsEditing(false)}
                size="small"
              >
                取消
              </Button>
            </Space>
          ) : (
            <Button 
              type="dashed" 
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
              size="small"
            >
              编辑手动输入
            </Button>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 标题和操作按钮 */}
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              📊 客户归纳汇总表
            </Title>
            <Space>
              <Button 
                type="primary" 
                icon={<ExportOutlined />} 
                onClick={onExport}
                size="small"
              >
                导出Excel
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={onRefresh}
                loading={loading}
                size="small"
              >
                刷新数据
              </Button>
            </Space>
          </div>
        }
        style={{ marginBottom: '24px' }}
      >
        <Text type="secondary">
          将同名客户和股东的数据归纳汇总，按照特定计算逻辑显示收入、付出、余额等信息
        </Text>
      </Card>

      {/* 主要内容区域 */}
      <Row gutter={24}>
        {/* 左侧：客户归纳表格 */}
        <Col span={16}>
          <Card 
            title="客户归纳数据表"
            style={{ backgroundColor: '#fafafa' }}
          >
            <Table
              columns={getTableColumns()}
              dataSource={consolidatedData}
              loading={loading}
              rowKey={(record) => `${record.customerName}_${record.userName}`}
              scroll={{ x: 'max-content', y: 600 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                pageSize: 20,
                pageSizeOptions: ['10', '20', '50', '100']
              }}
              size="small"
              bordered
            />
          </Card>
        </Col>

        {/* 右侧：汇总统计区域 */}
        <Col span={8}>
          {renderRightSummary()}
        </Col>
      </Row>
    </div>
  );
}
