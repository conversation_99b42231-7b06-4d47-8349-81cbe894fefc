'use client';

// 汇总表组件
// 跨模块数据汇总和对比分析

import { useState, useEffect, useMemo } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Select, 
  DatePicker, 
  Tooltip,
  Tag,
  Statistic,
  Row,
  Col
} from 'antd';
import { 
  ExportOutlined, 
  ReloadOutlined, 
  SettingOutlined,
  PrinterOutlined 
} from '@ant-design/icons';
import { MODULE_CONFIGS, getAllModules } from '../config/moduleConfigs';
import { FormulaEngine } from '../lib/calculations';

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function SummaryTable({ 
  customers = [], 
  loading = false,
  onRefresh,
  onExport 
}) {
  // 状态管理
  const [selectedModules, setSelectedModules] = useState(['all']); // 选中的模块
  const [dateRange, setDateRange] = useState(null); // 时间范围
  const [columnConfig, setColumnConfig] = useState({}); // 列配置
  const [summaryData, setSummaryData] = useState([]); // 汇总数据
  const [totalStats, setTotalStats] = useState({}); // 汇总统计

  // 获取所有模块
  const allModules = getAllModules();

  // 计算汇总数据
  const calculateSummaryData = useMemo(() => {
    if (!customers || customers.length === 0) return [];

    // 按客户名分组数据
    const customerGroups = {};
    
    customers.forEach(customer => {
      const key = `${customer.name}_${customer.userName}`;
      if (!customerGroups[key]) {
        customerGroups[key] = {
          customerName: customer.name,
          userName: customer.userName,
          moduleData: {},
          calculatedResults: {},
          totals: {},
          metadata: {
            lastUpdated: new Date(),
            customerCount: 0
          }
        };
      }
      
      // 添加模块数据
      const moduleId = customer.moduleId || 'module1_1';
      customerGroups[key].moduleData[moduleId] = {
        data1: customer.data1 || 0,
        data2: customer.data2 || 0,
        data3: customer.data3 || 0,
        data4: customer.data4 || 0,
        data5: customer.data5 || 0,
        data6: customer.data6 || 0,
        data7: customer.data7 || 0,
        result1: customer.result1 || 0,
        result2: customer.result2 || 0,
        result3: customer.result3 || 0,
        result4: customer.result4 || 0,
        result5: customer.result5 || 0
      };
      
      customerGroups[key].metadata.customerCount++;
    });

    // 计算跨模块汇总
    Object.values(customerGroups).forEach(group => {
      // 计算各模块总计
      let totalRevenue = 0;
      let totalExpense = 0;
      let totalProfit = 0;
      
      Object.entries(group.moduleData).forEach(([moduleId, data]) => {
        totalRevenue += parseFloat(data.data1 || 0);
        totalExpense += parseFloat(data.data2 || 0);
        totalProfit += parseFloat(data.result1 || 0);
      });
      
      group.totals = {
        totalRevenue,
        totalExpense,
        totalProfit,
        profitRate: totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0
      };
      
      // 计算综合指标
      group.calculatedResults = {
        formula1: totalRevenue * 0.1, // 示例公式
        formula2: totalExpense * 0.05, // 示例公式
        综合评分: Math.round((totalProfit / Math.max(totalRevenue, 1)) * 100)
      };
    });

    return Object.values(customerGroups);
  }, [customers, selectedModules, dateRange]);

  // 更新汇总数据
  useEffect(() => {
    setSummaryData(calculateSummaryData);
    
    // 计算总体统计
    const stats = calculateSummaryData.reduce((acc, item) => {
      acc.totalCustomers = (acc.totalCustomers || 0) + 1;
      acc.totalRevenue = (acc.totalRevenue || 0) + item.totals.totalRevenue;
      acc.totalExpense = (acc.totalExpense || 0) + item.totals.totalExpense;
      acc.totalProfit = (acc.totalProfit || 0) + item.totals.totalProfit;
      return acc;
    }, {});
    
    stats.avgProfitRate = stats.totalRevenue > 0 ? 
      (stats.totalProfit / stats.totalRevenue * 100) : 0;
    
    setTotalStats(stats);
  }, [calculateSummaryData]);

  // 生成动态列配置
  const generateColumns = () => {
    const columns = [
      {
        title: '客户名',
        dataIndex: 'customerName',
        key: 'customerName',
        fixed: 'left',
        width: 120,
        render: (text) => <strong>{text}</strong>
      },
      {
        title: '用户名',
        dataIndex: 'userName',
        key: 'userName',
        fixed: 'left',
        width: 120
      }
    ];

    // 添加模块数据列
    allModules.forEach(module => {
      // 模块主要数据列
      columns.push({
        title: (
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              backgroundColor: '#1890ff', 
              color: 'white', 
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              {module.displayName}
            </div>
            <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
              ({module.inputFields}+{module.resultFields})
            </div>
          </div>
        ),
        key: `${module.id}_data`,
        width: 150,
        render: (_, record) => {
          const moduleData = record.moduleData[module.id];
          if (!moduleData) {
            return <span style={{ color: '#ccc' }}>-</span>;
          }
          
          return (
            <div style={{ 
              backgroundColor: '#e6f7ff', 
              padding: '8px',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              <div>收入: {FormulaEngine.formatData.formatByType(moduleData.data1, 'integer')}</div>
              <div>支出: {FormulaEngine.formatData.formatByType(moduleData.data2, 'integer')}</div>
              <div>结果: {FormulaEngine.formatData.formatByType(moduleData.result1, 'result')}</div>
            </div>
          );
        }
      });
    });

    // 添加计算结果列
    columns.push({
      title: (
        <div style={{ 
          backgroundColor: '#52c41a', 
          color: 'white', 
          padding: '4px 8px',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          公式1
        </div>
      ),
      key: 'formula1',
      width: 120,
      render: (_, record) => (
        <div style={{ 
          backgroundColor: '#f6ffed', 
          padding: '8px',
          borderRadius: '4px',
          textAlign: 'right',
          fontWeight: 'bold'
        }}>
          {FormulaEngine.formatData.formatByType(record.calculatedResults.formula1, 'result')}
        </div>
      )
    });

    columns.push({
      title: (
        <div style={{ 
          backgroundColor: '#52c41a', 
          color: 'white', 
          padding: '4px 8px',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          公式2
        </div>
      ),
      key: 'formula2',
      width: 120,
      render: (_, record) => (
        <div style={{ 
          backgroundColor: '#f6ffed', 
          padding: '8px',
          borderRadius: '4px',
          textAlign: 'right',
          fontWeight: 'bold'
        }}>
          {FormulaEngine.formatData.formatByType(record.calculatedResults.formula2, 'result')}
        </div>
      )
    });

    // 添加汇总列
    columns.push({
      title: (
        <div style={{ 
          backgroundColor: '#faad14', 
          color: 'white', 
          padding: '4px 8px',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          总计
        </div>
      ),
      key: 'totals',
      width: 180,
      render: (_, record) => (
        <div style={{ 
          backgroundColor: '#fffbe6', 
          padding: '8px',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          <div><strong>总收入:</strong> {FormulaEngine.formatData.formatByType(record.totals.totalRevenue, 'result')}</div>
          <div><strong>总支出:</strong> {FormulaEngine.formatData.formatByType(record.totals.totalExpense, 'result')}</div>
          <div><strong>净利润:</strong> 
            <span style={{ 
              color: record.totals.totalProfit >= 0 ? '#52c41a' : '#ff4d4f',
              fontWeight: 'bold'
            }}>
              {FormulaEngine.formatData.formatByType(record.totals.totalProfit, 'result')}
            </span>
          </div>
          <div><strong>利润率:</strong> {record.totals.profitRate.toFixed(2)}%</div>
        </div>
      )
    });

    // 添加备注列
    columns.push({
      title: '备注',
      key: 'notes',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '12px', color: '#666' }}>
          <div>客户数: {record.metadata.customerCount}</div>
          <div>评分: {record.calculatedResults.综合评分}</div>
        </div>
      )
    });

    // 添加四舍五入列
    columns.push({
      title: '四舍五入',
      key: 'rounded',
      width: 120,
      render: (_, record) => (
        <div style={{ 
          backgroundColor: '#f0f0f0', 
          padding: '8px',
          borderRadius: '4px',
          textAlign: 'right',
          fontSize: '12px'
        }}>
          {Math.round(record.totals.totalProfit).toLocaleString()}
        </div>
      )
    });

    return columns;
  };

  // 处理导出
  const handleExport = () => {
    if (onExport) {
      onExport(summaryData);
    }
  };

  // 处理刷新
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>📊 跨模块数据汇总表</span>
          <Space>
            <Button 
              type="primary" 
              icon={<ExportOutlined />} 
              onClick={handleExport}
              size="small"
            >
              导出Excel
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh}
              loading={loading}
              size="small"
            >
              刷新数据
            </Button>
            <Button 
              icon={<SettingOutlined />} 
              size="small"
            >
              配置列
            </Button>
            <Button 
              icon={<PrinterOutlined />} 
              size="small"
            >
              打印
            </Button>
          </Space>
        </div>
      }
      style={{ marginBottom: '24px' }}
    >
      {/* 筛选区域 */}
      <div style={{ 
        marginBottom: '16px', 
        padding: '16px',
        backgroundColor: '#fafafa',
        borderRadius: '8px'
      }}>
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>模块选择:</div>
            <Select
              mode="multiple"
              placeholder="选择要显示的模块"
              value={selectedModules}
              onChange={setSelectedModules}
              style={{ width: '100%' }}
            >
              <Option value="all">全部模块</Option>
              {allModules.map(module => (
                <Option key={module.id} value={module.id}>
                  {module.displayName}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>时间范围:</div>
            <RangePicker 
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>汇总统计:</div>
            <div style={{ fontSize: '12px' }}>
              <Tag color="blue">客户: {totalStats.totalCustomers || 0}</Tag>
              <Tag color="green">利润: {FormulaEngine.formatData.formatByType(totalStats.totalProfit || 0, 'result')}</Tag>
              <Tag color="orange">利润率: {(totalStats.avgProfitRate || 0).toFixed(2)}%</Tag>
            </div>
          </Col>
        </Row>
      </div>

      {/* 汇总表格 */}
      <Table
        columns={generateColumns()}
        dataSource={summaryData}
        loading={loading}
        rowKey={(record) => `${record.customerName}_${record.userName}`}
        scroll={{ x: 'max-content', y: 600 }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          pageSize: 20,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        size="small"
        bordered
        summary={() => (
          <Table.Summary fixed>
            <Table.Summary.Row style={{ backgroundColor: '#fafafa', fontWeight: 'bold' }}>
              <Table.Summary.Cell index={0}>合计</Table.Summary.Cell>
              <Table.Summary.Cell index={1}>{totalStats.totalCustomers || 0}个客户</Table.Summary.Cell>
              {allModules.map((_, index) => (
                <Table.Summary.Cell key={`module_${index}`} index={index + 2}>
                  <div style={{ textAlign: 'center', fontSize: '12px' }}>
                    汇总数据
                  </div>
                </Table.Summary.Cell>
              ))}
              <Table.Summary.Cell index={allModules.length + 2}>
                {FormulaEngine.formatData.formatByType(totalStats.totalRevenue * 0.1 || 0, 'result')}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={allModules.length + 3}>
                {FormulaEngine.formatData.formatByType(totalStats.totalExpense * 0.05 || 0, 'result')}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={allModules.length + 4}>
                <div style={{ 
                  color: (totalStats.totalProfit || 0) >= 0 ? '#52c41a' : '#ff4d4f',
                  fontWeight: 'bold'
                }}>
                  {FormulaEngine.formatData.formatByType(totalStats.totalProfit || 0, 'result')}
                </div>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={allModules.length + 5}>总体备注</Table.Summary.Cell>
              <Table.Summary.Cell index={allModules.length + 6}>
                {Math.round(totalStats.totalProfit || 0).toLocaleString()}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          </Table.Summary>
        )}
        locale={{
          emptyText: (
            <div style={{ 
              padding: '40px', 
              textAlign: 'center',
              color: '#999'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无汇总数据</div>
              <div style={{ fontSize: '14px' }}>
                请先在各模块中添加客户数据
              </div>
            </div>
          )
        }}
      />
    </Card>
  );
}
