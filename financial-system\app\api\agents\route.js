// 代理商API路由 - JavaScript版本
// 避免TypeScript版本冲突

import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';

// 获取代理商列表
export async function GET(request) {
  try {
    const agents = await prisma.agent.findMany({
      include: {
        customers: true,
        periods: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(agents);
  } catch (error) {
    console.error('获取代理商列表失败:', error);
    return NextResponse.json(
      { error: '获取代理商列表失败' }, 
      { status: 500 }
    );
  }
}

// 创建新代理商
export async function POST(request) {
  try {
    const data = await request.json();
    
    // 数据验证
    if (!data.name || data.name.trim() === '') {
      return NextResponse.json(
        { error: '代理商名称不能为空' }, 
        { status: 400 }
      );
    }
    
    const agent = await prisma.agent.create({
      data: {
        name: data.name.trim(),
        clientName: data.clientName?.trim() || null,
        userName: data.userName?.trim() || null,
      },
      include: {
        customers: true,
        periods: true,
      }
    });
    
    return NextResponse.json(agent, { status: 201 });
  } catch (error) {
    console.error('创建代理商失败:', error);
    
    // 处理唯一约束错误
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: '代理商名称已存在' }, 
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: '创建代理商失败' }, 
      { status: 500 }
    );
  }
}

// 批量更新代理商
export async function PATCH(request) {
  try {
    const { updates } = await request.json();
    
    if (!Array.isArray(updates)) {
      return NextResponse.json(
        { error: '更新数据格式错误' }, 
        { status: 400 }
      );
    }
    
    const updatedAgents = [];
    
    // 使用事务批量更新
    await prisma.$transaction(async (tx) => {
      for (const update of updates) {
        if (!update.id) continue;
        
        const updatedAgent = await tx.agent.update({
          where: { id: update.id },
          data: {
            name: update.name?.trim(),
            clientName: update.clientName?.trim() || null,
            userName: update.userName?.trim() || null,
          },
          include: {
            customers: true,
            periods: true,
          }
        });
        
        updatedAgents.push(updatedAgent);
      }
    });
    
    return NextResponse.json(updatedAgents);
  } catch (error) {
    console.error('批量更新代理商失败:', error);
    return NextResponse.json(
      { error: '批量更新代理商失败' }, 
      { status: 500 }
    );
  }
}
