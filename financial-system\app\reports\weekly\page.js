'use client';

// 公司周利润表页面 - JavaScript版本
// 避免TypeScript版本冲突

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Layout,
  Table,
  Button,
  Card,
  DatePicker,
  Space,
  Statistic,
  Row,
  Col,
  message,
  Modal,
  Divider
} from 'antd';
import { 
  ArrowLeftOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAgentStore } from '../../../store/agentStore';
import { useCustomerStore } from '../../../store/customerStore';
import { FormulaEngine } from '../../../lib/calculations';
import { FormulaPageConsolidationCalculations } from '../../../lib/formulaPageConsolidationCalculations';
import dayjs from 'dayjs';

const { Header, Content } = Layout;
const { RangePicker } = DatePicker;

export default function WeeklyProfitPage() {
  const router = useRouter();
  const { agents, fetchAgents } = useAgentStore();
  const { customers, fetchCustomers } = useCustomerStore();
  
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([
    dayjs().startOf('week'),
    dayjs().endOf('week')
  ]);
  const [reportData, setReportData] = useState([]);
  const [summary, setSummary] = useState({});
  const [summaryTableData, setSummaryTableData] = useState(null);

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    
    // 加载基础数据
    loadData();
  }, [router]);

  // 加载汇总表数据
  const loadSummaryTableData = async () => {
    console.log('📊 开始加载汇总表数据...');
    try {
      // 1. 尝试从API加载数据
      const response = await fetch('/api/summary-layout');
      if (response.ok) {
        const apiData = await response.json();
        if (apiData.success && apiData.customerData) {
          console.log('✅ 从API加载汇总表数据成功');
          return apiData.customerData;
        }
      }

      // 2. 尝试从本地存储加载数据
      const localData = localStorage.getItem('summaryTableData');
      if (localData) {
        const parsedData = JSON.parse(localData);
        if (parsedData.customerData) {
          console.log('✅ 从本地存储加载汇总表数据成功');
          return parsedData.customerData;
        }
      }

      console.log('⚠️ 没有找到汇总表数据');
      return null;
    } catch (error) {
      console.error('❌ 加载汇总表数据失败:', error);
      return null;
    }
  };

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 加载汇总表数据
      const summaryData = await loadSummaryTableData();
      setSummaryTableData(summaryData);

      if (summaryData) {
        generateReportFromSummaryData(summaryData);
      } else {
        message.warning('没有找到汇总表数据，请先在公式配置页面保存数据');
        setReportData([]);
        setSummary({});
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 基于汇总表数据生成周报表
  const generateReportFromSummaryData = (customerData) => {
    console.log('📊 开始生成周报表数据...');

    try {
      // 使用FormulaPageConsolidationCalculations计算归纳数据
      const consolidatedResult = FormulaPageConsolidationCalculations.consolidateFormulaPageData(
        customerData,
        {}
      );

      console.log('✅ 归纳计算完成:', consolidatedResult);

      // 按客户分组生成报表数据
      const reportData = [];
      Object.entries(consolidatedResult.consolidatedByUser || {}).forEach(([customerName, records]) => {
        // 计算该客户的周数据
        const weeklyData = {
          totalAmount: 0,
          totalCost: 0,
          totalProfit: 0,
          recordCount: records.length
        };

        records.forEach(record => {
          weeklyData.totalAmount += record.roundedSum || 0;
          // 假设成本是总金额的70%（可以根据实际业务调整）
          weeklyData.totalCost += (record.roundedSum || 0) * 0.7;
        });

        weeklyData.totalProfit = weeklyData.totalAmount - weeklyData.totalCost;

        reportData.push({
          key: customerName,
          customerName: customerName,
          recordCount: weeklyData.recordCount,
          totalAmount: weeklyData.totalAmount,
          totalCost: weeklyData.totalCost,
          profit: weeklyData.totalProfit,
          profitMargin: weeklyData.totalAmount ? (weeklyData.totalProfit / weeklyData.totalAmount * 100) : 0,
          records: records // 保存原始记录用于详细查看
        });
      });

      setReportData(reportData);

      // 计算汇总统计
      const totalSummary = {
        totalCustomers: reportData.length,
        totalRecords: reportData.reduce((sum, item) => sum + item.recordCount, 0),
        totalAmount: reportData.reduce((sum, item) => sum + item.totalAmount, 0),
        totalCost: reportData.reduce((sum, item) => sum + item.totalCost, 0),
        totalProfit: reportData.reduce((sum, item) => sum + item.profit, 0),
      };

      totalSummary.profitMargin = totalSummary.totalAmount ?
        (totalSummary.totalProfit / totalSummary.totalAmount * 100) : 0;

      setSummary(totalSummary);
      console.log('✅ 周报表数据生成完成');

    } catch (error) {
      console.error('❌ 生成周报表数据失败:', error);
      message.error('生成周报表数据失败');
      setReportData([]);
      setSummary({});
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      width: 150,
      fixed: 'left',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            记录数: {record.recordCount}
          </div>
        </div>
      ),
    },
    {
      title: '周总额',
      dataIndex: 'totalAmount',
      width: 120,
      render: (value) => (
        <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '周成本',
      dataIndex: 'totalCost',
      width: 120,
      render: (value) => (
        <span style={{ color: '#fa8c16' }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '周利润',
      dataIndex: 'profit',
      width: 120,
      render: (value) => (
        <span style={{
          color: value >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '利润率',
      dataIndex: 'profitMargin',
      width: 100,
      render: (value) => (
        <span style={{
          color: (value || 0) >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {(value || 0).toFixed(2)}%
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => showCustomerDetail(record)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  // 查看客户详情
  const showCustomerDetail = (record) => {
    Modal.info({
      title: `${record.customerName} - 详细信息`,
      width: 800,
      content: (
        <div>
          <p><strong>记录数量:</strong> {record.recordCount}</p>
          <p><strong>周总额:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.totalAmount, 0, true)}</p>
          <p><strong>周成本:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.totalCost, 0, true)}</p>
          <p><strong>周利润:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.profit, 0, true)}</p>
          <p><strong>利润率:</strong> {record.profitMargin.toFixed(2)}%</p>
          <Divider />
          <h4>详细记录:</h4>
          <Table
            size="small"
            dataSource={record.records || []}
            columns={[
              { title: '用户', dataIndex: 'user', width: 100 },
              { title: '数据1', dataIndex: 'data1', width: 80, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0) },
              { title: '数据2', dataIndex: 'data2', width: 80, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0) },
              { title: '计算结果', dataIndex: 'roundedSum', width: 100, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0, true) },
            ]}
            pagination={false}
            scroll={{ y: 300 }}
          />
        </div>
      ),
    });
  };

  // 添加总计行
  const dataWithTotal = [
    ...reportData,
    {
      key: 'total',
      customerName: '总计',
      recordCount: summary.totalRecords,
      totalAmount: summary.totalAmount,
      totalCost: summary.totalCost,
      profit: summary.totalProfit,
      profitMargin: summary.profitMargin,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => router.push('/dashboard')}
            style={{ marginRight: '16px' }}
          >
            返回
          </Button>
          <h1 style={{ margin: 0, fontSize: '20px' }}>
            公司周利润表
          </h1>
        </div>
        <Space>
          <RangePicker 
            value={dateRange}
            onChange={setDateRange}
            format="YYYY-MM-DD"
          />
          <Button 
            icon={<ReloadOutlined />}
            onClick={loadData}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => message.info('导出功能开发中...')}
          >
            导出
          </Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        {/* 汇总统计 */}
        <Card title="汇总统计" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={4}>
              <Statistic title="代理商总数" value={summary.totalAgents || 0} />
            </Col>
            <Col span={4}>
              <Statistic title="客户总数" value={summary.totalCustomers || 0} />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总收入" 
                value={summary.totalAmount || 0}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总成本" 
                value={summary.totalCost || 0}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总利润" 
                value={summary.totalProfit || 0}
                valueStyle={{ color: (summary.totalProfit || 0) >= 0 ? '#3f8600' : '#cf1322' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="利润率" 
                value={summary.totalAmount ? (summary.totalProfit / summary.totalAmount * 100) : 0}
                precision={2}
                suffix="%"
                valueStyle={{ color: (summary.totalProfit || 0) >= 0 ? '#3f8600' : '#cf1322' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 周利润表 */}
        <Card title={`周利润表 (${dateRange[0]?.format('MM.DD')}-${dateRange[1]?.format('MM.DD')})`}>
          <Table 
            columns={columns} 
            dataSource={dataWithTotal}
            pagination={false}
            scroll={{ x: 2000, y: 600 }}
            size="small"
            bordered
            rowClassName={(record) => record.key === 'total' ? 'total-row' : ''}
            loading={loading}
          />
        </Card>
      </Content>
    </Layout>
  );
}
