'use client';

// EditableLabel 组件 - 支持双击编辑的标签组件
// 用于公式名称和数据字段名称的在线编辑

import React, { useState, useRef, useEffect } from 'react';
import { Input, message, Tooltip } from 'antd';
import { EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';

const EditableLabel = ({
  value,                    // 当前显示值
  defaultValue,            // 默认值
  onSave,                  // 保存回调 (newValue) => Promise<boolean>
  onCancel,                // 取消回调
  maxLength = 20,          // 最大长度
  validator,               // 验证函数 (value) => { isValid: boolean, error: string }
  placeholder = '点击编辑', // 占位符
  editable = true,         // 是否可编辑
  style = {},              // 自定义样式
  className = '',          // 自定义类名
  showEditIcon = true,     // 是否显示编辑图标
  autoFocus = true,        // 编辑时是否自动聚焦
  triggerType = 'doubleClick' // 触发方式：'doubleClick' | 'click'
}) => {
  // 状态管理
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value || '');
  const [isValid, setIsValid] = useState(true);
  const [validationError, setValidationError] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  
  // 引用
  const inputRef = useRef(null);
  const labelRef = useRef(null);
  
  // 同步外部value变化
  useEffect(() => {
    if (!isEditing) {
      setTempValue(value || '');
    }
  }, [value, isEditing]);
  
  // 编辑模式下自动聚焦
  useEffect(() => {
    if (isEditing && autoFocus && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing, autoFocus]);
  
  // 验证输入值
  const validateInput = (inputValue) => {
    // 长度验证
    if (inputValue.length > maxLength) {
      setIsValid(false);
      setValidationError(`最大长度为${maxLength}个字符`);
      return false;
    }
    
    // 空值验证
    if (!inputValue.trim()) {
      setIsValid(false);
      setValidationError('不能为空');
      return false;
    }
    
    // 自定义验证
    if (validator) {
      const result = validator(inputValue);
      if (!result.isValid) {
        setIsValid(false);
        setValidationError(result.error);
        return false;
      }
    }
    
    setIsValid(true);
    setValidationError('');
    return true;
  };
  
  // 开始编辑
  const startEdit = () => {
    if (!editable) return;
    
    setIsEditing(true);
    setTempValue(value || '');
    setIsValid(true);
    setValidationError('');
  };
  
  // 取消编辑
  const cancelEdit = () => {
    setIsEditing(false);
    setTempValue(value || '');
    setIsValid(true);
    setValidationError('');
    
    if (onCancel) {
      onCancel();
    }
  };
  
  // 保存编辑
  const saveEdit = async () => {
    const trimmedValue = tempValue.trim();
    
    // 验证输入
    if (!validateInput(trimmedValue)) {
      return;
    }
    
    // 如果值没有变化，直接取消编辑
    if (trimmedValue === value) {
      cancelEdit();
      return;
    }
    
    setIsSaving(true);
    
    try {
      // 调用保存回调
      const success = await onSave(trimmedValue);
      
      if (success) {
        setIsEditing(false);
        message.success('保存成功');
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };
  
  // 输入值变化处理
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setTempValue(newValue);
    validateInput(newValue);
  };
  
  // 键盘事件处理
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  };
  
  // 点击事件处理
  const handleClick = () => {
    if (triggerType === 'click') {
      startEdit();
    }
  };
  
  // 双击事件处理
  const handleDoubleClick = () => {
    if (triggerType === 'doubleClick') {
      startEdit();
    }
  };
  
  // 失焦处理
  const handleBlur = () => {
    // 延迟处理，避免与按钮点击冲突
    setTimeout(() => {
      if (isEditing && !isSaving) {
        saveEdit();
      }
    }, 100);
  };
  
  // 恢复默认值
  const restoreDefault = () => {
    if (defaultValue && onSave) {
      onSave(defaultValue);
    }
  };
  
  // 渲染编辑模式
  if (isEditing) {
    return (
      <div className={`editable-label-editing ${className}`} style={style}>
        <Input
          ref={inputRef}
          value={tempValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyPress}
          onBlur={handleBlur}
          placeholder={placeholder}
          maxLength={maxLength}
          status={!isValid ? 'error' : ''}
          style={{
            minWidth: '120px',
            border: isValid ? '2px solid #1890ff' : '2px solid #ff4d4f'
          }}
          suffix={
            <div style={{ display: 'flex', gap: '4px' }}>
              <CheckOutlined
                style={{ 
                  color: isValid ? '#52c41a' : '#d9d9d9',
                  cursor: isValid ? 'pointer' : 'not-allowed'
                }}
                onClick={isValid ? saveEdit : undefined}
              />
              <CloseOutlined
                style={{ color: '#ff4d4f', cursor: 'pointer' }}
                onClick={cancelEdit}
              />
            </div>
          }
          loading={isSaving}
        />
        {!isValid && validationError && (
          <div style={{ 
            color: '#ff4d4f', 
            fontSize: '12px', 
            marginTop: '2px',
            position: 'absolute',
            zIndex: 1000,
            background: '#fff',
            padding: '2px 4px',
            border: '1px solid #ff4d4f',
            borderRadius: '4px'
          }}>
            {validationError}
          </div>
        )}
      </div>
    );
  }
  
  // 渲染显示模式
  return (
    <div className={`editable-label-display ${className}`} style={style}>
      <Tooltip 
        title={editable ? `双击编辑${defaultValue ? ' | 右键恢复默认' : ''}` : '不可编辑'}
        placement="top"
      >
        <span
          ref={labelRef}
          onClick={handleClick}
          onDoubleClick={handleDoubleClick}
          onContextMenu={defaultValue ? (e) => {
            e.preventDefault();
            restoreDefault();
          } : undefined}
          style={{
            cursor: editable ? 'pointer' : 'default',
            padding: '4px 8px',
            borderRadius: '4px',
            border: '1px solid transparent',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '4px',
            minHeight: '32px',
            backgroundColor: editable ? '#fafafa' : 'transparent',
            transition: 'all 0.2s',
            ':hover': editable ? {
              backgroundColor: '#f0f0f0',
              border: '1px solid #d9d9d9'
            } : {}
          }}
          className={editable ? 'editable-label-hover' : ''}
        >
          {value || placeholder}
          {editable && showEditIcon && (
            <EditOutlined 
              style={{ 
                fontSize: '12px', 
                color: '#8c8c8c',
                opacity: 0.6
              }} 
            />
          )}
        </span>
      </Tooltip>
      
      <style jsx>{`
        .editable-label-hover:hover {
          background-color: #f0f0f0 !important;
          border: 1px solid #d9d9d9 !important;
        }
        
        .editable-label-editing {
          position: relative;
          display: inline-block;
        }
        
        .editable-label-display {
          display: inline-block;
        }
      `}</style>
    </div>
  );
};

export default EditableLabel;
