// 自定义Hook - 使用内存存储解决React状态管理问题
// 提供实时状态同步和持久化（模仿Redis功能）

import { useState, useEffect, useCallback, useRef } from 'react';

// 全局内存存储 - 模仿Redis功能，使用localStorage持久化
const memoryStore = new Map();

// 初始化时从localStorage恢复数据
const initializeMemoryStore = () => {
  if (typeof window !== 'undefined') {
    try {
      const stored = localStorage.getItem('financial_system_memory_store');
      if (stored) {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([key, value]) => {
          memoryStore.set(key, value);
        });
        console.log('🔄 从localStorage恢复内存存储数据');
      }
    } catch (err) {
      console.warn('⚠️ localStorage恢复失败:', err);
    }
  }
};

// 保存到localStorage
const persistToLocalStorage = () => {
  if (typeof window !== 'undefined') {
    try {
      const data = {};
      for (const [key, value] of memoryStore.entries()) {
        data[key] = value;
      }
      localStorage.setItem('financial_system_memory_store', JSON.stringify(data));
    } catch (err) {
      console.warn('⚠️ localStorage保存失败:', err);
    }
  }
};

// 初始化内存存储
initializeMemoryStore();

// 内存存储工具函数
const memoryStorage = {
  // 获取状态
  get: (key) => {
    const data = memoryStore.get(key);
    if (data) {
      console.log(`✅ 从内存加载状态成功: ${key}`, data.state);
      return {
        success: true,
        state: data.state,
        timestamp: data.timestamp
      };
    }
    return { success: false, state: null };
  },

  // 保存状态
  set: (key, state) => {
    const timestamp = new Date().toISOString();
    memoryStore.set(key, { state, timestamp });
    persistToLocalStorage(); // 立即持久化到localStorage
    console.log(`✅ 状态保存到内存成功: ${key}`, state);
    return { success: true, timestamp };
  },

  // 删除状态
  delete: (key) => {
    const deleted = memoryStore.delete(key);
    if (deleted) {
      persistToLocalStorage(); // 立即持久化到localStorage
      console.log(`✅ 状态清除成功: ${key}`);
    }
    return { success: deleted };
  },

  // 检查是否存在
  has: (key) => {
    return memoryStore.has(key);
  }
};

// 内存状态管理Hook（替代Redis）
export const useRedisState = (agentId, type, initialState = null) => {
  const [state, setState] = useState(initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastSync, setLastSync] = useState(null);

  // 使用ref来避免闭包问题
  const stateRef = useRef(state);
  const syncTimeoutRef = useRef(null);

  // 生成存储键
  const storageKey = `${agentId}:${type}`;

  // 更新ref
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  // 从内存获取状态
  const loadState = useCallback(async () => {
    if (!agentId || !type) return;

    try {
      setLoading(true);
      setError(null);

      // 模拟异步操作（保持接口一致性）
      await new Promise(resolve => setTimeout(resolve, 10));

      const data = memoryStorage.get(storageKey);

      if (data.success && data.state !== null) {
        setState(data.state);
        setLastSync(data.timestamp);
      } else if (initialState !== null) {
        setState(initialState);
        console.log(`⚠️ 内存中无状态，使用初始状态: ${type}`, initialState);
      }

    } catch (err) {
      console.error(`❌ 加载状态失败: ${type}`, err);
      setError(err.message);
      if (initialState !== null) {
        setState(initialState);
      }
    } finally {
      setLoading(false);
    }
  }, [agentId, type, storageKey]); // 移除initialState依赖，避免无限循环

  // 保存状态到内存
  const saveState = useCallback(async (newState) => {
    if (!agentId || !type) return false;

    try {
      setError(null);

      // 模拟异步操作（保持接口一致性）
      await new Promise(resolve => setTimeout(resolve, 5));

      const result = memoryStorage.set(storageKey, newState);

      if (result.success) {
        setLastSync(result.timestamp);
        return true;
      } else {
        throw new Error('保存失败');
      }

    } catch (err) {
      console.error(`❌ 保存状态失败: ${type}`, err);
      setError(err.message);
      return false;
    }
  }, [agentId, type, storageKey]);
  
  // 更新状态（本地+内存）
  const updateState = useCallback(async (newState) => {
    // 立即更新本地状态
    setState(newState);

    // 立即保存到内存（无需防抖，内存操作很快）
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }

    // 使用很短的延迟确保状态更新的顺序
    syncTimeoutRef.current = setTimeout(async () => {
      await saveState(newState);
    }, 10); // 10ms延迟，几乎是立即的

  }, [saveState]);

  // 强制同步状态
  const syncState = useCallback(async () => {
    if (syncTimeoutRef.current) {
      clearTimeout(syncTimeoutRef.current);
    }

    const currentState = stateRef.current;
    if (currentState !== null) {
      await saveState(currentState);
    }
  }, [saveState]);

  // 清除状态
  const clearState = useCallback(async () => {
    if (!agentId || !type) return false;

    try {
      // 模拟异步操作（保持接口一致性）
      await new Promise(resolve => setTimeout(resolve, 5));

      const result = memoryStorage.delete(storageKey);

      if (result.success) {
        setState(initialState);
        setLastSync(null);
        return true;
      } else {
        throw new Error('清除失败');
      }

    } catch (err) {
      console.error(`❌ 清除状态失败: ${type}`, err);
      setError(err.message);
      return false;
    }
  }, [agentId, type, initialState, storageKey]);

  // 组件挂载时加载状态（只在agentId和type变化时执行）
  useEffect(() => {
    if (agentId && type) {
      loadState();
    }
  }, [agentId, type]); // 只依赖agentId和type，避免无限循环

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, []);

  // 页面卸载前同步状态（内存存储无需特殊处理）
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
        // 内存存储无需异步保存，直接同步保存
        const currentState = stateRef.current;
        if (currentState !== null) {
          memoryStorage.set(storageKey, currentState);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [agentId, type, storageKey]);
  
  return {
    state,
    setState: updateState,
    loading,
    error,
    lastSync,
    loadState,
    saveState,
    syncState,
    clearState
  };
};

// 公式状态管理Hook
export const useFormulaState = (agentId) => {
  const defaultFormulas = {
    '公式1.1': 'data1 * data3 / 100',
    '公式1.2': 'data2 * 0.1',
    '公式1.3': '(data1 + data2) * data3',
    '公式1.4': 'data4 - data5',
    '公式1.5': 'data1 * data5 / 100'
  };

  return useRedisState(agentId, 'formulas', defaultFormulas);
};

// 编辑状态管理Hook
export const useEditingState = (agentId) => {
  return useRedisState(agentId, 'editing', {});
};

// 导出内存存储工具（用于调试和监控）
export const getMemoryStore = () => {
  const storeData = {};
  for (const [key, value] of memoryStore.entries()) {
    storeData[key] = value;
  }
  return storeData;
};

// 清空所有内存存储（用于重置）
export const clearAllMemoryStore = () => {
  memoryStore.clear();
  console.log('🧹 所有内存存储已清空');
};
