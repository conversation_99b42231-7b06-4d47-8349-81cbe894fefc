'use client';

// 新汇总表组件 v2.0
// 按模块汇总显示，支持右侧总和计算

import { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Input, 
  Button, 
  Space, 
  Divider,
  Table,
  InputNumber,
  Typography
} from 'antd';
import { 
  ExportOutlined, 
  ReloadOutlined, 
  PrinterOutlined,
  EditOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { NewSummaryCalculations } from '../lib/newSummaryCalculations';

const { Title, Text } = Typography;

export default function NewSummaryTable({ 
  customers = [], 
  loading = false,
  onRefresh,
  onExport 
}) {
  // 状态管理
  const [summaryData, setSummaryData] = useState(null);
  const [manualInputs, setManualInputs] = useState({ cashFlow: 0, discount: 0 });
  const [isEditing, setIsEditing] = useState(false);

  // 计算汇总数据
  const calculatedSummary = useMemo(() => {
    return NewSummaryCalculations.calculateModuleSummary(customers);
  }, [customers]);

  // 更新汇总数据
  useEffect(() => {
    setSummaryData(calculatedSummary);
    
    // 加载手动输入数据
    const savedInputs = NewSummaryCalculations.getManualInputs();
    setManualInputs(savedInputs);
  }, [calculatedSummary]);

  // 处理手动输入保存
  const handleSaveManualInputs = () => {
    NewSummaryCalculations.saveManualInputs(manualInputs);
    setIsEditing(false);
    
    // 重新计算汇总数据
    const newSummary = NewSummaryCalculations.calculateModuleSummary(customers);
    setSummaryData(newSummary);
  };

  // 处理导出
  const handleExport = () => {
    if (onExport && summaryData) {
      onExport(summaryData);
    }
  };

  // 渲染模块组
  const renderModuleGroup = (group, groupIndex) => {
    return (
      <div key={groupIndex} style={{ marginBottom: '24px' }}>
        {/* 分组标题 */}
        <div style={{ 
          marginBottom: '16px',
          padding: '8px 16px',
          backgroundColor: '#f0f8ff',
          borderRadius: '6px',
          borderLeft: '4px solid #1890ff'
        }}>
          <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
            {group.groupName}
          </Text>
          {group.groupType === 'special' && (
            <Text style={{ marginLeft: '16px', color: '#666', fontSize: '12px' }}>
              特殊处理 - 包含1.2结果1数据连接
            </Text>
          )}
        </div>

        {/* 特殊分隔线 */}
        {group.hasSeparator && (
          <Divider style={{ 
            borderColor: '#1890ff', 
            borderWidth: '2px',
            margin: '16px 0' 
          }} />
        )}

        {/* 模块数据表格 */}
        <Row gutter={group.groupType === 'compact' ? 8 : 16}>
          {group.modules.map((module, moduleIndex) => (
            <Col 
              key={moduleIndex} 
              span={group.modules.length === 1 ? 24 : 12}
              style={{ marginBottom: '16px' }}
            >
              {renderModuleCard(module, group.groupType)}
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  // 渲染模块卡片
  const renderModuleCard = (module, groupType) => {
    if (module.isEmpty) {
      return (
        <Card 
          title={module.displayName}
          size="small"
          style={{ 
            backgroundColor: '#f5f5f5',
            border: '1px dashed #d9d9d9'
          }}
        >
          <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
            暂无数据
          </div>
        </Card>
      );
    }

    return (
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
              {module.displayName}
            </span>
            <span style={{ fontSize: '12px', color: '#666' }}>
              客户数: {module.customerCount}
            </span>
          </div>
        }
        size="small"
        style={{ 
          backgroundColor: '#e6f7ff',
          border: '1px solid #91d5ff'
        }}
      >
        {/* 数据字段 */}
        <div style={{ marginBottom: '12px' }}>
          <Text strong style={{ color: '#1890ff', fontSize: '13px' }}>数据字段:</Text>
          <div style={{ marginTop: '4px' }}>
            {Object.entries(module.dataFields).map(([fieldName, value]) => (
              <div key={fieldName} style={{ 
                display: 'inline-block', 
                margin: '2px 8px 2px 0',
                padding: '2px 8px',
                backgroundColor: '#ffffff',
                borderRadius: '4px',
                fontSize: '12px'
              }}>
                <Text strong>{fieldName}:</Text> {NewSummaryCalculations.formatDisplayValue(value, 'decimal')}
              </div>
            ))}
          </div>
        </div>

        {/* 结果字段 */}
        <div style={{ marginBottom: '8px' }}>
          <Text strong style={{ color: '#52c41a', fontSize: '13px' }}>结果字段:</Text>
          <div style={{ marginTop: '4px' }}>
            {Object.entries(module.resultFields).map(([fieldName, value]) => (
              <div key={fieldName} style={{ 
                display: 'inline-block', 
                margin: '2px 8px 2px 0',
                padding: '2px 8px',
                backgroundColor: '#f6ffed',
                borderRadius: '4px',
                fontSize: '12px',
                border: '1px solid #b7eb8f'
              }}>
                <Text strong>{fieldName}:</Text> {NewSummaryCalculations.formatDisplayValue(value, 'decimal')}
              </div>
            ))}
          </div>
        </div>

        {/* 特殊连接显示 */}
        {module.specialConnection && (
          <div style={{ 
            marginTop: '8px',
            padding: '8px',
            backgroundColor: '#fff7e6',
            borderRadius: '4px',
            border: '1px solid #ffd591'
          }}>
            <Text style={{ fontSize: '12px', color: '#fa8c16' }}>
              🔗 {module.specialConnection.description}: {NewSummaryCalculations.formatDisplayValue(module.specialConnection.value, 'decimal')}
            </Text>
          </div>
        )}

        {/* 模块总和 */}
        <div style={{ 
          marginTop: '12px',
          padding: '8px',
          backgroundColor: '#fff2e8',
          borderRadius: '4px',
          textAlign: 'center'
        }}>
          <Text strong style={{ color: '#fa541c', fontSize: '14px' }}>
            模块总和: {NewSummaryCalculations.formatDisplayValue(module.totalSum, 'currency')}
          </Text>
        </div>
      </Card>
    );
  };

  // 渲染右侧总和区域
  const renderRightSummary = () => {
    if (!summaryData?.rightSummary) return null;

    const { precise, rounded } = summaryData.rightSummary;

    return (
      <Card 
        title={
          <div style={{ textAlign: 'center' }}>
            <Title level={4} style={{ margin: 0, color: '#52c41a' }}>
              📊 总和计算区域
            </Title>
          </div>
        }
        style={{ 
          backgroundColor: '#f6ffed',
          border: '2px solid #52c41a',
          height: 'fit-content'
        }}
      >
        {/* 6项指标 */}
        <div style={{ marginBottom: '16px' }}>
          {Object.entries(precise).map(([key, value]) => (
            <div key={key} style={{ 
              marginBottom: '12px',
              padding: '8px 12px',
              backgroundColor: key === '所有总和合计' ? '#fff7e6' : '#ffffff',
              borderRadius: '6px',
              border: key === '所有总和合计' ? '2px solid #faad14' : '1px solid #d9d9d9'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text strong style={{ 
                  color: key === '所有总和合计' ? '#fa8c16' : '#333',
                  fontSize: key === '所有总和合计' ? '16px' : '14px'
                }}>
                  {key}:
                </Text>
                {(key === '收付现金' || key === '减免') ? (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {isEditing ? (
                      <InputNumber
                        value={manualInputs[key === '收付现金' ? 'cashFlow' : 'discount']}
                        onChange={(val) => setManualInputs(prev => ({
                          ...prev,
                          [key === '收付现金' ? 'cashFlow' : 'discount']: val || 0
                        }))}
                        style={{ width: '120px' }}
                        precision={2}
                      />
                    ) : (
                      <Text style={{ fontSize: '14px', marginRight: '8px' }}>
                        {NewSummaryCalculations.formatDisplayValue(value, 'currency')}
                      </Text>
                    )}
                  </div>
                ) : (
                  <Text style={{ fontSize: '14px' }}>
                    {NewSummaryCalculations.formatDisplayValue(value, 'currency')}
                  </Text>
                )}
              </div>
              
              {/* 第二排：四舍五入数字 */}
              <div style={{ 
                marginTop: '4px', 
                textAlign: 'right',
                fontSize: '12px',
                color: '#666'
              }}>
                四舍五入: {NewSummaryCalculations.formatDisplayValue(rounded[key], 'currency')}
              </div>
            </div>
          ))}
        </div>

        {/* 编辑按钮 */}
        <div style={{ textAlign: 'center', marginTop: '16px' }}>
          {isEditing ? (
            <Space>
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={handleSaveManualInputs}
                size="small"
              >
                保存
              </Button>
              <Button 
                onClick={() => setIsEditing(false)}
                size="small"
              >
                取消
              </Button>
            </Space>
          ) : (
            <Button 
              type="dashed" 
              icon={<EditOutlined />}
              onClick={() => setIsEditing(true)}
              size="small"
            >
              编辑手动输入
            </Button>
          )}
        </div>
      </Card>
    );
  };

  if (!summaryData) {
    return (
      <Card loading={loading}>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          正在加载汇总数据...
        </div>
      </Card>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 标题和操作按钮 */}
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              📊 7模块汇总表 v2.0
            </Title>
            <Space>
              <Button 
                type="primary" 
                icon={<ExportOutlined />} 
                onClick={handleExport}
                size="small"
              >
                导出Excel
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={onRefresh}
                loading={loading}
                size="small"
              >
                刷新数据
              </Button>
              <Button 
                icon={<PrinterOutlined />} 
                size="small"
              >
                打印截图
              </Button>
            </Space>
          </div>
        }
        style={{ marginBottom: '24px' }}
      >
        <div style={{ 
          padding: '16px',
          backgroundColor: '#f0f8ff',
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <Text style={{ fontSize: '14px', color: '#666' }}>
            按模块汇总显示 | 总客户数: {summaryData.metadata.totalCustomers} | 
            活跃模块: {summaryData.metadata.totalModules} | 
            更新时间: {summaryData.metadata.lastUpdated.toLocaleString()}
          </Text>
        </div>
      </Card>

      {/* 主要内容区域 */}
      <Row gutter={24}>
        {/* 左侧：模块汇总区域 */}
        <Col span={16}>
          <Card 
            title="模块数据汇总"
            style={{ backgroundColor: '#fafafa' }}
          >
            {summaryData.moduleGroups.map((group, index) => 
              renderModuleGroup(group, index)
            )}
          </Card>
        </Col>

        {/* 右侧：总和计算区域 */}
        <Col span={8}>
          {renderRightSummary()}
          
          {/* 右下角额外数据区域 */}
          <Card 
            title="额外数据"
            size="small"
            style={{ 
              marginTop: '16px',
              backgroundColor: '#f9f9f9'
            }}
          >
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              其他类别数据
              <br />
              <Text style={{ fontSize: '12px' }}>
                可在此添加其他业务数据
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
