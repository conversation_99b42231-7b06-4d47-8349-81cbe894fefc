// 基础Playwright测试
const { chromium } = require('playwright');

async function basicTest() {
  console.log('🚀 启动基础测试...');
  
  try {
    const browser = await chromium.launch({ 
      headless: false,
      slowMo: 1000
    });
    
    console.log('✅ 浏览器启动成功');
    
    const page = await browser.newPage();
    console.log('✅ 新页面创建成功');
    
    await page.goto('http://localhost:3000/formula-config');
    console.log('✅ 页面导航成功');
    
    await page.waitForTimeout(5000);
    console.log('✅ 等待完成');
    
    const title = await page.title();
    console.log(`📄 页面标题: ${title}`);
    
    // 检查页面元素
    const hasFormulaGrid = await page.locator('text=公式配置网格').isVisible();
    console.log(`📊 公式配置网格可见: ${hasFormulaGrid}`);
    
    if (hasFormulaGrid) {
      console.log('🎉 页面加载成功！');
      
      // 尝试点击添加客户按钮
      const addButton = page.locator('button:has-text("添加客户")').first();
      if (await addButton.isVisible()) {
        console.log('🔘 找到添加客户按钮，点击...');
        await addButton.click();
        await page.waitForTimeout(2000);
        console.log('✅ 添加客户按钮点击成功');
        
        // 尝试输入数据
        const inputs = await page.$$('table input');
        console.log(`📝 找到 ${inputs.length} 个输入框`);
        
        if (inputs.length > 0) {
          try {
            await inputs[0].fill('100');
            console.log('✅ 成功输入数据到第一个输入框');
          } catch (e) {
            console.log('❌ 输入数据失败:', e.message);
          }
        }
      } else {
        console.log('⚠️ 未找到添加客户按钮');
      }
    } else {
      console.log('❌ 页面加载失败');
    }
    
    console.log('\n🔍 浏览器保持打开状态，请手动检查...');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

basicTest();
