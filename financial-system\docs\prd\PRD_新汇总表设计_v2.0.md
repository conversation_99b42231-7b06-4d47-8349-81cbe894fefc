# 新汇总表设计产品需求文档 (PRD v2.0)

## 📋 文档信息
- **版本**: v2.0
- **创建日期**: 2025-07-28
- **负责人**: Emma (产品经理)
- **审核人**: Mike (团队领袖)
- **开发人员**: <PERSON> (工程师)

## 🎯 背景与问题陈述

### 当前问题
- 现有汇总表按客户分组显示，不符合业务需求
- 需要按模块汇总显示，便于客户查看各模块的整体表现
- 缺少右侧总和计算区域
- 界面布局不适合截图展示给客户

### 业务目标
- 重新设计汇总表，按模块(1.1-1.7)显示汇总数据
- 每个模块显示该模块内所有客户的汇总信息
- 添加右侧总和计算区域，提供6项关键指标
- 优化界面布局，适合截图展示给客户

## 🏗️ 新汇总表整体架构

### 主要区域划分
```
┌─────────────────────────────────────┬─────────────────┐
│                                     │                 │
│           模块汇总区域                │   右侧总和区域    │
│        (按模块分组显示)               │   (6项指标)     │
│                                     │                 │
├─────────────────────────────────────┼─────────────────┤
│                                     │                 │
│                                     │  右下角额外数据  │
│                                     │                 │
└─────────────────────────────────────┴─────────────────┘
```

## 📊 模块分组详细设计

### 第一组：模块1.1和1.2
**数据结构**:
```javascript
{
  groupName: "第一组",
  modules: [
    {
      id: "module1_1",
      name: "客户1.1", 
      dataFields: ["数据1", "数据2", "数据6", "数据7"],
      resultFields: ["结果1", "结果2", "结果3", "结果4", "结果5"],
      summaryData: {
        数据1: 总和,
        数据2: 总和,
        数据6: 总和,
        数据7: 总和,
        结果1: 总和,
        结果2: 总和,
        结果3: 总和,
        结果4: 总和,
        结果5: 总和
      }
    },
    {
      id: "module1_2",
      name: "客户1.2",
      // 同样结构
    }
  ]
}
```

### 第二组：模块1.3（特殊处理）
**特殊要求**:
- 加横线分隔
- 数据字段：数据1、数据2
- 结果字段：结果1
- 同时接入1.2的结果1数据并添加说明

**数据结构**:
```javascript
{
  groupName: "第二组",
  isSpecial: true,
  separator: true,
  modules: [
    {
      id: "module1_3",
      name: "客户1.3",
      dataFields: ["数据1", "数据2"],
      resultFields: ["结果1"],
      specialConnection: {
        fromModule: "module1_2",
        fromField: "结果1",
        description: "接入1.2结果1数据"
      }
    }
  ]
}
```

### 第三组：模块1.4和1.5
**布局要求**: 与第一组类似，四个模块(1.1,1.2,1.4,1.5)放在一起

### 第四组：模块1.6和1.7
**数据字段**: 数据1、数据2、数据3
**结果字段**: 结果1、结果2、结果3、结果4
**布局要求**: 三个模块间距不要太大

## 💰 右侧总和区域设计

### 6项关键指标
```javascript
{
  rightSummaryArea: {
    indicators: [
      {
        name: "收入",
        calculation: "数量×价格的总和结果",
        type: "calculated",
        formula: "sum(quantity * price)"
      },
      {
        name: "付出", 
        calculation: "-数量×价格的总和结果",
        type: "calculated",
        formula: "-sum(quantity * price)"
      },
      {
        name: "收付现金",
        type: "manual_input",
        editable: true
      },
      {
        name: "上周余额",
        type: "auto_generated",
        source: "previous_week_table"
      },
      {
        name: "减免",
        type: "manual_input", 
        editable: true
      },
      {
        name: "所有总和合计",
        calculation: "以上5项的合计",
        type: "calculated",
        formula: "sum(above_5_items)"
      }
    ]
  }
}
```

### 显示格式要求
- **第1排**: 显示实际精确数字
- **第2排**: 显示四舍五入到百位的数字

```javascript
{
  displayFormat: {
    row1: {
      type: "precise",
      format: "decimal(2)"
    },
    row2: {
      type: "rounded",
      format: "round_to_hundred"
    }
  }
}
```

## 🎨 界面布局设计规范

### 颜色编码方案
- **蓝色区域**: 模块数据显示区域
- **绿色区域**: 右侧总和计算区域  
- **黄色区域**: 重要数据高亮显示
- **白色区域**: 输入和编辑区域

### 模块分组视觉分隔
- 第一组和第二组之间：加粗分隔线
- 第二组特殊处理：横线分隔 + 特殊标识
- 第三组和第四组：适当间距
- 第四组内部：紧凑布局，间距较小

### 响应式布局
- 适配不同屏幕尺寸
- 保持截图效果的清晰度
- 确保客户查看时的可读性

## 🔢 数据计算逻辑

### 模块汇总计算
```javascript
// 每个模块的汇总计算
function calculateModuleSummary(moduleId) {
  const customers = getCustomersByModule(moduleId);
  const summary = {
    dataFields: {},
    resultFields: {}
  };
  
  // 计算数据字段总和
  MODULE_CONFIG[moduleId].dataFields.forEach(field => {
    summary.dataFields[field] = customers.reduce((sum, customer) => 
      sum + (customer[field] || 0), 0);
  });
  
  // 计算结果字段总和（重要：这是总和的基础）
  MODULE_CONFIG[moduleId].resultFields.forEach(field => {
    summary.resultFields[field] = customers.reduce((sum, customer) => 
      sum + (customer[field] || 0), 0);
  });
  
  return summary;
}
```

### 右侧总和计算
```javascript
// 右侧6项指标计算
function calculateRightSummary(allModuleSummaries) {
  return {
    收入: calculateIncome(allModuleSummaries),
    付出: -calculateIncome(allModuleSummaries),
    收付现金: getManualInput('cash_flow'),
    上周余额: getPreviousWeekBalance(),
    减免: getManualInput('discount'),
    所有总和合计: function() {
      return this.收入 + this.付出 + this.收付现金 + this.上周余额 + this.减免;
    }
  };
}
```

## 📱 用户交互设计

### 可编辑字段
- 收付现金：支持手动输入
- 减免：支持手动输入
- 实时计算更新

### 数据展示
- 精确数字和四舍五入数字同时显示
- 支持数据导出功能
- 支持打印和截图优化

## 🎯 成功指标

### 功能指标
- 7个模块数据正确汇总显示
- 右侧6项指标计算准确
- 手动输入功能正常工作
- 数据实时更新响应

### 用户体验指标
- 界面布局清晰美观
- 截图效果满足客户展示需求
- 操作流畅，响应时间<1秒
- 数据可读性强

### 技术指标
- 代码结构清晰，易于维护
- 性能优秀，支持大数据量
- 错误处理完善，系统稳定
- 兼容性良好，支持多浏览器

## 🚀 开发计划

### 第一阶段：数据结构重构
- 重新设计按模块汇总的数据结构
- 更新计算引擎逻辑
- 实现右侧总和计算

### 第二阶段：界面布局开发  
- 实现新的汇总表布局
- 添加模块分组显示
- 实现右侧总和区域

### 第三阶段：功能完善
- 添加手动输入功能
- 实现数据实时更新
- 优化截图展示效果

### 第四阶段：测试验证
- 功能测试验证
- 界面效果测试
- 性能和稳定性测试

## 📋 验收标准

### 必须满足
- ✅ 7个模块按要求分组显示
- ✅ 每个模块显示正确的汇总数据
- ✅ 右侧6项指标计算准确
- ✅ 界面布局符合效果图要求
- ✅ 支持手动输入和自动计算
- ✅ 截图效果清晰美观

### 优化目标
- ✅ 响应速度优秀
- ✅ 用户体验流畅
- ✅ 代码质量高
- ✅ 系统稳定可靠

---

**产品经理**: Emma  
**文档版本**: v2.0  
**创建日期**: 2025-07-28
