# 技术方案设计 - 财务管理系统 (SQLite3版本)

## 1. 文档信息
- **项目名称**: 财务管理系统（代理商盈亏跟踪器）
- **版本**: v1.0 (简化版)
- **创建日期**: 2025年7月27日
- **负责人**: Bob (架构师)
- **数据库**: SQLite3
- **特殊说明**: 暂不考虑实时计算性能优化和复杂权限安全机制

## 2. 技术栈选择

### 2.1 前端技术栈
**框架选择**: **Next.js 14 + React 18**
- **理由**: 
  - 服务端渲染(SSR)支持，提升首屏加载速度
  - 内置路由系统，简化页面管理
  - API Routes功能，前后端一体化开发
  - TypeScript原生支持
  - 优秀的开发体验和生态

**UI组件库**: **Ant Design + Tailwind CSS**
- **Ant Design**: 提供丰富的表格、表单、日期选择器等业务组件
- **Tailwind CSS**: 快速样式开发，保持设计一致性

**状态管理**: **Zustand**
- **理由**: 轻量级状态管理，学习成本低，适合中小型项目

### 2.2 后端技术栈
**运行环境**: **Node.js + Next.js API Routes**
- **理由**: 
  - 与前端共享代码和类型定义
  - 简化部署流程
  - 减少技术栈复杂度

**数据库**: **SQLite3 + Prisma ORM**
- **SQLite3优势**:
  - 零配置，文件型数据库
  - 适合中小型应用
  - 支持事务和并发
  - 部署简单，无需独立数据库服务器
- **Prisma ORM优势**:
  - 类型安全的数据库访问
  - 自动生成数据库迁移
  - 优秀的开发体验
  - 内置连接池管理

### 2.3 开发工具链
- **TypeScript**: 类型安全，减少运行时错误
- **ESLint + Prettier**: 代码规范和格式化
- **Playwright**: 端到端测试（按团队要求强制使用）
- **Jest**: 单元测试框架

## 3. 系统架构设计

### 3.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Next.js) │    │  API Routes     │    │  SQLite3 + Prisma│
│                 │    │                 │    │                 │
│  - React组件    │◄──►│  - 业务逻辑     │◄──►│  - 数据存储     │
│  - 状态管理     │    │  - 数据验证     │    │  - 数据模型     │
│  - 用户界面     │    │  - 公式计算     │    │  - 事务处理     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 目录结构
```
财务管理系统2/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/
│   │   │   └── login/          # 登录页面
│   │   ├── dashboard/          # 主仪表盘
│   │   ├── reports/
│   │   │   ├── weekly/         # 周利润表
│   │   │   └── yearly/         # 年利润表
│   │   ├── summary/            # 汇总表
│   │   └── api/                # API路由
│   │       ├── auth/
│   │       ├── agents/
│   │       ├── customers/
│   │       └── calculations/
│   ├── components/             # 共享组件
│   │   ├── ui/                 # 基础UI组件
│   │   ├── forms/              # 表单组件
│   │   └── tables/             # 表格组件
│   ├── lib/                    # 工具库
│   │   ├── db.ts              # 数据库连接
│   │   ├── calculations.ts     # 计算引擎
│   │   └── validations.ts      # 数据验证
│   ├── store/                  # 状态管理
│   └── types/                  # TypeScript类型定义
├── prisma/
│   ├── schema.prisma          # 数据库模型
│   └── migrations/            # 数据库迁移
├── public/                    # 静态资源
├── tests/                     # 测试文件
└── docs/                      # 项目文档
```

## 4. 数据库设计 (SQLite3 + Prisma)

### 4.1 数据模型设计
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 用户表 (简化版，无复杂权限)
model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  password  String   // 简单加密存储
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 代理商表
model Agent {
  id          Int        @id @default(autoincrement())
  name        String
  clientName  String?
  userName    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  customers   Customer[]
  periods     Period[]
}

// 客户表
model Customer {
  id          Int      @id @default(autoincrement())
  name        String
  userName    String
  agentId     Int
  agent       Agent    @relation(fields: [agentId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 数据字段 (8种类型)
  data1       BigInt?   // 千万整数
  data2       BigInt?   // 十亿整数  
  data3       Float?    // 百分比 (存储为小数)
  data4       Float?    // 高精度数字 (8位小数)
  data5       Float?    // 百分比 (存储为小数)
  data6       BigInt?   // 计算结果 (只读)
  data7       BigInt?   // 计算结果 (只读)
  data8       Float?    // 计算结果 (只读)
  
  financialData FinancialData[]
}

// 周期管理表
model Period {
  id          Int      @id @default(autoincrement())
  agentId     Int
  agent       Agent    @relation(fields: [agentId], references: [id])
  periodType  String   // "1-3期" 或 "4-7期"
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 财务数据表
model FinancialData {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id])
  
  // 公式相关
  formula1_1  String?
  formula1_2  String?
  formula1_3  String?
  formula1_4  String?
  formula1_5  String?
  
  // 计算结果
  result1_1   Float?
  result1_2   Float?
  result1_3   Float?
  result1_4   Float?
  result1_5   Float?
  
  // 汇总数据
  totalSum    Float?
  roundedSum  Int?
  
  // 右侧统计区域
  quantity    Int?
  price       Float?
  income      Float?
  expense     Float?
  cashFlow    Float?
  lastWeekBalance Float?
  reduction   Float?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// 全局公式配置表
model GlobalFormula {
  id          Int      @id @default(autoincrement())
  name        String   @unique // 如: "formula1_1", "formula1_2"
  expression  String   // 公式表达式
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

### 4.2 数据库特性利用
**SQLite3优势利用**:
- **事务支持**: 确保数据一致性
- **JSON支持**: 存储复杂的公式配置
- **全文搜索**: 支持客户名称搜索
- **触发器**: 自动计算衍生数据

## 5. 核心功能实现方案

### 5.1 公式计算引擎 (简化版)
```typescript
// src/lib/calculations.ts
export class FormulaEngine {
  // 简单的公式解析器
  static parseFormula(expression: string, data: Record<string, number>): number {
    // 替换变量名为实际值
    let formula = expression;
    Object.entries(data).forEach(([key, value]) => {
      formula = formula.replace(new RegExp(key, 'g'), value.toString());
    });
    
    // 使用Function构造器安全执行数学表达式
    try {
      return new Function('return ' + formula)();
    } catch (error) {
      console.error('公式计算错误:', error);
      return 0;
    }
  }
  
  // 批量计算客户数据
  static calculateCustomerData(customer: CustomerData): CalculationResult {
    const results: CalculationResult = {};
    
    // 计算数据6: 数据4 - 数据5
    if (customer.data4 !== null && customer.data5 !== null) {
      results.data6 = Math.round(customer.data4 - customer.data5);
    }
    
    // 其他计算逻辑...
    return results;
  }
}
```

### 5.2 API路由设计
```typescript
// src/app/api/customers/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { FormulaEngine } from '@/lib/calculations';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 创建客户数据
    const customer = await prisma.customer.create({
      data: {
        name: data.name,
        userName: data.userName,
        agentId: data.agentId,
        data1: data.data1,
        data2: data.data2,
        data3: data.data3,
        data4: data.data4,
        data5: data.data5,
      }
    });
    
    // 计算衍生数据
    const calculations = FormulaEngine.calculateCustomerData(customer);
    
    // 更新计算结果
    const updatedCustomer = await prisma.customer.update({
      where: { id: customer.id },
      data: {
        data6: calculations.data6,
        data7: calculations.data7,
        data8: calculations.data8,
      }
    });
    
    return NextResponse.json(updatedCustomer);
  } catch (error) {
    return NextResponse.json({ error: '创建客户失败' }, { status: 500 });
  }
}
```

### 5.3 前端组件设计
```typescript
// src/components/tables/CustomerDataTable.tsx
'use client';

import { Table, Input, Button } from 'antd';
import { useState, useEffect } from 'react';
import { useCustomerStore } from '@/store/customerStore';

export default function CustomerDataTable({ agentId }: { agentId: number }) {
  const { customers, addCustomer, updateCustomer } = useCustomerStore();
  
  const columns = [
    {
      title: '客户名',
      dataIndex: 'name',
      render: (text: string, record: Customer) => (
        <Input 
          value={text} 
          onChange={(e) => updateCustomer(record.id, { name: e.target.value })}
        />
      ),
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      render: (text: string, record: Customer) => (
        <Input 
          value={text} 
          onChange={(e) => updateCustomer(record.id, { userName: e.target.value })}
        />
      ),
    },
    // 数据1-8列...
    {
      title: '数据1 (千万整数)',
      dataIndex: 'data1',
      render: (value: number, record: Customer) => (
        <Input 
          type="number" 
          value={value} 
          onChange={(e) => handleDataChange(record.id, 'data1', e.target.value)}
        />
      ),
    },
    // 更多列...
  ];
  
  const handleDataChange = async (id: number, field: string, value: string) => {
    // 更新本地状态
    updateCustomer(id, { [field]: parseFloat(value) });
    
    // 触发重新计算
    await recalculateCustomer(id);
  };
  
  return (
    <Table 
      columns={columns} 
      dataSource={customers}
      pagination={false}
      scroll={{ x: 1500 }}
    />
  );
}
```

## 6. 部署方案 (Context7环境)

### 6.1 部署配置
```json
// package.json
{
  "name": "financial-management-system",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate"
  },
  "dependencies": {
    "next": "14.0.0",
    "react": "18.0.0",
    "prisma": "^5.0.0",
    "@prisma/client": "^5.0.0",
    "antd": "^5.0.0",
    "zustand": "^4.0.0"
  }
}
```

### 6.2 环境配置
```env
# .env.local
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
```

### 6.3 构建和部署流程
```bash
# 1. 安装依赖
npm install

# 2. 生成Prisma客户端
npm run db:generate

# 3. 初始化数据库
npm run db:push

# 4. 构建项目
npm run build

# 5. 启动生产服务
npm run start
```

## 7. 开发计划 (简化版)

### 7.1 第一阶段 (1周): 基础搭建
- Next.js项目初始化
- Prisma + SQLite3配置
- 基础UI组件开发
- 简单登录功能

### 7.2 第二阶段 (2周): 核心功能
- 主仪表盘开发
- 客户数据管理
- 基础公式计算
- 报表页面

### 7.3 第三阶段 (1周): 完善优化
- 数据验证完善
- UI/UX优化
- 基础测试
- 部署准备

## 8. 技术优势总结

### 8.1 SQLite3选择优势
- **零配置**: 无需独立数据库服务器
- **高性能**: 对于中小型应用性能优秀
- **可靠性**: 成熟稳定，广泛使用
- **便携性**: 单文件数据库，易于备份和迁移

### 8.2 技术栈优势
- **开发效率**: Next.js全栈开发，减少技术栈复杂度
- **类型安全**: TypeScript + Prisma提供端到端类型安全
- **开发体验**: 优秀的开发工具链和热重载
- **部署简单**: 单一应用部署，运维成本低

### 8.3 扩展性考虑
- **数据库迁移**: Prisma支持后续迁移到PostgreSQL/MySQL
- **性能优化**: 后续可添加Redis缓存
- **功能扩展**: 模块化设计支持功能扩展
- **安全增强**: 预留安全机制扩展接口

这个技术方案在满足当前需求的同时，为未来的功能扩展和性能优化预留了充分的空间。

## 9. 关键技术实现细节

### 9.1 数据类型处理策略
```typescript
// src/types/customer.ts
export interface CustomerData {
  id: number;
  name: string;
  userName: string;

  // 8种数据类型的精确定义
  data1: bigint | null;     // 千万整数，显示时四舍五入到0位小数
  data2: bigint | null;     // 十亿整数，显示时四舍五入到0位小数
  data3: number | null;     // 百分比，存储为小数，显示为百分比格式(保留2位)
  data4: number | null;     // 高精度数字，保留8位小数
  data5: number | null;     // 百分比，存储为小数，显示为百分比格式(保留2位)
  data6: bigint | null;     // 计算结果，只读，四舍五入到0位小数
  data7: bigint | null;     // 计算结果，只读，四舍五入到0位小数
  data8: number | null;     // 计算结果，只读，精度待定
}

// 数据格式化工具
export class DataFormatter {
  static formatData1(value: bigint | null): string {
    if (value === null) return '';
    return Math.round(Number(value)).toLocaleString();
  }

  static formatData3(value: number | null): string {
    if (value === null) return '';
    return (value * 100).toFixed(2) + '%';
  }

  static formatData4(value: number | null): string {
    if (value === null) return '';
    return value.toFixed(8);
  }
}
```

### 9.2 公式引擎详细实现
```typescript
// src/lib/formula-engine.ts
export class AdvancedFormulaEngine {
  private static readonly ALLOWED_FUNCTIONS = ['Math.abs', 'Math.round', 'Math.floor', 'Math.ceil'];

  // 安全的公式执行环境
  static createSafeContext(data: Record<string, number>) {
    return {
      ...data,
      Math: {
        abs: Math.abs,
        round: Math.round,
        floor: Math.floor,
        ceil: Math.ceil,
        min: Math.min,
        max: Math.max,
      }
    };
  }

  // 公式验证
  static validateFormula(expression: string): { valid: boolean; error?: string } {
    // 检查危险函数调用
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /setTimeout\s*\(/,
      /setInterval\s*\(/,
      /require\s*\(/,
      /import\s*\(/,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(expression)) {
        return { valid: false, error: '公式包含不安全的函数调用' };
      }
    }

    return { valid: true };
  }

  // 执行公式计算
  static executeFormula(expression: string, context: Record<string, number>): number {
    const validation = this.validateFormula(expression);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    try {
      const safeContext = this.createSafeContext(context);
      const func = new Function(...Object.keys(safeContext), `return ${expression}`);
      return func(...Object.values(safeContext));
    } catch (error) {
      console.error('公式执行错误:', error);
      return 0;
    }
  }
}
```

### 9.3 状态管理实现 (Zustand)
```typescript
// src/store/customerStore.ts
import { create } from 'zustand';
import { CustomerData } from '@/types/customer';

interface CustomerStore {
  customers: CustomerData[];
  loading: boolean;

  // Actions
  setCustomers: (customers: CustomerData[]) => void;
  addCustomer: (customer: Omit<CustomerData, 'id'>) => Promise<void>;
  updateCustomer: (id: number, updates: Partial<CustomerData>) => Promise<void>;
  deleteCustomer: (id: number) => Promise<void>;
  recalculateCustomer: (id: number) => Promise<void>;
}

export const useCustomerStore = create<CustomerStore>((set, get) => ({
  customers: [],
  loading: false,

  setCustomers: (customers) => set({ customers }),

  addCustomer: async (customerData) => {
    set({ loading: true });
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });

      if (response.ok) {
        const newCustomer = await response.json();
        set(state => ({
          customers: [...state.customers, newCustomer],
          loading: false
        }));
      }
    } catch (error) {
      console.error('添加客户失败:', error);
      set({ loading: false });
    }
  },

  updateCustomer: async (id, updates) => {
    // 乐观更新
    set(state => ({
      customers: state.customers.map(c =>
        c.id === id ? { ...c, ...updates } : c
      )
    }));

    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });

      if (response.ok) {
        const updatedCustomer = await response.json();
        set(state => ({
          customers: state.customers.map(c =>
            c.id === id ? updatedCustomer : c
          )
        }));
      }
    } catch (error) {
      console.error('更新客户失败:', error);
      // 回滚乐观更新
      // 这里可以添加错误处理逻辑
    }
  },

  recalculateCustomer: async (id) => {
    try {
      const response = await fetch(`/api/customers/${id}/calculate`, {
        method: 'POST',
      });

      if (response.ok) {
        const calculatedCustomer = await response.json();
        set(state => ({
          customers: state.customers.map(c =>
            c.id === id ? calculatedCustomer : c
          )
        }));
      }
    } catch (error) {
      console.error('重新计算失败:', error);
    }
  },
}));
```

## 10. 测试策略 (Playwright集成)

### 10.1 E2E测试配置
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',

  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### 10.2 核心功能测试用例
```typescript
// tests/e2e/customer-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('客户数据管理', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/login');
    await page.fill('#username', 'admin');
    await page.fill('#password', '123456');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('添加新客户', async ({ page }) => {
    // 导航到汇总表页面
    await page.click('text=代理商数据录入表格');

    // 添加新客户行
    await page.click('text=添加客户行');

    // 填写客户信息
    const lastRow = page.locator('table tbody tr').last();
    await lastRow.locator('input[placeholder="客户名"]').fill('测试客户');
    await lastRow.locator('input[placeholder="用户名"]').fill('测试用户');

    // 填写数据字段
    await lastRow.locator('input[data-field="data1"]').fill('1000000');
    await lastRow.locator('input[data-field="data2"]').fill('2000000');
    await lastRow.locator('input[data-field="data3"]').fill('15.5');

    // 保存数据
    await page.click('text=保存数据');

    // 验证数据已保存
    await expect(page.locator('text=测试客户')).toBeVisible();
    await expect(page.locator('text=测试用户')).toBeVisible();
  });

  test('公式计算验证', async ({ page }) => {
    await page.goto('/summary/1');

    // 设置全局公式
    await page.fill('#formula1', 'data1 * data3 / 100');
    await page.fill('#formula2', 'data2 * 0.1');

    // 触发计算
    await page.click('text=点击生成结果');

    // 验证计算结果
    const result1 = await page.locator('[data-field="result1"]').inputValue();
    const result2 = await page.locator('[data-field="result2"]').inputValue();

    expect(parseFloat(result1)).toBeGreaterThan(0);
    expect(parseFloat(result2)).toBeGreaterThan(0);
  });

  test('报表生成测试', async ({ page }) => {
    await page.goto('/dashboard');

    // 点击周利润表链接
    await page.click('text=公司周利润表');
    await expect(page).toHaveURL('/reports/weekly');

    // 验证表格数据加载
    await expect(page.locator('table')).toBeVisible();
    await expect(page.locator('table tbody tr')).toHaveCount.greaterThan(0);

    // 验证总计行
    await expect(page.locator('.total-row')).toBeVisible();
  });
});
```

## 11. 部署和运维

### 11.1 Context7环境部署脚本
```bash
#!/bin/bash
# deploy.sh

echo "开始部署财务管理系统..."

# 1. 安装依赖
echo "安装依赖..."
npm ci

# 2. 生成Prisma客户端
echo "生成数据库客户端..."
npx prisma generate

# 3. 数据库迁移
echo "执行数据库迁移..."
npx prisma db push

# 4. 构建应用
echo "构建应用..."
npm run build

# 5. 启动应用
echo "启动应用..."
npm run start

echo "部署完成！应用运行在 http://localhost:3000"
```

### 11.2 数据备份策略
```typescript
// scripts/backup-database.ts
import fs from 'fs';
import path from 'path';

const backupDatabase = () => {
  const dbPath = './prisma/dev.db';
  const backupDir = './backups';
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(backupDir, `backup-${timestamp}.db`);

  // 确保备份目录存在
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  // 复制数据库文件
  fs.copyFileSync(dbPath, backupPath);
  console.log(`数据库备份完成: ${backupPath}`);
};

// 每日自动备份
setInterval(backupDatabase, 24 * 60 * 60 * 1000);
```

这个技术方案提供了一个完整、可行的实现路径，使用SQLite3作为数据库，避免了复杂的实时计算优化和权限管理，专注于核心业务功能的实现。
