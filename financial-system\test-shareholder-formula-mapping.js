// 股东公式映射功能全面测试
const { chromium } = require('playwright');

class ShareholderFormulaMappingTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
    this.baseUrl = 'http://dlsykgzq.w1.luyouxia.net/formula-config/';
  }

  // 启动浏览器
  async setup() {
    console.log('🚀 启动股东公式映射测试...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 2000
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    this.page = await context.newPage();
    
    // 监听页面日志
    this.page.on('console', msg => {
      if (msg.type() === 'log') {
        console.log(`🌐 页面日志: ${msg.text()}`);
      }
    });
  }

  // 记录测试结果
  logTestResult(testName, passed, details = '', expected = '', actual = '') {
    const result = {
      testName,
      passed,
      details,
      expected,
      actual,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
  }

  // 访问页面
  async navigateToPage() {
    console.log('📊 访问股东公式映射页面...');
    await this.page.goto(this.baseUrl, { timeout: 0 });
    await this.page.waitForTimeout(5000);
    
    const title = await this.page.title();
    console.log(`✅ 页面加载完成: "${title}"`);
  }

  // 清空所有股东数据
  async clearAllShareholders() {
    console.log('🧹 清空现有股东数据...');
    try {
      // 查找删除按钮
      const deleteButtons = await this.page.$$('button:has-text("删除"), .delete-btn, [title*="删除"]');
      console.log(`找到 ${deleteButtons.length} 个删除按钮`);
      
      // 点击所有删除按钮
      for (const button of deleteButtons) {
        try {
          await button.click();
          await this.page.waitForTimeout(500);
        } catch (e) {
          // 忽略已删除的按钮
        }
      }
      
      await this.page.waitForTimeout(2000);
      console.log('✅ 股东数据清空完成');
    } catch (error) {
      console.log(`⚠️ 清空股东数据时出现问题: ${error.message}`);
    }
  }

  // 添加指定数量的股东
  async addShareholders(count) {
    console.log(`👥 添加 ${count} 个股东...`);
    
    for (let i = 0; i < count; i++) {
      try {
        const addButton = this.page.locator('button:has-text("添加客户"), button:has-text("添加股东"), button:has-text("添加")').first();
        
        if (await addButton.isVisible()) {
          await addButton.click();
          await this.page.waitForTimeout(2000);
          console.log(`✅ 添加第 ${i + 1} 个股东成功`);
        } else {
          console.log(`❌ 未找到添加按钮，无法添加第 ${i + 1} 个股东`);
          break;
        }
      } catch (error) {
        console.log(`❌ 添加第 ${i + 1} 个股东失败: ${error.message}`);
        break;
      }
    }
  }

  // 验证股东公式映射
  async verifyShareholderFormulaMapping(shareholderCount) {
    console.log(`\n🔍 验证 ${shareholderCount} 个股东的公式映射...`);
    
    try {
      // 获取页面上显示的公式信息
      const formulaInfo = await this.page.evaluate(() => {
        const results = [];
        
        // 查找公式相关的文本或元素
        const formulaElements = document.querySelectorAll('.formula-card, [class*="formula"], text*="公式"');
        formulaElements.forEach((element, index) => {
          const text = element.textContent || element.innerText || '';
          if (text.includes('公式') && (text.includes('1.3') || text.includes('1.4') || text.includes('1.5'))) {
            results.push({
              index,
              text: text.trim(),
              element: element.tagName
            });
          }
        });
        
        return results;
      });
      
      console.log(`📐 找到 ${formulaInfo.length} 个公式相关元素`);
      
      // 验证公式映射规则
      const expectedMappings = this.getExpectedFormulaMappings(shareholderCount);
      
      for (let i = 0; i < shareholderCount; i++) {
        const expected = expectedMappings[i];
        console.log(`   股东${i + 1}: 期望公式 ${expected.formulas.join(' & ')}`);
        
        // 在页面中查找对应的公式
        const foundFormulas = formulaInfo.filter(info => 
          expected.formulas.some(formula => info.text.includes(formula))
        );
        
        if (foundFormulas.length >= expected.formulas.length) {
          this.logTestResult(
            `股东${i + 1}公式映射`,
            true,
            `正确映射到 ${expected.formulas.join(' & ')}`,
            expected.formulas.join(' & '),
            foundFormulas.map(f => f.text).join(' & ')
          );
        } else {
          this.logTestResult(
            `股东${i + 1}公式映射`,
            false,
            `映射不正确或未找到`,
            expected.formulas.join(' & '),
            foundFormulas.map(f => f.text).join(' & ')
          );
        }
      }
      
    } catch (error) {
      console.log(`❌ 验证公式映射时出错: ${error.message}`);
    }
  }

  // 获取期望的公式映射
  getExpectedFormulaMappings(shareholderCount) {
    const mappings = [];
    
    for (let i = 0; i < shareholderCount; i++) {
      if (i === 0) {
        // 第1个股东：公式1.31 & 公式1.32
        mappings.push({ formulas: ['1.31', '1.32'] });
      } else if (i === 1) {
        // 第2个股东：公式1.41 & 公式1.42
        mappings.push({ formulas: ['1.41', '1.42'] });
      } else {
        // 第3个及以上股东：公式1.51 & 公式1.52
        mappings.push({ formulas: ['1.51', '1.52'] });
      }
    }
    
    return mappings;
  }

  // 输入测试数据到指定股东
  async inputTestDataToShareholder(shareholderIndex, testData) {
    console.log(`📝 向股东${shareholderIndex + 1}输入测试数据: ${JSON.stringify(testData)}`);
    
    try {
      // 查找对应股东的输入框
      const tables = await this.page.$$('table');
      
      if (tables.length > shareholderIndex) {
        const table = tables[shareholderIndex];
        const inputs = await table.$$('input[type="text"], input[type="number"], input:not([type])');
        
        console.log(`   找到 ${inputs.length} 个输入框`);
        
        // 输入数据到前几个字段
        const dataFields = ['data1', 'data2', 'data3', 'data4', 'data5'];
        
        for (let i = 0; i < Math.min(dataFields.length, inputs.length); i++) {
          const fieldName = dataFields[i];
          const value = testData[fieldName];
          
          if (value !== undefined) {
            try {
              await inputs[i].click({ clickCount: 3 }); // 选中所有内容
              await inputs[i].fill(value.toString());
              await this.page.waitForTimeout(300);
              
              const actualValue = await inputs[i].inputValue();
              const inputCorrect = actualValue === value.toString();
              
              console.log(`      ${fieldName}: ${value} ${inputCorrect ? '✅' : '❌'}`);
            } catch (e) {
              console.log(`      ${fieldName}: ${value} ❌ (输入失败)`);
            }
          }
        }
      } else {
        console.log(`   ❌ 未找到股东${shareholderIndex + 1}的表格`);
      }
    } catch (error) {
      console.log(`   ❌ 输入数据异常: ${error.message}`);
    }
  }

  // 获取计算结果
  async getCalculationResults() {
    try {
      const results = await this.page.evaluate(() => {
        const resultData = [];
        
        // 查找所有strong元素中的数字
        const strongElements = document.querySelectorAll('strong');
        strongElements.forEach((element, index) => {
          const text = element.textContent.trim();
          const num = parseFloat(text);
          if (!isNaN(num) && isFinite(num)) {
            resultData.push({ index, value: num, element: 'strong' });
          }
        });
        
        // 查找表格中的结果
        const tables = document.querySelectorAll('table');
        tables.forEach((table, tableIndex) => {
          const rows = table.querySelectorAll('tbody tr');
          rows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, cellIndex) => {
              const strongInCell = cell.querySelector('strong');
              if (strongInCell) {
                const text = strongInCell.textContent.trim();
                const num = parseFloat(text);
                if (!isNaN(num) && isFinite(num)) {
                  resultData.push({
                    index: `table_${tableIndex}_row_${rowIndex}_col_${cellIndex}`,
                    value: num,
                    element: 'table_strong'
                  });
                }
              }
            });
          });
        });
        
        return resultData;
      });
      
      return results;
    } catch (error) {
      console.log(`❌ 获取计算结果异常: ${error.message}`);
      return [];
    }
  }

  // 计算预期结果
  calculateExpectedResult(formula, data) {
    try {
      // 替换公式中的变量
      let expression = formula;
      Object.entries(data).forEach(([key, value]) => {
        const regex = new RegExp(key, 'g');
        expression = expression.replace(regex, `(${value})`);
      });
      
      // 处理幂运算
      expression = expression.replace(/\^/g, '**');
      
      // 安全计算
      const result = eval(expression);
      return isNaN(result) || !isFinite(result) ? 0 : result;
    } catch (error) {
      console.log(`❌ 计算预期结果失败: ${formula}, 错误: ${error.message}`);
      return 0;
    }
  }

  // 执行股东数量测试
  async testShareholderCount() {
    console.log('\n📊 执行股东数量测试...');

    const testCases = [
      { count: 1, description: '单个股东测试' },
      { count: 2, description: '两个股东测试' },
      { count: 3, description: '三个股东测试' },
      { count: 4, description: '四个股东测试（验证第4个股东使用1.51&1.52）' }
    ];

    for (const testCase of testCases) {
      console.log(`\n🧪 ${testCase.description} (${testCase.count}个股东)`);

      // 清空现有股东
      await this.clearAllShareholders();

      // 添加指定数量的股东
      await this.addShareholders(testCase.count);

      // 验证公式映射
      await this.verifyShareholderFormulaMapping(testCase.count);

      await this.page.waitForTimeout(2000);
    }
  }

  // 执行复杂公式计算测试
  async testComplexFormulaCalculations() {
    console.log('\n🧮 执行复杂公式计算测试...');

    // 准备测试环境：添加1个股东
    await this.clearAllShareholders();
    await this.addShareholders(1);

    const complexTestCases = [
      {
        name: '多层嵌套运算',
        data: { data1: 10, data2: 5, data3: 2, data4: 3, data5: 4 },
        formulas: {
          'nested_formula': '((data1+data2)*data3-data4)/data5', // ((10+5)*2-3)/4 = 6.75
        }
      },
      {
        name: '混合运算',
        data: { data1: 20, data2: 4, data3: 3, data4: 2, data5: 5 },
        formulas: {
          'mixed_formula': 'data1*data2+data3-data4/data5', // 20*4+3-2/5 = 82.6
        }
      },
      {
        name: '幂运算',
        data: { data1: 2, data2: 3, data3: 4, data4: 2, data5: 1 },
        formulas: {
          'power_formula': 'data1^data2+data3*data5', // 2^3+4*1 = 12
        }
      },
      {
        name: '括号优先级',
        data: { data1: 6, data2: 3, data3: 2, data4: 4, data5: 2 },
        formulas: {
          'priority_formula': 'data1/(data2+data3)*data4-data5', // 6/(3+2)*4-2 = 2.8
        }
      }
    ];

    for (const testCase of complexTestCases) {
      console.log(`\n🧪 ${testCase.name}`);
      console.log(`📊 测试数据: ${JSON.stringify(testCase.data)}`);

      // 输入测试数据
      await this.inputTestDataToShareholder(0, testCase.data);

      // 触发计算
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(3000);

      // 获取计算结果
      const results = await this.getCalculationResults();
      console.log(`📈 找到 ${results.length} 个计算结果`);

      // 验证每个公式
      for (const [formulaName, formula] of Object.entries(testCase.formulas)) {
        const expected = this.calculateExpectedResult(formula, testCase.data);
        console.log(`   📐 ${formulaName}: ${formula} = ${expected}`);

        // 在结果中查找匹配的值
        const found = results.find(r => Math.abs(r.value - expected) < 0.01);

        if (found) {
          this.logTestResult(
            `${testCase.name}-${formulaName}`,
            true,
            `计算正确`,
            expected.toString(),
            found.value.toString()
          );
        } else {
          // 查找最接近的值
          const closest = results.reduce((prev, curr) =>
            Math.abs(curr.value - expected) < Math.abs(prev.value - expected) ? curr : prev
          );

          this.logTestResult(
            `${testCase.name}-${formulaName}`,
            false,
            `计算不匹配，最接近值: ${closest.value}`,
            expected.toString(),
            closest.value.toString()
          );
        }
      }
    }
  }

  // 执行负数专项测试
  async testNegativeNumbers() {
    console.log('\n➖ 执行负数专项测试...');

    // 准备测试环境：添加1个股东
    await this.clearAllShareholders();
    await this.addShareholders(1);

    const negativeTestCases = [
      {
        name: '单个负数输入',
        data: { data1: -100, data2: 50, data3: 25, data4: 10, data5: 5 },
        expectedResults: {
          'addition': -100 + 50, // -50
          'multiplication': -100 * 50, // -5000
          'division': -100 / 10, // -10
        }
      },
      {
        name: '全负数输入',
        data: { data1: -10, data2: -5, data3: -2, data4: -1, data5: -4 },
        expectedResults: {
          'negative_addition': -10 + (-5), // -15
          'negative_multiplication': (-10) * (-5), // 50
          'negative_division': (-10) / (-2), // 5
        }
      },
      {
        name: '正负混合',
        data: { data1: 100, data2: -50, data3: 25, data4: -10, data5: 5 },
        expectedResults: {
          'mixed_addition': 100 + (-50), // 50
          'mixed_multiplication': 100 * (-50), // -5000
          'positive_negative_div': 100 / (-10), // -10
        }
      },
      {
        name: '负数运算验证',
        data: { data1: -10, data2: -5, data3: 10, data4: -2, data5: 2 },
        expectedResults: {
          'neg_plus_neg': (-10) + (-5), // -15
          'neg_times_neg': (-10) * (-5), // 50
          'pos_times_neg': 10 * (-5), // -50
          'neg_div_neg': (-10) / (-2), // 5
        }
      }
    ];

    for (const testCase of negativeTestCases) {
      console.log(`\n🧪 ${testCase.name}`);
      console.log(`📊 测试数据: ${JSON.stringify(testCase.data)}`);

      // 输入测试数据
      await this.inputTestDataToShareholder(0, testCase.data);

      // 触发计算
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(3000);

      // 获取计算结果
      const results = await this.getCalculationResults();
      console.log(`📈 找到 ${results.length} 个计算结果`);

      // 分析负数处理
      const hasNegativeInputs = Object.values(testCase.data).some(v => v < 0);
      const hasNegativeResults = results.some(r => r.value < 0);

      console.log(`   🔍 负数分析:`);
      console.log(`      输入包含负数: ${hasNegativeInputs ? '✅是' : '❌否'}`);
      console.log(`      结果包含负数: ${hasNegativeResults ? '✅是' : '❌否'}`);

      if (hasNegativeInputs && !hasNegativeResults) {
        this.logTestResult(
          `${testCase.name}-负数处理`,
          false,
          '输入了负数但结果中没有负数，可能存在负数处理问题'
        );
      } else if (hasNegativeInputs && hasNegativeResults) {
        this.logTestResult(
          `${testCase.name}-负数处理`,
          true,
          '负数处理正常：输入负数，结果中也有负数'
        );
      }

      // 验证具体的负数运算结果
      for (const [operationName, expected] of Object.entries(testCase.expectedResults)) {
        const found = results.find(r => Math.abs(r.value - expected) < 0.01);

        if (found) {
          this.logTestResult(
            `${testCase.name}-${operationName}`,
            true,
            `负数运算正确`,
            expected.toString(),
            found.value.toString()
          );
        } else {
          this.logTestResult(
            `${testCase.name}-${operationName}`,
            false,
            `负数运算不匹配`,
            expected.toString(),
            '未找到匹配结果'
          );
        }
      }
    }
  }

  // 执行边界值测试
  async testBoundaryValues() {
    console.log('\n🔬 执行边界值测试...');

    // 准备测试环境：添加1个股东
    await this.clearAllShareholders();
    await this.addShareholders(1);

    const boundaryTestCases = [
      {
        name: '零值处理',
        data: { data1: 0, data2: 10, data3: 0, data4: 5, data5: 0 },
        expectedResults: {
          'zero_addition': 0 + 10, // 10
          'zero_multiplication': 0 * 10, // 0
          'division_by_nonzero': 0 / 5, // 0
        }
      },
      {
        name: '极小值测试',
        data: { data1: 0.01, data2: -0.01, data3: 0.001, data4: -0.001, data5: 0.1 },
        expectedResults: {
          'small_addition': 0.01 + (-0.01), // 0
          'small_multiplication': 0.01 * (-0.01), // -0.0001
          'small_division': 0.01 / 0.1, // 0.1
        }
      },
      {
        name: '极大值测试',
        data: { data1: 999999, data2: -999999, data3: 100000, data4: 50000, data5: 10000 },
        expectedResults: {
          'large_addition': 999999 + (-999999), // 0
          'large_multiplication': 100000 * 50000, // 5000000000
          'large_division': 999999 / 10000, // 99.9999
        }
      },
      {
        name: '混合边界值',
        data: { data1: 0, data2: 999999, data3: -0.01, data4: 0.01, data5: -999999 },
        expectedResults: {
          'mixed_boundary': 0 + 999999, // 999999
          'small_large_mult': (-0.01) * 999999, // -9999.99
          'boundary_division': 0.01 / (-999999), // -0.00000001
        }
      }
    ];

    for (const testCase of boundaryTestCases) {
      console.log(`\n🧪 ${testCase.name}`);
      console.log(`📊 测试数据: ${JSON.stringify(testCase.data)}`);

      // 输入测试数据
      await this.inputTestDataToShareholder(0, testCase.data);

      // 触发计算
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(3000);

      // 获取计算结果
      const results = await this.getCalculationResults();
      console.log(`📈 找到 ${results.length} 个计算结果`);

      // 验证边界值处理
      for (const [operationName, expected] of Object.entries(testCase.expectedResults)) {
        const found = results.find(r => Math.abs(r.value - expected) < 0.01);

        if (found) {
          this.logTestResult(
            `${testCase.name}-${operationName}`,
            true,
            `边界值处理正确`,
            expected.toString(),
            found.value.toString()
          );
        } else {
          // 对于极小值，使用更宽松的比较
          const foundLoose = results.find(r => Math.abs(r.value - expected) < 0.1);
          if (foundLoose) {
            this.logTestResult(
              `${testCase.name}-${operationName}`,
              true,
              `边界值处理基本正确（允许精度误差）`,
              expected.toString(),
              foundLoose.value.toString()
            );
          } else {
            this.logTestResult(
              `${testCase.name}-${operationName}`,
              false,
              `边界值处理不匹配`,
              expected.toString(),
              '未找到匹配结果'
            );
          }
        }
      }
    }
  }

  // 生成测试报告
  generateTestReport() {
    console.log('\n' + '='.repeat(80));
    console.log('🎯 股东公式映射功能全面测试报告');
    console.log('='.repeat(80));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;

    console.log(`📊 测试统计:`);
    console.log(`   测试URL: ${this.baseUrl}`);
    console.log(`   总测试项: ${totalTests}`);
    console.log(`   通过测试: ${passedTests}`);
    console.log(`   失败测试: ${failedTests}`);
    console.log(`   成功率: ${successRate}%`);

    // 按测试类型分类统计
    const testCategories = {};
    this.testResults.forEach(result => {
      const category = result.testName.split('-')[0];
      if (!testCategories[category]) {
        testCategories[category] = { total: 0, passed: 0 };
      }
      testCategories[category].total++;
      if (result.passed) {
        testCategories[category].passed++;
      }
    });

    console.log('\n📈 分类测试结果:');
    Object.entries(testCategories).forEach(([category, stats]) => {
      const categoryRate = ((stats.passed / stats.total) * 100).toFixed(1);
      console.log(`   ${category}: ${stats.passed}/${stats.total} (${categoryRate}%)`);
    });

    // 详细测试结果
    console.log('\n📝 详细测试结果:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.testName}`);
      if (result.details) {
        console.log(`   详情: ${result.details}`);
      }
      if (result.expected && result.actual) {
        console.log(`   预期: ${result.expected}, 实际: ${result.actual}`);
      }
    });

    // 失败测试汇总
    const failedResults = this.testResults.filter(r => !r.passed);
    if (failedResults.length > 0) {
      console.log('\n🔴 失败测试汇总:');
      failedResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.testName}: ${result.details}`);
      });
    }

    // 总结
    console.log('\n🎯 测试总结:');
    if (successRate >= 90) {
      console.log('🎉 股东公式映射功能优秀！');
      console.log('✅ 公式映射规则正确');
      console.log('✅ 复杂公式计算准确');
      console.log('✅ 负数处理正常');
      console.log('✅ 边界值处理良好');
    } else if (successRate >= 70) {
      console.log('⚠️ 股东公式映射功能良好，但有一些问题需要关注。');
    } else {
      console.log('❌ 股东公式映射功能需要改进。');
    }

    console.log('='.repeat(80));
  }

  // 执行完整测试
  async runCompleteTest() {
    try {
      console.log('🎯 开始股东公式映射功能全面测试...\n');

      // 1. 启动浏览器并访问页面
      await this.setup();
      await this.navigateToPage();

      // 2. 执行股东数量测试
      await this.testShareholderCount();

      // 3. 执行复杂公式计算测试
      await this.testComplexFormulaCalculations();

      // 4. 执行负数专项测试
      await this.testNegativeNumbers();

      // 5. 执行边界值测试
      await this.testBoundaryValues();

      // 6. 生成测试报告
      this.generateTestReport();

      console.log('\n🎉 所有测试完成！');
      console.log('🔍 浏览器保持打开状态，您可以手动验证结果...');
      console.log('💡 建议手动验证：');
      console.log('   1. 检查股东公式映射是否正确显示');
      console.log('   2. 验证复杂公式的计算结果');
      console.log('   3. 确认负数处理的准确性');
      console.log('   4. 测试边界值的处理情况');
      console.log('按 Ctrl+C 退出测试');

      // 保持浏览器打开
      await new Promise(() => {});

    } catch (error) {
      console.error('❌ 测试执行异常:', error);
    }
  }
}

// 执行测试
async function main() {
  const tester = new ShareholderFormulaMappingTest();
  await tester.runCompleteTest();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ShareholderFormulaMappingTest;
