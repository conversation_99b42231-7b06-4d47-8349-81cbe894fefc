

Web 应用需求文档


项目名称： 财务管理系统（代理商盈亏跟踪器）
版本： 1.8 (最终修订版 - 无功能扩展)
日期： 2025年7月25日
1. 引言
本文档概述了一个基于网络的应用程序的需求，该应用程序旨在管理财务数据，包括客户/代理商账户、每周和年度利润以及详细的交易汇总。目标是提供一个用户友好的界面，用于数据录入、查看、计算和报告。
2. 用户角色
●管理员/代理商： 可以登录，管理客户数据，输入财务信息，查看各种报告（周利润、年利润、详细汇总），并可能配置系统设置和公式。
3. 核心功能和模块
应用程序应包含以下主要模块：
3.1. 登录模块
●功能：
○用户通过“账号”和“密码”进行身份验证。
○“进去”按钮用于访问主系统。
●需求：
○安全的登录机制（例如：密码加密存储、会话管理）。
○处理不正确凭据的错误提示。
○支持键盘回车键登录。
3.2. 主仪表盘 / 导航
●功能：
○用户成功登录后，进入显示关键导航选项的仪表盘。
○日期选择： 一个可选择或显示当前操作日期的字段。
○备注列： 文本输入，用于添加简短备注（最大长度30字符）。
○报告链接：
■“公司周利润表”链接/按钮：导航到周利润报表。
■“公司年利润表”链接/按钮：导航到年利润报表。
■“导入月报表”：用于将数据导入月报表（月报表本身的功能在下面 3.5 描述）。
○操作按钮：
■“添加行”：在表格中添加新的代理商数据行。
■“保存数据”：将当前主仪表盘表格中的数据保存到后端。
●需求：
○数据录入表格中的行应可编辑（所有输入框）。
○表格数据应支持自动排序功能。
3.3. 代理商数据录入表格 (主仪表盘中的表格)
●功能：
○展示代理商的核心数据，并提供进入详细编辑页面的入口。
○“代理名/客户名/用户名”列： 显示或输入代理商名称。
○“日期编辑1”链接/按钮： 点击后导航到日期编辑界面（对应1-3期）。
○“日期编辑2”链接/按钮： 点击后导航到日期编辑界面（对应4-7期），且此周期与“日期编辑1”对应的结算周期不同。
○“周期选择”链接/按钮： 点击后，将本表格（代理商数据录入表格）中的所有数据导入到对应的周期中。
○“代理商数据录入表格”链接/按钮： 点击后导航到该特定代理商的详细汇总表（客户数据编辑表）。
○“删除代理”按钮： 用于移除对应的代理商行。
●需求：
○“代理名/客户名/用户名”列可编辑。
○“日期编辑1”和“日期编辑2”应能根据代理商数据跳转到对应的日期配置界面。
○“周期选择”功能需实现数据的正确导入和关联。
3.4. 公司周利润表
●功能：
○显示公司层面的周利润数据概览。
○日期范围显示： 显示当前报告的日期范围（例如：“7.22-7.28”），应支持动态更新或用户选择。
○数据表格：
■“代理名/下客户”列。
■“总额”列： 计算并显示总收入或总额。
■“成本”列： 计算并显示总成本。
■多期数据列： 包含多个“期”（例如：“第1期”至“第12期”），每期下方有“总额”和“成本”子列。
* “写”： 某个期可能标记为“写”，表示该期数据可编辑。
* “同左”： 大多数期标记为“同左”，表示其结构和数据（如果适用）与左侧的某个参考期相同。
■“合计”列： 汇总所有期间的“总额”和“成本”总和。
○总计行： 表格底部包含所有代理商在各期以及“合计”列中的总额和成本的总计，这些总计应显著显示。
●需求：
○表格数据应根据后端数据自动计算和汇总。
○“总额”和“成本”的计算逻辑需与汇总表中的数据和公式关联。
○日期范围应支持动态更新或用户选择。
3.5. 公司年利润表
●功能：
○显示公司层面的年利润数据概览。
○日期范围显示： 显示当前报告的日期范围（例如：“7.22-7.28”），应支持动态更新或用户选择。
○数据表格：
■“代理名/下客户”列。
■“余额”列。
■“减免”列。
■“本周利润”列。
■“合计”列：汇总余额、减免和本周利润的总和。
○总计行： 表格底部包含所有代理商在各列的总计，这些总计应显著显示。
●需求：
○表格数据应根据后端数据自动计算和汇总。
○“本周利润”的计算应与汇总表中的利润数据关联。
○日期范围应支持动态更新或可选择。
3.6. 月报表模块
●功能：
○显示公司层面的月度利润报告。
○数据将从其他模块（如汇总表或代理商数据录入表格）导入。
●需求：
○提供月度数据的汇总和展示。
○数据应自动计算和汇总。
○可能包含月份选择器。
○“导入月报表”：需实现从相关数据源将数据导入此模块的功能。
3.7. 汇总表 / 客户数据编辑表
●功能：
○显示特定代理商的详细客户数据编辑界面。
○页面标题： 动态显示当前代理商名称。
○全局公式输入（“公式填入口”）：
■在表格顶部提供可编辑的输入框，用于定义全局的计算公式。
■具体格式为：公式1.1 __________ 公式1.2 __________ 公式1.3 __________ 公式1.4 __________ 公式1.5 __________。
■这些公式将在整个汇总表的计算中使用，支持复杂的表达式，可以引用其他全局公式或具体数据列（例如 (c+d*f)*e*g）。
■重要：这些公式的填写结果不会直接显示在表格中，而是由其“计算结果”列显示运算结果。
○多条目/周期管理（“期”区块）：
■界面按编号分组（例如：1.1, 2.1...7.1），每个分组代表一个独立的日期周期或计算单元。
■日期选择器： 每个分组顶部有一个可选择日期的字段，允许用户为该周期选择日期范围。
■“(+)生成”按钮： 每个分组旁边都有一个，点击后触发该分组内的所有计算（包括百分比、计算结果和总和），并可能自动填充相关数据。
■期次定义和结构：
* 每个大周期可能包含多个“期”，每期对应一个具体的日期区间。
* “一期”通常指一个日期范围内的4个子期。
* “二期”通常指一个日期范围内的3个子期。
* 周期说明文本： 每个大周期下方有文本说明，这表明日期区间是可配置和可自定义的。
* “每一次就是一个周期”的语义强调每个日期范围为一个独立的计算上下文。
○客户数据表格（行内编辑与计算）：
■区块化布局： 表格在视觉上被分隔成不同的蓝色背景区块，每个区块包含相关的数据列。
■“客户名”列： 文本输入。
■“用户名”列： 文本输入。
* 需求： 支持字母自动排序功能。标记“(生成1.1b)”表示其值可由某种生成规则填充。
■数据列（数据1 至 数据8）：
* “数据1 (千万整数)”： 输入整数。
* 显示要求： 小数点后0位，四舍五入。
* “数据2 (十亿整数)”： 输入整数。
* 显示要求： 小数点后0位，四舍五入。
* “数据3”： 输入百分比数字，显示为百分比格式（例如：10.01%），小数点后保留2位。
* “数据4”： 输入数字，小数点后保留8位。
* “数据5”： 输入百分比数字，显示为百分比格式（例如：4.50%），小数点后保留2位。
* “数据6 (千万整数)(计算，不可改)”： 只读计算结果，初步推断计算逻辑为 数据4 - 数据5。
* 显示要求： 小数点后0位，四舍五入。
* “数据7 (十亿整数)(计算，不可改)”： 只读计算结果，具体计算逻辑待定，可能与百分比或公式相关。
* 显示要求： 小数点后0位，四舍五入。
* “数据8 (计算，不可改)”： 只读计算结果，具体计算逻辑待定。
* 分类与公式运用： 整个数据录入表格（数据1-8）有7种类型，每种类型对应不同的公式运用和显示方式，大致分为两类：1-3类和4-7类。
* “数据 (生成)”： 部分数据列（如数据1、数据2、数据6、数据7）标记为“生成”，表示其值可由系统自动填充，并可配置是否自动生成。
■公式填写列（公式1.1 至 公式1.5 等）：
* 这些列是每个客户行内部的公式字段，其名称对应顶部的全局公式。
* 需求： 这些列显示的是根据全局公式以及当前行的数据计算出的结果，而不是公式本身。
* “计算结果1.1”、“计算结果1.2”等：在这些公式字段下方，有对应的“计算结果”行，这些是根据公式计算出的实际数值，且是只读的。
* 公式位置调整： 公式3.1 需后移5格，且 1.6 和 1.7 占据两格（指表格布局调整）。
* 公式显示提示： 每种排序类型（如1.1, 1.2, 1.4, 1.5 或 1.2 单独 或 1.6, 1.7）在显示时需要有相应的提示文本。
■总和列（右侧表格中的第一列）： 汇总当前行中 公式1.1 到 公式1.5 等计算结果的总和。
* 显示要求： 小数点后面0位，四舍五入。
■四舍五入列（右侧表格中的第二列）： 显示“总和”列进行四舍五入到整数后的结果。
○右侧总计区域：
■“代理”列： 用于显示代理商名称，下方可扩展显示“下下下”等多个客户。
■“客户名”和“用户名”： 用于显示客户和用户名。
■“数量”、“价格”： 可编辑输入字段。
■“收入”、“付出”、“收付现金”、“上周余额”： 汇总统计字段。
* “自己编辑 (可改)”： 在“付出”、“上周余额”旁有可编辑字段，用于手动调整或输入额外值。
* “总计(收入)-减去(支出)”： 计算逻辑为“收入”减去“支出”。
■“减免 (自动生成第一页减免)”： 此字段从第一页（主仪表盘）的减免数据自动带入，并可能允许编辑。
■底部总和（黄色高亮）： 汇总右侧区域所有相关数字的最终总和。
* 显示要求： 小数点后0位，四舍五入。
■“公司总计 A.1”，“公司总计 A.2”： 公司级别的关键总计，可能与全局公式A.1, A.2相关联。
■底部总结区域：
* “周总 (计算)”，“月总 (计算)”，“年总 (计算)”： 只读字段，自动计算并显示所有客户数据在当前周/月/年的总和。
* “自己编辑”： 周总、月总、年总旁边可能还有可编辑的字段，用于手动调整或补充。
* “总和”： 某个计算周期的最终总和，例如 1725500。此值与第一页（主仪表盘）上周数据关联。
○页面操作按钮：
■“自动生成”：提示已执行，具体逻辑待定（例如，自动填充某些默认数据）。
■“点击生成结果，不点为空白”：触发所有计算结果的刷新。
■“点击出来汇总表”：用于生成或查看最终报告（例如PDF、Excel），或生成当前汇总的摘要。
■“添加客户行”：在当前分组内添加新的客户条目行。
■“汇总表”链接/按钮：本表格（代理商数据录入表格）中的所有数据导入到汇总表模块。
●需求：
○强大的公式解析引擎： 核心需求是实现一个能够解析和执行用户自定义复杂公式的引擎，支持：
■引用特定数据列（如数据1, 数据2）。
■引用全局公式和局部公式，并能正确将这些公式应用于表格数据以显示计算结果。
■支持嵌套公式和常见的数学运算符。
■能够精确处理不同数据类型（整数、百分比、小数点后8位数字）的输入和输出，并根据要求进行正确的四舍五入（小数点后0位）和格式化显示（百分比格式）。
■能够在前端或后端进行计算，确保性能和准确性。
○灵活的日期周期管理： 允许用户配置每个大周期下的子期数量、名称和日期范围，并能选择日期。
○数据录入与计算的实时/按需性： 可编辑字段的变更应能实时或通过触发按钮更新相关计算结果和总计。
○自动排序： “用户名”、“客户名”列需支持字母自动排序功能。
○数据自动生成与可配置性： 实现数据列的“生成”功能，并支持用户配置是否自动生成。
○“同上”逻辑： “百分比”列的“同上”显示和可编辑性。
○只读字段的自动计算： 确保所有标记为“计算，不可改”的字段以及所有计算结果和总计根据预设或用户定义的公式准确计算。
○总计和汇总的准确性： 所有总计（包括右侧区域和底部的周/月/年总计、右侧总和、四舍五入列）必须准确反映表格中详细数据的汇总结果，并区分可编辑和只读部分。特别是汇总表底部的总和，需与主仪表盘的“上周数据”关联。
○公式列显示结果而非公式： 确保汇总表中的公式列显示的是计算结果，而非公式本身。
○公式定位调整： 实现 公式3.1 后移5格，1.6 和 1.7 占据两格的布局调整。
○排序显示提示： 在每种排序类型下，应有相应的提示文本。
○用户体验： 清晰指示可编辑和只读字段，提供友好的错误消息（例如，公式语法错误）。

