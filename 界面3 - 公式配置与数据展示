公式1.1: (data1+data2+data4)+data3  公式1.2: (data1+data2+data4)+data3  公式1.31: data1+data2+data3  公式1.32: data1+data2+data4 公式1.5: data1+data2+data3+data4+data5 公式1.41: data1+data2+data3+data4 公式1.42: data1+data2+data3+data4+data5公式1.51: data1+data2+data3+data4公式1.52: data1+data2+data3+data4+data5
公式2.1: data1*data2  公式2.2: data2*data4    公式2.31: data1*data2*data3公式2.32: data1*data2*data4   公式2.5: data1*data2*data3*data4*data5公式2.41: data1*data2*data3*data4公式2.42: data1*data2*data3*data4*data5公式2.51: data1*data2*data3*data4公式2.52: data1*data2*data3*data4*data5
公式3.1: data1公式3.2: data2*data4公式3.3: 公式3.4:
公式4.1: data1/data2公式4.2: data2/data4公式4.31: data1/data2/data3公式4.32: data1/data2/data4公式4.5: data1/data2/data3/data4/data5公式4.41: data1/data2/data3/data4公式4.42: data1/data2/data3/data4/data5公式4.51: data1/data2/data3/data4公式4.52: data1/data2/data3/data4/data5
公式5.1: data1-data2公式5.2: data2-data4公式5.31: data1-data2-data3公式5.32: data1-data2-data4
公式6.1: data1^2公式6.2: data2^2公式6.3: data3^2公式6.41: data1^data2 公式6.42: data2^data3 公式6.43: data3^data4
公式7.1: data1公式7.2: data2*data4公式7.3: data3+data5


                 保存值入汇总表              汇总表  




公式1     添加客户
客户      用户        数据1      数据2     数据3      数据4    数据5    数据6     数据7     结果1   结果2      添加股东
                                                                                          0                   删除   

公式2     添加客户
客户      用户        数据1      数据2     数据3      数据4    数据5    数据6     数据7     结果1   结果2     添加股东
                                                                                                            删除   


公式3     添加客户
客户      用户        数据1      数据2     数据3      数据4         结果1       添加股东
                                                                               删除           

公式4     添加客户
客户      用户        数据1      数据2     数据3      数据4    数据5    数据6     数据7     结果1   结果2      添加股东
                                                                                                            删除   


公式5     添加客户
客户      用户        数据1      数据2     数据3      数据4    数据5    数据6     数据7     结果1   结果2         添加股东
                                                                                                                删除   


公式6     添加客户
客户      用户        数据1      数据2     数据3      数据4    数据5       结果1   结果2  结果3        
                                                                                                    删除   


公式6     添加客户
客户      用户        数据1      数据2     数据3      数据4    数据5       结果1   结果2    结果3
                                                                                                    删除   