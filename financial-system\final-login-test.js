// 最终登录测试 - 尝试多种凭据
const { chromium } = require('playwright');

async function finalLoginTest() {
  console.log('🎯 最终登录测试 - 尝试多种凭据');
  console.log('🌐 网址: http://dlsykgzq.w1.luyouxia.net\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const page = await browser.newPage();
  
  // 监听网络响应
  page.on('response', response => {
    if (response.url().includes('login') || response.url().includes('auth')) {
      console.log(`📡 ${response.status()} - ${response.url()}`);
    }
  });
  
  try {
    await page.goto('http://dlsykgzq.w1.luyouxia.net');
    await page.waitForTimeout(3000);
    
    // 尝试多种登录凭据
    const credentials = [
      { username: 'admin', password: '123456', desc: '您提供的凭据' },
      { username: 'admin', password: 'admin', desc: '常见默认密码' },
      { username: 'admin', password: '123', desc: '简单密码' },
      { username: 'test', password: '123456', desc: '测试账户' },
      { username: 'user', password: '123456', desc: '用户账户' },
      { username: '', password: '', desc: '空凭据（跳过登录）' }
    ];
    
    for (let i = 0; i < credentials.length; i++) {
      const cred = credentials[i];
      console.log(`\n🔐 尝试${i + 1}: ${cred.desc}`);
      console.log(`   用户名: "${cred.username}"`);
      console.log(`   密码: "${cred.password}"`);
      
      if (cred.username === '' && cred.password === '') {
        // 尝试跳过登录，直接访问功能页面
        console.log('🔄 尝试跳过登录...');
        const success = await tryDirectAccess(page);
        if (success) {
          console.log('✅ 无需登录即可访问！');
          break;
        }
        continue;
      }
      
      // 刷新页面确保在登录页面
      await page.goto('http://dlsykgzq.w1.luyouxia.net/login');
      await page.waitForTimeout(2000);
      
      const success = await attemptLogin(page, cred.username, cred.password);
      if (success) {
        console.log(`✅ 登录成功！使用凭据: ${cred.username}/${cred.password}`);
        await testLoggedInFeatures(page);
        break;
      } else {
        console.log(`❌ 登录失败: ${cred.desc}`);
      }
    }
    
    console.log('\n🔍 浏览器保持打开状态，请手动尝试登录...');
    console.log('💡 建议：');
    console.log('   1. 检查用户名和密码是否正确');
    console.log('   2. 查看是否有验证码要求');
    console.log('   3. 检查账户是否被锁定');
    console.log('   4. 尝试重置密码功能');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 尝试登录
async function attemptLogin(page, username, password) {
  try {
    const usernameInput = page.locator('#login_username, input[placeholder*="用户名"]').first();
    const passwordInput = page.locator('#login_password, input[type="password"]').first();
    const loginButton = page.locator('button[type="submit"], button:has-text("进 去")').first();
    
    if (await usernameInput.isVisible() && await passwordInput.isVisible() && await loginButton.isVisible()) {
      // 清空并输入凭据
      await usernameInput.clear();
      await usernameInput.fill(username);
      await page.waitForTimeout(500);
      
      await passwordInput.clear();
      await passwordInput.fill(password);
      await page.waitForTimeout(500);
      
      // 点击登录
      await loginButton.click();
      await page.waitForTimeout(3000);
      
      // 检查登录结果
      const currentUrl = page.url();
      const isLoggedIn = !currentUrl.includes('/login') && !currentUrl.includes('/login/');
      
      // 检查是否有成功跳转
      if (isLoggedIn) {
        return true;
      }
      
      // 检查是否有错误消息
      const errorElements = await page.$$('.ant-message-error, .error, text=错误, text=失败');
      if (errorElements.length > 0) {
        for (const error of errorElements) {
          const text = await error.textContent();
          console.log(`   🔴 错误消息: ${text}`);
        }
      }
      
      return false;
    }
    
    return false;
  } catch (error) {
    console.log(`   ❌ 登录尝试异常: ${error.message}`);
    return false;
  }
}

// 尝试直接访问功能页面
async function tryDirectAccess(page) {
  const testUrls = [
    'http://dlsykgzq.w1.luyouxia.net/dashboard',
    'http://dlsykgzq.w1.luyouxia.net/formula-config',
    'http://dlsykgzq.w1.luyouxia.net/'
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`   🔗 尝试访问: ${url}`);
      await page.goto(url, { timeout: 10000 });
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      const isAccessible = !currentUrl.includes('/login');
      
      if (isAccessible) {
        console.log(`   ✅ 可直接访问: ${url}`);
        
        // 测试页面功能
        const hasButtons = await page.locator('button').count() > 0;
        const hasInputs = await page.locator('input').count() > 0;
        
        if (hasButtons && hasInputs) {
          console.log('   🎉 页面功能完整，无需登录！');
          await testPageFeatures(page);
          return true;
        }
      } else {
        console.log(`   ❌ 需要登录: ${url}`);
      }
    } catch (error) {
      console.log(`   ❌ 访问失败: ${error.message}`);
    }
  }
  
  return false;
}

// 测试登录后的功能
async function testLoggedInFeatures(page) {
  console.log('\n⚙️ 测试登录后功能...');
  
  try {
    // 测试仪表盘
    await page.goto('http://dlsykgzq.w1.luyouxia.net/dashboard');
    await page.waitForTimeout(3000);
    console.log(`✅ 仪表盘访问成功: ${page.url()}`);
    
    await testPageFeatures(page);
    
    // 测试公式配置
    await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config');
    await page.waitForTimeout(3000);
    console.log(`✅ 公式配置访问成功: ${page.url()}`);
    
  } catch (error) {
    console.log(`❌ 登录后功能测试异常: ${error.message}`);
  }
}

// 测试页面功能
async function testPageFeatures(page) {
  console.log('   📊 测试页面功能...');
  
  try {
    // 查找添加按钮
    const addButtons = await page.$$('button:has-text("添加"), button:has-text("新增")');
    console.log(`   📊 找到 ${addButtons.length} 个添加按钮`);
    
    if (addButtons.length > 0) {
      await addButtons[0].click();
      await page.waitForTimeout(2000);
      
      // 测试数据输入
      const inputs = await page.$$('input[type="text"], input[type="number"], table input');
      console.log(`   📝 找到 ${inputs.length} 个输入框`);
      
      if (inputs.length >= 2) {
        // 测试正数输入
        await inputs[0].fill('100');
        await page.waitForTimeout(500);
        const value1 = await inputs[0].inputValue();
        console.log(`   ${value1 === '100' ? '✅' : '❌'} 正数输入: ${value1}`);
        
        // 测试负数输入
        await inputs[1].fill('-50');
        await page.waitForTimeout(500);
        const value2 = await inputs[1].inputValue();
        console.log(`   ${value2 === '-50' ? '✅' : '❌'} 负数输入: ${value2}`);
        
        // 检查计算结果
        await page.keyboard.press('Tab');
        await page.waitForTimeout(2000);
        
        const results = await page.$$('strong');
        if (results.length > 0) {
          console.log(`   📈 找到 ${results.length} 个计算结果`);
          
          for (let i = 0; i < Math.min(3, results.length); i++) {
            const text = await results[i].textContent();
            const num = parseFloat(text);
            if (!isNaN(num)) {
              console.log(`   📊 结果${i + 1}: ${num}`);
            }
          }
        }
      }
    }
    
  } catch (error) {
    console.log(`   ❌ 页面功能测试异常: ${error.message}`);
  }
}

// 执行测试
finalLoginTest().catch(console.error);
