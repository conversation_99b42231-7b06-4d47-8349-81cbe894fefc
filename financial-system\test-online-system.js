// 在线财务管理系统功能测试脚本
const { chromium } = require('playwright');

class OnlineSystemTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://dlsykgzq.w1.luyouxia.net';
    this.testResults = [];
  }

  // 启动浏览器
  async setup() {
    console.log('🚀 启动在线系统测试...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 2000,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    this.page = await context.newPage();
    
    // 监听页面错误
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🔴 页面错误: ${msg.text()}`);
      }
    });
    
    this.page.on('pageerror', error => {
      console.log(`🔴 页面异常: ${error.message}`);
    });
  }

  // 记录测试结果
  logResult(testName, passed, details = '') {
    const result = { testName, passed, details, timestamp: new Date().toISOString() };
    this.testResults.push(result);
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
  }

  // 测试网站可访问性
  async testWebsiteAccessibility() {
    console.log('\n🌐 测试网站可访问性...');
    
    try {
      const response = await this.page.goto(this.baseUrl, { 
        waitUntil: 'networkidle',
        timeout: 30000 
      });
      
      const status = response.status();
      const url = this.page.url();
      
      if (status === 200) {
        this.logResult('网站访问', true, `状态码: ${status}, URL: ${url}`);
      } else {
        this.logResult('网站访问', false, `状态码: ${status}, 可能有重定向或错误`);
      }
      
      // 检查页面标题
      const title = await this.page.title();
      this.logResult('页面标题', title.length > 0, `标题: "${title}"`);
      
      // 等待页面完全加载
      await this.page.waitForTimeout(5000);
      
      return status === 200;
    } catch (error) {
      this.logResult('网站访问', false, `访问失败: ${error.message}`);
      return false;
    }
  }

  // 测试页面基本元素
  async testPageElements() {
    console.log('\n🔍 测试页面基本元素...');
    
    try {
      // 检查是否有登录页面
      const hasLoginForm = await this.page.locator('input[type="password"], input[placeholder*="密码"]').isVisible({ timeout: 3000 });
      if (hasLoginForm) {
        this.logResult('登录页面检测', true, '检测到登录表单');
        await this.testLoginFunctionality();
        return;
      }
      
      // 检查主要页面元素
      const elements = [
        { selector: 'text=财务管理', name: '财务管理标题' },
        { selector: 'text=公式配置', name: '公式配置链接' },
        { selector: 'text=仪表盘', name: '仪表盘链接' },
        { selector: 'button', name: '按钮元素' },
        { selector: 'table', name: '表格元素' },
        { selector: 'input', name: '输入框元素' }
      ];
      
      for (const element of elements) {
        try {
          const isVisible = await this.page.locator(element.selector).first().isVisible({ timeout: 2000 });
          this.logResult(element.name, isVisible, isVisible ? '元素存在且可见' : '元素不存在或不可见');
        } catch (error) {
          this.logResult(element.name, false, `检测失败: ${error.message}`);
        }
      }
      
    } catch (error) {
      this.logResult('页面元素检测', false, `检测异常: ${error.message}`);
    }
  }

  // 测试登录功能
  async testLoginFunctionality() {
    console.log('\n🔐 测试登录功能...');
    
    try {
      // 查找用户名和密码输入框
      const usernameInput = this.page.locator('input[type="text"], input[placeholder*="用户名"], input[placeholder*="账号"]').first();
      const passwordInput = this.page.locator('input[type="password"], input[placeholder*="密码"]').first();
      const loginButton = this.page.locator('button[type="submit"], button:has-text("登录"), button:has-text("登入")').first();
      
      if (await usernameInput.isVisible() && await passwordInput.isVisible() && await loginButton.isVisible()) {
        this.logResult('登录表单元素', true, '用户名、密码输入框和登录按钮都存在');
        
        // 尝试登录
        await usernameInput.fill('admin');
        await passwordInput.fill('123456');
        await loginButton.click();
        
        // 等待登录结果
        await this.page.waitForTimeout(3000);
        
        // 检查是否登录成功（URL变化或页面内容变化）
        const currentUrl = this.page.url();
        const hasLoggedIn = !currentUrl.includes('login') || await this.page.locator('text=退出, text=登出, text=注销').isVisible({ timeout: 2000 });
        
        this.logResult('登录功能', hasLoggedIn, hasLoggedIn ? '登录成功' : '登录失败或需要其他凭据');
        
        if (hasLoggedIn) {
          await this.testMainFunctionality();
        }
      } else {
        this.logResult('登录表单元素', false, '登录表单元素不完整');
      }
      
    } catch (error) {
      this.logResult('登录功能测试', false, `登录测试异常: ${error.message}`);
    }
  }

  // 测试主要功能
  async testMainFunctionality() {
    console.log('\n⚙️ 测试主要功能...');
    
    try {
      // 测试导航功能
      await this.testNavigation();
      
      // 测试公式配置功能
      await this.testFormulaConfiguration();
      
      // 测试数据输入功能
      await this.testDataInput();
      
    } catch (error) {
      this.logResult('主要功能测试', false, `功能测试异常: ${error.message}`);
    }
  }

  // 测试导航功能
  async testNavigation() {
    console.log('\n🧭 测试导航功能...');
    
    const navigationLinks = [
      { text: '仪表盘', name: '仪表盘导航' },
      { text: '公式配置', name: '公式配置导航' },
      { text: '汇总表', name: '汇总表导航' },
      { text: '利润表', name: '利润表导航' }
    ];
    
    for (const link of navigationLinks) {
      try {
        const linkElement = this.page.locator(`text=${link.text}`).first();
        if (await linkElement.isVisible({ timeout: 2000 })) {
          await linkElement.click();
          await this.page.waitForTimeout(2000);
          
          // 检查页面是否发生变化
          const currentUrl = this.page.url();
          this.logResult(link.name, true, `导航成功，当前URL: ${currentUrl}`);
        } else {
          this.logResult(link.name, false, '导航链接不存在');
        }
      } catch (error) {
        this.logResult(link.name, false, `导航测试失败: ${error.message}`);
      }
    }
  }

  // 测试公式配置功能
  async testFormulaConfiguration() {
    console.log('\n📊 测试公式配置功能...');
    
    try {
      // 尝试访问公式配置页面
      await this.page.goto(`${this.baseUrl}/formula-config`);
      await this.page.waitForTimeout(3000);
      
      // 检查公式配置页面元素
      const hasFormulaGrid = await this.page.locator('text=公式配置, .formula-card, text=公式').isVisible({ timeout: 3000 });
      this.logResult('公式配置页面', hasFormulaGrid, hasFormulaGrid ? '公式配置页面加载成功' : '公式配置页面未找到');
      
      if (hasFormulaGrid) {
        // 测试公式编辑功能
        const formulaCards = await this.page.$$('.formula-card, [class*="formula"], button:has-text("公式")');
        if (formulaCards.length > 0) {
          await formulaCards[0].click();
          await this.page.waitForTimeout(1000);
          
          const hasModal = await this.page.locator('.ant-modal, [role="dialog"], textarea').isVisible({ timeout: 2000 });
          this.logResult('公式编辑功能', hasModal, hasModal ? '公式编辑模态框打开成功' : '公式编辑功能未响应');
          
          if (hasModal) {
            // 关闭模态框
            const closeButton = this.page.locator('button:has-text("取消"), button:has-text("关闭"), .ant-modal-close').first();
            if (await closeButton.isVisible()) {
              await closeButton.click();
            }
          }
        }
      }
      
    } catch (error) {
      this.logResult('公式配置功能', false, `公式配置测试异常: ${error.message}`);
    }
  }

  // 测试数据输入功能
  async testDataInput() {
    console.log('\n📝 测试数据输入功能...');
    
    try {
      // 查找添加客户按钮
      const addButton = this.page.locator('button:has-text("添加客户"), button:has-text("添加"), button:has-text("新增")').first();
      if (await addButton.isVisible({ timeout: 3000 })) {
        await addButton.click();
        await this.page.waitForTimeout(2000);
        this.logResult('添加客户功能', true, '添加客户按钮点击成功');
        
        // 测试数据输入
        const inputs = await this.page.$$('table input, input[type="text"], input[type="number"]');
        if (inputs.length > 0) {
          // 尝试在第一个输入框中输入测试数据
          await inputs[0].fill('100');
          await this.page.waitForTimeout(500);
          
          const inputValue = await inputs[0].inputValue();
          this.logResult('数据输入功能', inputValue === '100', `输入测试: 期望100, 实际${inputValue}`);
          
          // 测试负数输入
          if (inputs.length > 1) {
            await inputs[1].fill('-50');
            await this.page.waitForTimeout(500);
            
            const negativeValue = await inputs[1].inputValue();
            this.logResult('负数输入功能', negativeValue === '-50', `负数测试: 期望-50, 实际${negativeValue}`);
          }
        } else {
          this.logResult('数据输入功能', false, '未找到输入框');
        }
      } else {
        this.logResult('添加客户功能', false, '未找到添加客户按钮');
      }
      
    } catch (error) {
      this.logResult('数据输入功能', false, `数据输入测试异常: ${error.message}`);
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📋 生成在线系统测试报告...');
    console.log('='.repeat(60));
    console.log('🌐 在线财务管理系统功能测试报告');
    console.log('='.repeat(60));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
    
    console.log(`📊 测试统计:`);
    console.log(`   网址: ${this.baseUrl}`);
    console.log(`   总测试项: ${totalTests}`);
    console.log(`   通过测试: ${passedTests}`);
    console.log(`   失败测试: ${failedTests}`);
    console.log(`   成功率: ${successRate}%`);
    
    console.log('\n📈 详细测试结果:');
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.testName}`);
      if (result.details) {
        console.log(`   详情: ${result.details}`);
      }
    });
    
    // 问题汇总
    const failedResults = this.testResults.filter(r => !r.passed);
    if (failedResults.length > 0) {
      console.log('\n🔴 发现的问题:');
      failedResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.testName}: ${result.details}`);
      });
    }
    
    // 总结
    console.log('\n🎯 测试总结:');
    if (successRate >= 80) {
      console.log('🎉 系统功能良好，大部分功能正常运行！');
    } else if (successRate >= 60) {
      console.log('⚠️ 系统基本可用，但有一些问题需要关注。');
    } else {
      console.log('❌ 系统存在较多问题，需要进一步检查。');
    }
    
    console.log('='.repeat(60));
  }

  // 执行完整测试
  async runCompleteTest() {
    try {
      await this.setup();
      
      const isAccessible = await this.testWebsiteAccessibility();
      if (isAccessible) {
        await this.testPageElements();
      }
      
      this.generateReport();
      
      console.log('\n🎉 测试完成！');
      console.log('🔍 浏览器保持打开状态，您可以手动检查系统功能...');
      console.log('按 Ctrl+C 退出测试');
      
      // 保持浏览器打开
      await new Promise(() => {});
      
    } catch (error) {
      console.error('❌ 测试执行异常:', error);
    }
  }
}

// 执行测试
async function main() {
  const tester = new OnlineSystemTester();
  await tester.runCompleteTest();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = OnlineSystemTester;
