// 自动化登录测试 - 使用admin/123456
const { chromium } = require('playwright');

async function automatedTest() {
  console.log('🚀 启动自动化测试 - 使用登录凭据');
  console.log('🌐 网址: http://dlsykgzq.w1.luyouxia.net');
  console.log('👤 用户名: admin');
  console.log('🔑 密码: 123456\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 访问网站
    console.log('📊 步骤1: 访问网站...');
    await page.goto('http://dlsykgzq.w1.luyouxia.net');
    await page.waitForTimeout(3000);
    
    const title = await page.title();
    console.log(`✅ 页面标题: "${title}"`);
    
    // 2. 检查是否需要登录
    console.log('\n📊 步骤2: 检查登录状态...');
    const hasLoginForm = await page.locator('input[type="password"], input[placeholder*="密码"]').isVisible({ timeout: 3000 });
    
    if (hasLoginForm) {
      console.log('🔐 检测到登录表单，开始登录...');
      
      // 查找用户名输入框
      const usernameSelectors = [
        'input[placeholder*="用户名"]',
        'input[placeholder*="账号"]', 
        'input[name="username"]',
        'input[id="username"]',
        'input[type="text"]'
      ];
      
      let usernameInput = null;
      for (const selector of usernameSelectors) {
        try {
          const input = page.locator(selector).first();
          if (await input.isVisible({ timeout: 1000 })) {
            usernameInput = input;
            console.log(`✅ 找到用户名输入框: ${selector}`);
            break;
          }
        } catch (e) {}
      }
      
      // 查找密码输入框
      const passwordInput = page.locator('input[type="password"]').first();
      
      // 查找登录按钮
      const loginButtonSelectors = [
        'button[type="submit"]',
        'button:has-text("登录")',
        'button:has-text("登入")',
        'button:has-text("确定")'
      ];
      
      let loginButton = null;
      for (const selector of loginButtonSelectors) {
        try {
          const button = page.locator(selector).first();
          if (await button.isVisible({ timeout: 1000 })) {
            loginButton = button;
            console.log(`✅ 找到登录按钮: ${selector}`);
            break;
          }
        } catch (e) {}
      }
      
      if (usernameInput && await passwordInput.isVisible() && loginButton) {
        // 执行登录
        console.log('📝 输入登录凭据...');
        await usernameInput.fill('admin');
        await passwordInput.fill('123456');
        await page.waitForTimeout(1000);
        
        console.log('🔘 点击登录按钮...');
        await loginButton.click();
        await page.waitForTimeout(5000);
        
        // 检查登录结果
        const currentUrl = page.url();
        const hasLoggedIn = !currentUrl.includes('login') || 
                           await page.locator('text=退出, text=登出, text=注销, text=欢迎').isVisible({ timeout: 3000 });
        
        console.log(`${hasLoggedIn ? '✅' : '❌'} 登录${hasLoggedIn ? '成功' : '失败'}`);
        console.log(`📍 当前URL: ${currentUrl}`);
        
        if (!hasLoggedIn) {
          console.log('❌ 登录失败，可能凭据不正确或需要其他验证');
          return;
        }
      } else {
        console.log('❌ 登录表单元素不完整');
        return;
      }
    } else {
      console.log('✅ 无需登录或已登录状态');
    }
    
    // 3. 测试主要功能
    console.log('\n📊 步骤3: 测试主要功能...');
    
    // 测试导航
    const navLinks = [
      { text: '仪表盘', url: '/dashboard' },
      { text: '公式配置', url: '/formula-config' },
      { text: '汇总表', url: '/summary-layout' }
    ];
    
    for (const nav of navLinks) {
      try {
        console.log(`\n🧭 测试${nav.text}导航...`);
        
        // 尝试直接访问URL
        const fullUrl = `http://dlsykgzq.w1.luyouxia.net${nav.url}`;
        await page.goto(fullUrl);
        await page.waitForTimeout(3000);
        
        const currentUrl = page.url();
        const pageLoaded = !currentUrl.includes('error') && !currentUrl.includes('404');
        
        console.log(`${pageLoaded ? '✅' : '❌'} ${nav.text}页面: ${pageLoaded ? '加载成功' : '加载失败'}`);
        console.log(`📍 URL: ${currentUrl}`);
        
        if (pageLoaded && nav.text === '公式配置') {
          await testFormulaPage(page);
        }
        
        if (pageLoaded && nav.text === '仪表盘') {
          await testDashboardPage(page);
        }
        
      } catch (error) {
        console.log(`❌ ${nav.text}导航测试失败: ${error.message}`);
      }
    }
    
    console.log('\n🎉 自动化测试完成！');
    console.log('🔍 浏览器保持打开状态，您可以手动检查...');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 测试公式配置页面
async function testFormulaPage(page) {
  console.log('📐 测试公式配置页面功能...');
  
  try {
    // 检查公式相关元素
    const hasFormulaElements = await page.locator('text=公式, .formula-card, text=配置').isVisible({ timeout: 3000 });
    console.log(`   ${hasFormulaElements ? '✅' : '❌'} 公式元素: ${hasFormulaElements ? '存在' : '不存在'}`);
    
    // 查找公式卡片
    const formulaCards = await page.$$('.formula-card, [class*="formula"], button:has-text("公式")');
    console.log(`   📊 找到 ${formulaCards.length} 个公式相关元素`);
    
    if (formulaCards.length > 0) {
      // 测试公式编辑
      await formulaCards[0].click();
      await page.waitForTimeout(2000);
      
      const hasModal = await page.locator('.ant-modal, [role="dialog"], textarea').isVisible({ timeout: 3000 });
      console.log(`   ${hasModal ? '✅' : '❌'} 公式编辑: ${hasModal ? '正常' : '无响应'}`);
      
      if (hasModal) {
        // 获取公式表达式
        const textarea = page.locator('textarea').first();
        if (await textarea.isVisible()) {
          const expression = await textarea.inputValue();
          console.log(`   📐 公式表达式: "${expression}"`);
        }
        
        // 关闭模态框
        const closeBtn = page.locator('button:has-text("取消"), .ant-modal-close').first();
        if (await closeBtn.isVisible()) {
          await closeBtn.click();
        }
      }
    }
    
    // 测试数据输入
    await testDataInput(page);
    
  } catch (error) {
    console.log(`   ❌ 公式页面测试异常: ${error.message}`);
  }
}

// 测试仪表盘页面
async function testDashboardPage(page) {
  console.log('📊 测试仪表盘页面功能...');
  
  try {
    // 查找添加客户按钮
    const addButtons = await page.$$('button:has-text("添加客户"), button:has-text("添加"), button:has-text("新增")');
    console.log(`   📊 找到 ${addButtons.length} 个添加按钮`);
    
    if (addButtons.length > 0) {
      await addButtons[0].click();
      await page.waitForTimeout(2000);
      console.log(`   ✅ 添加客户按钮点击成功`);
      
      await testDataInput(page);
    }
    
  } catch (error) {
    console.log(`   ❌ 仪表盘测试异常: ${error.message}`);
  }
}

// 测试数据输入功能
async function testDataInput(page) {
  console.log('   📝 测试数据输入功能...');
  
  try {
    const inputs = await page.$$('input[type="text"], input[type="number"], table input');
    console.log(`   📊 找到 ${inputs.length} 个输入框`);
    
    if (inputs.length >= 4) {
      // 输入测试数据: data1=100, data2=2, data3=50, data4=1
      const testData = [100, 2, 50, 1];
      
      for (let i = 0; i < Math.min(4, inputs.length); i++) {
        await inputs[i].fill(testData[i].toString());
        await page.waitForTimeout(300);
        
        const actualValue = await inputs[i].inputValue();
        console.log(`   ${actualValue === testData[i].toString() ? '✅' : '❌'} 输入data${i+1}: 期望${testData[i]}, 实际${actualValue}`);
      }
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(2000);
      
      // 检查计算结果
      const results = await page.$$('strong, .result, [class*="result"]');
      console.log(`   📊 找到 ${results.length} 个结果元素`);
      
      if (results.length > 0) {
        console.log('   📈 计算结果:');
        for (let i = 0; i < Math.min(5, results.length); i++) {
          try {
            const text = await results[i].textContent();
            const num = parseFloat(text);
            if (!isNaN(num)) {
              console.log(`      结果${i+1}: ${num}`);
              
              // 验证公式1.1: (100+2+1)+50 = 153
              if (num === 153) {
                console.log(`      ✅ 公式1.1验证成功: ${num}`);
              }
            }
          } catch (e) {}
        }
      }
      
      // 测试负数输入
      console.log('   ➖ 测试负数输入...');
      if (inputs.length >= 2) {
        await inputs[0].fill('-100');
        await inputs[1].fill('-2');
        await page.waitForTimeout(1000);
        
        const negValue1 = await inputs[0].inputValue();
        const negValue2 = await inputs[1].inputValue();
        
        console.log(`   ${negValue1 === '-100' ? '✅' : '❌'} 负数输入1: 期望-100, 实际${negValue1}`);
        console.log(`   ${negValue2 === '-2' ? '✅' : '❌'} 负数输入2: 期望-2, 实际${negValue2}`);
      }
    }
    
  } catch (error) {
    console.log(`   ❌ 数据输入测试异常: ${error.message}`);
  }
}

// 执行测试
automatedTest().catch(console.error);
