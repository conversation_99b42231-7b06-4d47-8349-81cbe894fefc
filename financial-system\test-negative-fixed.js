// 修复版本 - 专门测试负数和公式准确性
const { chromium } = require('playwright');

async function testNegativeFixed() {
  console.log('🎯 修复版 - 专门测试负数和公式准确性');
  console.log('🌐 URL: http://dlsykgzq.w1.luyouxia.net/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 访问页面
    console.log('📊 步骤1: 访问公式配置页面...');
    await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config/', {
      timeout: 0
    });
    
    await page.waitForTimeout(5000);
    console.log('✅ 页面加载完成');
    
    // 2. 添加客户
    console.log('\n📊 步骤2: 添加客户...');
    const addButton = page.locator('button:has-text("添加客户")').first();
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(3000);
      console.log('✅ 添加客户成功');
    }
    
    // 3. 执行测试用例
    const testCases = [
      {
        name: '基础正数测试',
        data: [100, 2, 50, 1],
        expected: {
          formula1_1: 153, // (100+2+1)+50 = 153
          formula2_1: 200, // 100*2 = 200
          formula3_1: 100  // 100
        }
      },
      {
        name: '基础负数测试',
        data: [-100, -2, -50, -1],
        expected: {
          formula1_1: -153, // (-100+-2+-1)+(-50) = -153
          formula2_1: 200,  // (-100)*(-2) = 200
          formula3_1: -100  // -100
        }
      },
      {
        name: '正负混合测试',
        data: [100, -2, 50, -1],
        expected: {
          formula1_1: 147, // (100+-2+-1)+50 = 147
          formula2_1: -200, // 100*(-2) = -200
          formula3_1: 100   // 100
        }
      },
      {
        name: '零值测试',
        data: [0, -5, 0, 2],
        expected: {
          formula1_1: -3,  // (0+-5+2)+0 = -3
          formula2_1: 0,   // 0*(-5) = 0
          formula3_1: 0    // 0
        }
      }
    ];
    
    // 执行每个测试用例
    for (const testCase of testCases) {
      await executeTestCaseFixed(page, testCase);
      await page.waitForTimeout(3000);
    }
    
    console.log('\n🎉 所有测试完成！');
    console.log('🔍 浏览器保持打开状态，请手动验证结果...');
    console.log('💡 请特别关注：');
    console.log('   1. 负数输入是否被正确接受');
    console.log('   2. 负数计算结果是否准确');
    console.log('   3. 正负数混合运算是否正确');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 执行单个测试用例（修复版）
async function executeTestCaseFixed(page, testCase) {
  console.log(`\n🧪 执行测试: ${testCase.name}`);
  console.log(`📊 测试数据: data1=${testCase.data[0]}, data2=${testCase.data[1]}, data3=${testCase.data[2]}, data4=${testCase.data[3]}`);
  
  try {
    // 使用更稳定的输入方法
    await inputDataSafely(page, testCase.data);
    
    // 触发计算
    console.log('   ⚡ 触发计算...');
    await page.keyboard.press('Tab');
    await page.waitForTimeout(3000);
    
    // 获取计算结果
    const results = await getCalculationResults(page);
    console.log(`   📊 找到 ${results.length} 个计算结果`);
    
    // 验证结果
    await verifyResults(results, testCase);
    
  } catch (error) {
    console.log(`   ❌ ${testCase.name}执行异常: ${error.message}`);
  }
}

// 安全输入数据
async function inputDataSafely(page, data) {
  console.log('   📝 输入测试数据...');
  
  try {
    // 方法1: 通过表格行定位输入框
    const firstRow = page.locator('table tbody tr').first();
    
    for (let i = 0; i < 4; i++) {
      const value = data[i];
      if (value !== undefined) {
        // 尝试多种选择器
        const selectors = [
          `table tbody tr:first-child td:nth-child(${i + 3}) input`, // 假设前2列是其他字段
          `table tbody tr:first-child input:nth-of-type(${i + 1})`,
          `input:nth-of-type(${i + 3})` // 跳过前面的非数据输入框
        ];
        
        let inputSuccess = false;
        for (const selector of selectors) {
          try {
            const input = page.locator(selector).first();
            if (await input.isVisible({ timeout: 1000 })) {
              // 使用三次点击选中所有内容，然后输入
              await input.click({ clickCount: 3 });
              await input.fill(value.toString());
              await page.waitForTimeout(300);
              
              // 验证输入
              const actualValue = await input.inputValue();
              if (actualValue === value.toString()) {
                console.log(`      ✅ data${i+1}: ${value} (使用${selector})`);
                inputSuccess = true;
                break;
              }
            }
          } catch (e) {
            // 继续尝试下一个选择器
          }
        }
        
        if (!inputSuccess) {
          console.log(`      ❌ data${i+1}: ${value} (输入失败)`);
        }
      }
    }
    
    // 方法2: 如果上面失败，使用JavaScript直接设置
    if (!await verifyInputSuccess(page, data)) {
      console.log('   🔄 使用JavaScript方法输入数据...');
      await page.evaluate((testData) => {
        const inputs = document.querySelectorAll('table input');
        for (let i = 0; i < Math.min(4, inputs.length); i++) {
          if (testData[i] !== undefined && inputs[i]) {
            inputs[i].value = testData[i].toString();
            inputs[i].dispatchEvent(new Event('input', { bubbles: true }));
            inputs[i].dispatchEvent(new Event('change', { bubbles: true }));
          }
        }
      }, data);
    }
    
  } catch (error) {
    console.log(`   ❌ 数据输入异常: ${error.message}`);
  }
}

// 验证输入是否成功
async function verifyInputSuccess(page, expectedData) {
  try {
    const inputs = await page.$$('table input');
    let successCount = 0;
    
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      if (expectedData[i] !== undefined) {
        const actualValue = await inputs[i].inputValue();
        if (actualValue === expectedData[i].toString()) {
          successCount++;
        }
      }
    }
    
    return successCount >= 2; // 至少成功输入2个字段
  } catch (error) {
    return false;
  }
}

// 获取计算结果
async function getCalculationResults(page) {
  try {
    const results = await page.evaluate(() => {
      const resultData = [];
      const strongElements = document.querySelectorAll('strong');
      
      strongElements.forEach((element, index) => {
        const text = element.textContent.trim();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          resultData.push({ index, value: num });
        }
      });
      
      return resultData;
    });
    
    return results;
  } catch (error) {
    console.log(`   ❌ 获取结果异常: ${error.message}`);
    return [];
  }
}

// 验证计算结果
async function verifyResults(results, testCase) {
  console.log('   🔍 验证计算结果:');
  
  if (results.length === 0) {
    console.log('      ❌ 未找到任何计算结果');
    return;
  }
  
  // 显示所有结果
  console.log('   📈 页面显示的结果:');
  results.forEach(result => {
    console.log(`      位置${result.index}: ${result.value}`);
  });
  
  // 验证预期结果
  let correctCount = 0;
  let totalCount = 0;
  
  // 验证公式1.1
  if (testCase.expected.formula1_1 !== undefined) {
    totalCount++;
    const expected = testCase.expected.formula1_1;
    const found = results.find(r => Math.abs(r.value - expected) < 0.01);
    
    if (found) {
      console.log(`      ✅ 公式1.1: 预期${expected}, 找到${found.value} (位置${found.index})`);
      correctCount++;
    } else {
      console.log(`      ❌ 公式1.1: 预期${expected}, 未找到匹配结果`);
      
      // 查找最接近的值
      const closest = results.reduce((prev, curr) => 
        Math.abs(curr.value - expected) < Math.abs(prev.value - expected) ? curr : prev
      );
      console.log(`         最接近的值: ${closest.value} (差值: ${Math.abs(closest.value - expected).toFixed(2)})`);
    }
  }
  
  // 验证公式2.1
  if (testCase.expected.formula2_1 !== undefined) {
    totalCount++;
    const expected = testCase.expected.formula2_1;
    const found = results.find(r => Math.abs(r.value - expected) < 0.01);
    
    if (found) {
      console.log(`      ✅ 公式2.1: 预期${expected}, 找到${found.value} (位置${found.index})`);
      correctCount++;
    } else {
      console.log(`      ❌ 公式2.1: 预期${expected}, 未找到匹配结果`);
    }
  }
  
  // 验证公式3.1
  if (testCase.expected.formula3_1 !== undefined) {
    totalCount++;
    const expected = testCase.expected.formula3_1;
    const found = results.find(r => Math.abs(r.value - expected) < 0.01);
    
    if (found) {
      console.log(`      ✅ 公式3.1: 预期${expected}, 找到${found.value} (位置${found.index})`);
      correctCount++;
    } else {
      console.log(`      ❌ 公式3.1: 预期${expected}, 未找到匹配结果`);
    }
  }
  
  const successRate = totalCount > 0 ? (correctCount / totalCount * 100).toFixed(1) : 0;
  console.log(`   📊 ${testCase.name}验证结果: ${correctCount}/${totalCount} 正确 (${successRate}%)`);
  
  // 特别分析负数处理
  if (testCase.name.includes('负数') || testCase.data.some(v => v < 0)) {
    const hasNegativeInputs = testCase.data.some(v => v < 0);
    const hasNegativeResults = results.some(r => r.value < 0);
    
    console.log('   🔍 负数处理分析:');
    console.log(`      输入包含负数: ${hasNegativeInputs ? '✅是' : '❌否'}`);
    console.log(`      结果包含负数: ${hasNegativeResults ? '✅是' : '❌否'}`);
    
    if (hasNegativeInputs && !hasNegativeResults) {
      console.log('      ⚠️ 警告: 输入了负数但结果中没有负数，可能存在负数处理问题');
    } else if (hasNegativeInputs && hasNegativeResults) {
      console.log('      ✅ 负数处理正常: 输入负数，结果中也有负数');
    }
  }
}

// 执行测试
testNegativeFixed().catch(console.error);
