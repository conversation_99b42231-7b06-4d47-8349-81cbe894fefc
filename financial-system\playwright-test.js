// Playwright自动化测试 - 验证财务管理系统修复效果
const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3000/formula-config/';

test.describe('财务管理系统修复验证', () => {
  
  test.beforeEach(async ({ page }) => {
    // 访问系统
    await page.goto(BASE_URL, { 
      timeout: 60000,
      waitUntil: 'networkidle' 
    });
    await page.waitForTimeout(3000);
  });

  test('1. 系统基本功能测试', async ({ page }) => {
    console.log('🧪 测试1: 系统基本功能');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/.*/, { timeout: 10000 });
    console.log('   ✅ 页面标题正常');
    
    // 验证关键元素存在
    const addButton = page.locator('button:has-text("添加客户")').first();
    await expect(addButton).toBeVisible({ timeout: 10000 });
    console.log('   ✅ 添加客户按钮存在');
    
    const table = page.locator('table').first();
    await expect(table).toBeVisible({ timeout: 10000 });
    console.log('   ✅ 数据表格存在');
    
    // 测试添加客户功能
    await addButton.click();
    await page.waitForTimeout(2000);
    
    const tables = await page.$$('table');
    expect(tables.length).toBeGreaterThan(0);
    console.log(`   ✅ 添加客户成功，表格数量: ${tables.length}`);
  });

  test('2. 负数处理修复验证', async ({ page }) => {
    console.log('🧪 测试2: 负数处理修复验证');
    
    // 添加客户
    await page.locator('button:has-text("添加客户")').first().click();
    await page.waitForTimeout(2000);
    
    // 获取输入框
    const inputs = await page.$$('input');
    expect(inputs.length).toBeGreaterThan(0);
    console.log(`   📊 找到输入框: ${inputs.length}个`);
    
    // 输入负数测试数据
    const negativeData = [-100, -50, -25, -10];
    console.log('   📝 输入负数测试数据: -100, -50, -25, -10');
    
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      await inputs[i].fill(negativeData[i].toString());
      await page.waitForTimeout(300);
      
      // 验证负数输入被接受
      const value = await inputs[i].inputValue();
      expect(value).toBe(negativeData[i].toString());
      console.log(`   ✅ 输入框${i+1}: ${negativeData[i]} 输入成功`);
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    await page.waitForTimeout(4000);
    
    // 获取计算结果
    const results = await page.evaluate(() => {
      const nums = [];
      document.querySelectorAll('strong').forEach(el => {
        const text = el.textContent.trim();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          nums.push(num);
        }
      });
      return nums;
    });
    
    // 验证计算结果
    expect(results.length).toBeGreaterThan(0);
    console.log(`   📊 计算结果数量: ${results.length}`);
    
    // 验证包含负数结果
    const hasNegativeResults = results.some(r => r < 0);
    expect(hasNegativeResults).toBe(true);
    console.log('   ✅ 负数处理修复成功：计算结果包含负数');
    
    // 验证负负得正
    const expectedPositive = (-100) * (-50); // 5000
    const foundPositive = results.find(r => Math.abs(r - expectedPositive) < 100);
    if (foundPositive) {
      console.log(`   ✅ 负负得正验证成功: 找到结果${foundPositive}`);
    }
  });

  test('3. 股东公式映射修复验证', async ({ page }) => {
    console.log('🧪 测试3: 股东公式映射修复验证');
    
    const addButton = page.locator('button:has-text("添加客户")').first();
    
    // 添加3个股东
    for (let i = 1; i <= 3; i++) {
      await addButton.click();
      await page.waitForTimeout(2000);
      console.log(`   ✅ 添加第${i}个股东`);
    }
    
    // 验证股东数量
    const tables = await page.$$('table');
    expect(tables.length).toBeGreaterThanOrEqual(3);
    console.log(`   📊 股东数量: ${tables.length}`);
    
    // 检查公式映射
    const pageContent = await page.textContent('body');
    
    const expectedFormulas = [
      { shareholder: 1, formulas: ['1.31', '1.32'] },
      { shareholder: 2, formulas: ['1.41', '1.42'] },
      { shareholder: 3, formulas: ['1.51', '1.52'] }
    ];
    
    for (const { shareholder, formulas } of expectedFormulas) {
      const foundFormulas = formulas.filter(f => pageContent.includes(f));
      expect(foundFormulas.length).toBe(formulas.length);
      console.log(`   ✅ 股东${shareholder}公式映射正确: ${formulas.join('&')}`);
    }
    
    // 输入不同数据验证计算差异
    if (tables.length >= 2) {
      // 第1个股东数据
      const table1Inputs = await tables[0].$$('input');
      const data1 = [100, 2, 50, 1];
      for (let i = 0; i < Math.min(4, table1Inputs.length); i++) {
        await table1Inputs[i].fill(data1[i].toString());
        await page.waitForTimeout(200);
      }
      
      // 第2个股东数据
      const table2Inputs = await tables[1].$$('input');
      const data2 = [200, 3, 75, 2];
      for (let i = 0; i < Math.min(4, table2Inputs.length); i++) {
        await table2Inputs[i].fill(data2[i].toString());
        await page.waitForTimeout(200);
      }
      
      console.log('   📝 输入不同股东数据验证计算差异');
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(4000);
      
      // 验证产生不同结果
      const allResults = await page.evaluate(() => {
        const nums = [];
        document.querySelectorAll('strong').forEach(el => {
          const text = el.textContent.trim();
          const num = parseFloat(text);
          if (!isNaN(num) && isFinite(num)) {
            nums.push(num);
          }
        });
        return nums;
      });
      
      const uniqueResults = [...new Set(allResults)];
      expect(uniqueResults.length).toBeGreaterThan(1);
      console.log(`   ✅ 股东公式映射产生不同结果: ${uniqueResults.length}种不同值`);
    }
  });

  test('4. 计算精度修复验证', async ({ page }) => {
    console.log('🧪 测试4: 计算精度修复验证');
    
    // 添加客户
    await page.locator('button:has-text("添加客户")').first().click();
    await page.waitForTimeout(2000);
    
    // 获取输入框
    const inputs = await page.$$('input');
    
    // 输入小数测试数据
    const precisionData = [10.5, 3.33, 2.25, 1.67];
    console.log('   📝 输入小数精度测试数据: 10.5, 3.33, 2.25, 1.67');
    
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      await inputs[i].fill(precisionData[i].toString());
      await page.waitForTimeout(300);
      
      // 验证小数输入被接受
      const value = await inputs[i].inputValue();
      expect(value).toBe(precisionData[i].toString());
      console.log(`   ✅ 小数输入${i+1}: ${precisionData[i]} 输入成功`);
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    await page.waitForTimeout(4000);
    
    // 获取计算结果
    const results = await page.evaluate(() => {
      const nums = [];
      document.querySelectorAll('strong').forEach(el => {
        const text = el.textContent.trim();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          nums.push(num);
        }
      });
      return nums;
    });
    
    // 验证包含小数结果
    const hasDecimalResults = results.some(r => r % 1 !== 0);
    expect(hasDecimalResults).toBe(true);
    console.log('   ✅ 计算精度修复成功：结果包含小数');
    
    // 验证具体精度计算
    const expectedMultiplication = 10.5 * 3.33; // 34.965
    const foundMultiplication = results.find(r => Math.abs(r - expectedMultiplication) < 0.1);
    if (foundMultiplication) {
      console.log(`   ✅ 乘法精度验证成功: 找到结果${foundMultiplication}`);
    }
    
    const decimalResults = results.filter(r => r % 1 !== 0);
    console.log(`   📈 小数结果示例: ${decimalResults.slice(0, 3).join(', ')}`);
  });

  test('5. 真实业务场景验证', async ({ page }) => {
    console.log('🧪 测试5: 真实业务场景验证');
    
    // 添加客户
    await page.locator('button:has-text("添加客户")').first().click();
    await page.waitForTimeout(2000);
    
    // 获取输入框
    const inputs = await page.$$('input');
    
    // 场景1: 亏损企业
    console.log('   💼 场景1: 亏损企业测试');
    const lossData = [300000, 400000, 100000, -10000]; // 收入30万，成本40万，费用10万，税费减免1万
    console.log('   📝 输入亏损企业数据: 收入30万, 成本40万, 费用10万, 税费减免1万');
    
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      await inputs[i].fill(lossData[i].toString());
      await page.waitForTimeout(300);
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    await page.waitForTimeout(4000);
    
    // 验证亏损计算
    const results = await page.evaluate(() => {
      const nums = [];
      document.querySelectorAll('strong').forEach(el => {
        const text = el.textContent.trim();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          nums.push(num);
        }
      });
      return nums;
    });
    
    // 验证包含负数（亏损）
    const hasNegativeResults = results.some(r => r < 0);
    expect(hasNegativeResults).toBe(true);
    console.log('   ✅ 亏损企业场景：正确显示负数亏损');
    
    // 验证预期亏损金额
    const expectedLoss = 300000 - 400000 - 100000 + 10000; // -190000
    const foundLoss = results.find(r => Math.abs(r - expectedLoss) < 5000);
    if (foundLoss) {
      console.log(`   ✅ 亏损金额计算正确: 找到${foundLoss.toLocaleString()}元`);
    }
    
    console.log('   ✅ 真实业务场景修复成功');
  });

  test('6. 综合功能稳定性测试', async ({ page }) => {
    console.log('🧪 测试6: 综合功能稳定性测试');
    
    // 添加多个客户并输入各种数据
    const testCases = [
      { name: '正数数据', data: [1000, 500, 250, 100] },
      { name: '负数数据', data: [-1000, -500, -250, -100] },
      { name: '小数数据', data: [10.5, 5.25, 2.75, 1.33] },
      { name: '混合数据', data: [1000, -500, 25.5, -10.25] }
    ];
    
    for (const testCase of testCases) {
      console.log(`   📝 测试${testCase.name}: ${testCase.data.join(', ')}`);
      
      // 添加客户
      await page.locator('button:has-text("添加客户")').first().click();
      await page.waitForTimeout(1000);
      
      // 输入数据
      const tables = await page.$$('table');
      const lastTable = tables[tables.length - 1];
      const inputs = await lastTable.$$('input');
      
      for (let i = 0; i < Math.min(4, inputs.length); i++) {
        await inputs[i].fill(testCase.data[i].toString());
        await page.waitForTimeout(200);
      }
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(2000);
      
      // 验证有计算结果
      const results = await page.evaluate(() => {
        const nums = [];
        document.querySelectorAll('strong').forEach(el => {
          const text = el.textContent.trim();
          const num = parseFloat(text);
          if (!isNaN(num) && isFinite(num)) {
            nums.push(num);
          }
        });
        return nums;
      });
      
      expect(results.length).toBeGreaterThan(0);
      console.log(`   ✅ ${testCase.name}计算正常，结果数量: ${results.length}`);
    }
    
    console.log('   ✅ 综合功能稳定性测试通过');
  });

});
