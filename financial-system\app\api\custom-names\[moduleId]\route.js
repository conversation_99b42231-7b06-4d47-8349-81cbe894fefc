// API路由 - 自定义名称管理
// GET /api/custom-names/[moduleId] - 获取模块的自定义名称
// PUT /api/custom-names/[moduleId] - 批量更新模块的自定义名称

import { NextResponse } from 'next/server';
import { MODULE_CONFIGS } from '../../../../config/moduleConfigs';

/**
 * 获取模块的自定义名称配置
 * GET /api/custom-names/[moduleId]
 */
export async function GET(request, { params }) {
  try {
    const { moduleId } = params;
    
    // 验证模块ID
    if (!MODULE_CONFIGS[moduleId]) {
      return NextResponse.json(
        { success: false, error: '模块不存在' },
        { status: 404 }
      );
    }
    
    const moduleConfig = MODULE_CONFIGS[moduleId];
    const customNames = moduleConfig.customNames || { formulas: {}, dataFields: {} };
    
    // 构建响应数据
    const responseData = {
      moduleId,
      moduleName: moduleConfig.displayName,
      customNames: {
        formulas: {},
        dataFields: {}
      }
    };
    
    // 处理公式名称
    Object.keys(customNames.formulas || {}).forEach(formulaId => {
      const formula = customNames.formulas[formulaId];
      responseData.customNames.formulas[formulaId] = {
        defaultName: formula.defaultName,
        customName: formula.customName || formula.defaultName,
        expression: formula.expression,
        lastModified: formula.lastModified,
        isCustomized: !!formula.customName
      };
    });
    
    // 处理数据字段名称
    Object.keys(customNames.dataFields || {}).forEach(fieldId => {
      const field = customNames.dataFields[fieldId];
      responseData.customNames.dataFields[fieldId] = {
        defaultName: field.defaultName,
        customName: field.customName || field.defaultName,
        fieldType: field.fieldType,
        lastModified: field.lastModified,
        isCustomized: !!field.customName
      };
    });
    
    return NextResponse.json({
      success: true,
      data: responseData
    });
    
  } catch (error) {
    console.error('获取自定义名称失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 批量更新模块的自定义名称
 * PUT /api/custom-names/[moduleId]
 * Body: { formulas?: {}, dataFields?: {} }
 */
export async function PUT(request, { params }) {
  try {
    const { moduleId } = params;
    const body = await request.json();
    
    // 验证模块ID
    if (!MODULE_CONFIGS[moduleId]) {
      return NextResponse.json(
        { success: false, error: '模块不存在' },
        { status: 404 }
      );
    }
    
    const { formulas = {}, dataFields = {} } = body;
    const moduleConfig = MODULE_CONFIGS[moduleId];
    
    // 确保customNames结构存在
    if (!moduleConfig.customNames) {
      moduleConfig.customNames = { formulas: {}, dataFields: {} };
    }
    
    const updateResults = [];
    const currentTime = new Date().toISOString();
    
    // 更新公式名称
    for (const [formulaId, formulaData] of Object.entries(formulas)) {
      try {
        const { customName, expression } = formulaData;
        
        // 验证数据
        if (!customName || customName.trim().length === 0) {
          updateResults.push({
            fieldId: formulaId,
            success: false,
            error: '公式名称不能为空'
          });
          continue;
        }
        
        if (!expression || expression.trim().length === 0) {
          updateResults.push({
            fieldId: formulaId,
            success: false,
            error: '公式表达式不能为空'
          });
          continue;
        }
        
        // 更新配置
        if (!moduleConfig.customNames.formulas[formulaId]) {
          moduleConfig.customNames.formulas[formulaId] = {
            defaultName: `公式${formulaId.replace('formula', '').replace('_', '.')}`,
            customName: null,
            expression: '',
            lastModified: null
          };
        }
        
        const oldName = moduleConfig.customNames.formulas[formulaId].customName;
        moduleConfig.customNames.formulas[formulaId].customName = customName.trim();
        moduleConfig.customNames.formulas[formulaId].expression = expression.trim();
        moduleConfig.customNames.formulas[formulaId].lastModified = currentTime;
        
        updateResults.push({
          fieldId: formulaId,
          success: true,
          oldName,
          newName: customName.trim()
        });
        
      } catch (error) {
        updateResults.push({
          fieldId: formulaId,
          success: false,
          error: error.message
        });
      }
    }
    
    // 更新数据字段名称
    for (const [fieldId, fieldData] of Object.entries(dataFields)) {
      try {
        const { customName } = fieldData;
        
        // 验证数据
        if (!customName || customName.trim().length === 0) {
          updateResults.push({
            fieldId,
            success: false,
            error: '字段名称不能为空'
          });
          continue;
        }
        
        // 更新配置
        if (!moduleConfig.customNames.dataFields[fieldId]) {
          moduleConfig.customNames.dataFields[fieldId] = {
            defaultName: `数据${fieldId.replace('data', '')}`,
            customName: null,
            fieldType: 'decimal',
            lastModified: null
          };
        }
        
        const oldName = moduleConfig.customNames.dataFields[fieldId].customName;
        moduleConfig.customNames.dataFields[fieldId].customName = customName.trim();
        moduleConfig.customNames.dataFields[fieldId].lastModified = currentTime;
        
        updateResults.push({
          fieldId,
          success: true,
          oldName,
          newName: customName.trim()
        });
        
      } catch (error) {
        updateResults.push({
          fieldId,
          success: false,
          error: error.message
        });
      }
    }
    
    // 统计结果
    const successCount = updateResults.filter(r => r.success).length;
    const failedCount = updateResults.filter(r => !r.success).length;
    
    return NextResponse.json({
      success: failedCount === 0,
      data: {
        moduleId,
        updateResults,
        summary: {
          total: updateResults.length,
          success: successCount,
          failed: failedCount
        }
      }
    });
    
  } catch (error) {
    console.error('更新自定义名称失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
