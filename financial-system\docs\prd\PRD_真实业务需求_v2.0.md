# 财务管理系统 - 真实业务需求产品规格书 v2.0

## 1. 文档信息
- **版本**: v2.0
- **创建日期**: 2025年7月30日
- **负责人**: Emma (产品经理)
- **更新原因**: 基于老板提供的真实页面截图，重新理解业务需求

## 2. 背景与问题陈述

### 2.1 真实业务场景
基于老板提供的实际页面，系统包含以下核心页面：

1. **主汇总表页面** - 显示所有客户的汇总数据
2. **客户详细表页面** - 每个客户的详细数据录入和计算
3. **周报表页面** - 按周汇总的数据报表
4. **月报表页面** - 按月汇总的数据报表

### 2.2 核心业务流程
```
客户详细表数据录入 → 公式自动计算 → 生成最终汇总值 → 更新主汇总表 → 生成周/月报表
```

### 2.3 当前痛点
- 公式参数无法灵活配置（如收入=数量*单价的参数字段固定）
- 数据字段名称无法自定义（数据1、数据2等不够直观）
- 客户详细表计算结果无法自动更新到主汇总表
- 字段名称修改后无法同步到所有相关报表

## 3. 目标与成功指标

### 3.1 项目目标
- **灵活性目标**: 用户可以自定义数据字段名称和公式参数
- **自动化目标**: 客户详细表结果自动更新到主汇总表
- **一致性目标**: 字段名称修改同步到所有相关页面
- **效率目标**: 减少手动数据转移，提升工作效率

### 3.2 关键结果指标
- 字段名称自定义成功率 = 100%
- 数据自动同步准确率 = 100%
- 公式参数配置灵活度 = 支持所有固定公式结构
- 用户操作步骤减少 = 50%以上

## 4. 用户画像与用户故事

### 4.1 目标用户
**财务数据管理员**
- 需要管理多个客户的财务数据
- 需要生成周报表和月报表
- 希望字段名称更直观易懂
- 希望减少重复的数据录入工作

### 4.2 核心用户故事

**故事1: 自定义字段名称**
> 作为财务管理员，我希望将"数据1"改为"收入数量"，这样我和同事都能更清楚地理解这个字段的含义。

**故事2: 配置公式参数**
> 作为财务管理员，我希望能够配置收入公式中的"数量字段"和"单价字段"，这样当我修改字段名称时，公式仍然能正确计算。

**故事3: 自动数据更新**
> 作为财务管理员，当我在客户详细表中完成数据录入和计算后，我希望最终结果能自动更新到主汇总表，而不需要手动复制粘贴。

## 5. 功能规格详述

### 5.1 可编辑字段名称功能

#### 5.1.1 基本交互
- **触发方式**: 双击字段名称（如"数据1"）
- **编辑界面**: 内联编辑框，支持实时预览
- **保存方式**: Enter键确认，Esc键取消
- **验证规则**: 
  - 长度限制：1-20个字符
  - 不允许重复名称
  - 不允许特殊字符

#### 5.1.2 同步范围
字段名称修改后，需要同步更新到：
- ✅ 客户详细表的表头
- ✅ 主汇总表的相关显示
- ✅ 周报表的字段引用
- ✅ 月报表的字段引用
- ✅ 公式编辑器的变量选择器

### 5.2 公式参数配置功能

#### 5.2.1 固定公式结构
基于真实业务，系统包含以下固定公式：
```javascript
// 收入计算
收入 = 数量字段 * 单价字段

// 付出计算  
付出 = 数量字段 * 单价字段 * (-1)

// 余额计算
余额 = 上期余额 + 收入 - 付出 - 减免

// 最终汇总
最终汇总 = 所有计算结果的加权求和
```

#### 5.2.2 参数配置界面
- **配置方式**: 点击公式名称进入配置模式
- **参数选择**: 下拉选择可用的数据字段
- **实时预览**: 显示公式计算结果
- **保存验证**: 确保所选字段数据类型正确

### 5.3 自动数据更新功能

#### 5.3.1 更新触发条件
- 客户详细表数据修改时
- 公式参数配置变更时
- 用户点击"保存并更新汇总"按钮时

#### 5.3.2 更新流程
```
1. 客户详细表计算完成
2. 提取最终汇总值
3. 调用更新API
4. 更新主汇总表对应客户行
5. 触发周/月报表重新计算
6. 显示更新成功提示
```

#### 5.3.3 数据验证
- 计算结果数值验证
- 数据完整性检查
- 异常情况处理和回滚

### 5.4 跨表同步功能

#### 5.4.1 同步触发
- 字段名称修改时自动触发
- 支持批量同步多个字段
- 提供同步进度提示

#### 5.4.2 同步范围
- **客户详细表**: 更新表头显示
- **主汇总表**: 更新相关列名
- **周报表**: 更新字段引用和显示
- **月报表**: 更新字段引用和显示
- **公式配置**: 更新变量名称

## 6. 范围定义

### 6.1 包含功能 (In Scope)
✅ 字段名称双击编辑  
✅ 公式参数配置界面  
✅ 客户详细表到主汇总表的自动更新  
✅ 跨表字段名称同步  
✅ 数据验证和错误处理  
✅ 操作历史记录  

### 6.2 排除功能 (Out of Scope)
❌ 公式结构的完全自定义（保持固定结构）  
❌ 历史数据的批量修改  
❌ 复杂的权限管理  
❌ 数据导入导出功能  

## 7. 依赖与风险

### 7.1 技术依赖
- 现有的客户详细表组件
- 主汇总表数据结构
- 周/月报表计算逻辑
- 数据库字段映射关系

### 7.2 业务依赖
- 用户对现有业务流程的理解
- 字段名称标准化规范
- 数据计算规则的确认

### 7.3 潜在风险
- **数据一致性风险**: 同步过程中可能出现数据不一致
- **性能风险**: 大量客户数据同步可能影响性能
- **用户体验风险**: 复杂的配置可能增加学习成本

### 7.4 风险缓解策略
- 实现事务性更新，确保数据一致性
- 采用异步处理和进度提示
- 提供详细的操作指南和帮助文档

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**: 修改现有组件，支持真实业务流程 (2天)
- **阶段2**: 实现自动数据更新功能 (1天)
- **阶段3**: 完善跨表同步机制 (1天)
- **阶段4**: 全面测试和优化 (1天)

### 8.2 测试计划
- **单元测试**: 各个功能组件独立测试
- **集成测试**: 数据流转完整性测试
- **用户验收测试**: 使用真实业务数据进行测试

### 8.3 上线策略
- **灰度发布**: 先在单个客户数据上测试
- **全量发布**: 确认无误后应用到所有客户
- **监控跟踪**: 实时监控数据同步状态

---

**Emma完成时间**: 2025年7月30日  
**下一步**: 提交给Mike进行技术评审和开发任务分配
