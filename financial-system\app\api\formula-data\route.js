// GET - 获取公式配置页面的所有数据
export async function GET() {
  try {
    // 动态导入数据库模块
    const { customerOperations, shareholderOperations, formulaOperations } = await import('../../../lib/database.js');

    const customers = customerOperations.getAll();
    
    // 为每个客户获取股东信息
    const customersWithShareholders = customers.map(customer => {
      const shareholders = shareholderOperations.getByCustomer(customer.key);
      return {
        key: customer.key,
        customer: customer.customer_name,
        user: customer.user_name,
        data1: customer.data1,
        data2: customer.data2,
        data3: customer.data3,
        data4: customer.data4,
        data5: customer.data5,
        data6: customer.data6,
        data7: customer.data7,
        result1: customer.result1,
        result2: customer.result2,
        result3: customer.result3,
        shareholders: shareholders.map(s => ({
          key: s.key,
          name: s.name,
          ratio: s.ratio,
          result1: s.result1,
          result2: s.result2
        }))
      };
    });

    // 按组分组
    const groupedData = {};
    customersWithShareholders.forEach(customer => {
      const groupKey = customers.find(c => c.key === customer.key)?.group_key || 'group1';
      if (!groupedData[groupKey]) {
        groupedData[groupKey] = [];
      }
      groupedData[groupKey].push(customer);
    });

    // 获取公式数据
    const formulas = formulaOperations.getAll();
    const formulaExpressions = {};
    formulas.forEach(formula => {
      formulaExpressions[formula.name] = formula.expression;
    });

    return Response.json({ 
      success: true, 
      data: {
        customerData: groupedData,
        formulaExpressions
      }
    });
  } catch (error) {
    console.error('获取公式配置数据失败:', error);
    return Response.json({ success: false, error: error.message }, { status: 500 });
  }
}

// POST - 保存客户数据
export async function POST(request) {
  try {
    // 动态导入数据库模块
    const { customerOperations, shareholderOperations, formulaOperations } = await import('../../../lib/database.js');

    const { action, data } = await request.json();
    
    switch (action) {
      case 'createCustomer':
        const createResult = customerOperations.create({
          ...data.customerData,
          group_key: data.groupKey
        });
        return Response.json({ success: true, data: { id: createResult.lastInsertRowid } });
        
      case 'updateCustomer':
        const updateResult = customerOperations.update(data.key, data.customerData);
        return Response.json({ success: true, data: { changes: updateResult.changes } });
        
      case 'deleteCustomer':
        const deleteResult = customerOperations.delete(data.key);
        return Response.json({ success: true, data: { changes: deleteResult.changes } });
        
      case 'createShareholder':
        const createShareholderResult = shareholderOperations.create({
          ...data.shareholderData,
          customer_key: data.customerKey
        });
        return Response.json({ success: true, data: { id: createShareholderResult.lastInsertRowid } });
        
      case 'updateShareholder':
        const updateShareholderResult = shareholderOperations.update(data.key, data.shareholderData);
        return Response.json({ success: true, data: { changes: updateShareholderResult.changes } });
        
      case 'deleteShareholder':
        const deleteShareholderResult = shareholderOperations.delete(data.key);
        return Response.json({ success: true, data: { changes: deleteShareholderResult.changes } });
        
      case 'saveFormula':
        const saveFormulaResult = formulaOperations.upsert(data.name, data.expression);
        return Response.json({ success: true, data: { changes: saveFormulaResult.changes } });
        
      default:
        return Response.json({ success: false, error: '未知的操作类型' }, { status: 400 });
    }
  } catch (error) {
    console.error('保存数据失败:', error);
    return Response.json({ success: false, error: error.message }, { status: 500 });
  }
}
