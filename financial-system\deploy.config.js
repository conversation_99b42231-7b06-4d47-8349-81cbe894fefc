// 路由侠部署配置文件
// 财务管理系统 - JavaScript版本

module.exports = {
  // 项目基本信息
  name: '财务管理系统',
  version: '1.0.0',
  description: '代理商盈亏跟踪器 - JavaScript版本',
  
  // 构建配置
  build: {
    // 构建命令
    command: 'npm run build',
    
    // 输出目录
    outputDir: '.next',
    
    // 静态文件目录
    staticDir: 'public',
    
    // 环境变量
    env: {
      NODE_ENV: 'production',
      DATABASE_URL: 'file:./prod.db',
    },
  },
  
  // 服务器配置
  server: {
    // 端口配置
    port: 3000,
    
    // 启动命令
    startCommand: 'npm start',
    
    // 健康检查
    healthCheck: {
      path: '/api/health',
      timeout: 30000,
    },
  },
  
  // 路由配置
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path: '/login',
      component: 'login',
    },
    {
      path: '/dashboard',
      component: 'dashboard',
      auth: true,
    },
    {
      path: '/summary/:id',
      component: 'summary',
      auth: true,
    },
    {
      path: '/reports/weekly',
      component: 'reports/weekly',
      auth: true,
    },
    {
      path: '/reports/yearly',
      component: 'reports/yearly',
      auth: true,
    },
    {
      path: '/reports/monthly',
      component: 'reports/monthly',
      auth: true,
    },
    {
      path: '/periods/:id',
      component: 'periods',
      auth: true,
    },
  ],
  
  // 数据库配置
  database: {
    type: 'sqlite',
    path: './prod.db',
    migrations: './prisma/migrations',
    backup: {
      enabled: true,
      schedule: '0 2 * * *', // 每天凌晨2点备份
      retention: 30, // 保留30天
    },
  },
  
  // 安全配置
  security: {
    // HTTPS配置
    https: {
      enabled: true,
      forceRedirect: true,
    },
    
    // CORS配置
    cors: {
      origin: ['https://yourdomain.com'],
      credentials: true,
    },
    
    // 速率限制
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 最多100个请求
    },
  },
  
  // 监控配置
  monitoring: {
    // 日志配置
    logging: {
      level: 'info',
      format: 'json',
      rotation: {
        maxFiles: 10,
        maxSize: '10m',
      },
    },
    
    // 性能监控
    performance: {
      enabled: true,
      sampleRate: 0.1,
    },
    
    // 错误追踪
    errorTracking: {
      enabled: true,
      dsn: process.env.SENTRY_DSN,
    },
  },
  
  // 缓存配置
  cache: {
    // 静态资源缓存
    static: {
      maxAge: '1y',
      immutable: true,
    },
    
    // API缓存
    api: {
      maxAge: '5m',
      staleWhileRevalidate: '1h',
    },
  },
  
  // 部署钩子
  hooks: {
    // 部署前钩子
    preDeploy: [
      'npm ci',
      'npx prisma generate',
      'npx prisma migrate deploy',
    ],
    
    // 部署后钩子
    postDeploy: [
      'npm run health-check',
      'npm run warm-up',
    ],
  },
  
  // 环境配置
  environments: {
    production: {
      domain: 'your-domain.com',
      ssl: true,
      database: {
        url: 'file:./prod.db',
      },
    },
    staging: {
      domain: 'staging.your-domain.com',
      ssl: true,
      database: {
        url: 'file:./staging.db',
      },
    },
  },
};
