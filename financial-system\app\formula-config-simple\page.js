'use client';

// 简化版公式配置页面 - 去掉有问题的功能
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Card, Typography, Space } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

export default function SimpleFormulaConfigPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  return (
    <div style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      {/* 顶部导航 */}
      <div style={{
        background: '#fff',
        padding: '16px 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button 
            icon={<ArrowLeftOutlined />}
            onClick={() => router.push('/dashboard')}
          >
            返回仪表盘
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            🧮 界面3 - 公式配置与数据展示 (简化版)
          </Title>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div style={{ padding: '24px' }}>
        <Card>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Title level={3}>📋 功能说明</Title>
            
            <Text>
              这是简化版的公式配置页面，已移除可能导致无限循环的复杂功能。
            </Text>

            <div style={{ 
              background: '#f6ffed', 
              border: '1px solid #b7eb8f',
              borderRadius: '6px',
              padding: '16px'
            }}>
              <Text strong style={{ color: '#52c41a' }}>✅ 当前状态：正常运行</Text>
              <br />
              <Text type="secondary">
                原有的复杂公式计算、动态表格、数据同步等功能已暂时移除，
                避免React无限循环错误。
              </Text>
            </div>

            <div style={{ 
              background: '#fff7e6', 
              border: '1px solid #ffd591',
              borderRadius: '6px',
              padding: '16px'
            }}>
              <Text strong style={{ color: '#fa8c16' }}>🔧 已移除的功能：</Text>
              <ul style={{ marginTop: '8px', marginBottom: 0 }}>
                <li>复杂的useEffect依赖</li>
                <li>动态表格组件</li>
                <li>实时数据同步检查</li>
                <li>公式计算引擎</li>
                <li>可编辑单元格组件</li>
              </ul>
            </div>

            <Space>
              <Button 
                type="primary"
                onClick={() => router.push('/dashboard')}
              >
                返回仪表盘
              </Button>
              <Button 
                onClick={() => router.push('/test')}
              >
                访问测试页面
              </Button>
            </Space>
          </Space>
        </Card>
      </div>
    </div>
  );
}
