# 财务管理系统 - 公式编辑功能架构设计

## 1. 文档信息
- **项目名称**: 财务管理系统 - 公式编辑功能架构设计
- **版本**: v1.0
- **创建日期**: 2025年7月30日
- **负责人**: Bob (架构师)
- **状态**: 设计中

## 2. 架构概览

### 2.1 核心设计原则
- **可扩展性**: 支持未来更多模块的动态配置
- **数据一致性**: 确保跨模块数据同步的准确性
- **用户体验**: 提供流畅的编辑和实时反馈
- **性能优化**: 最小化不必要的重新渲染和数据更新

### 2.2 技术栈选择
- **前端框架**: React 18 + Next.js 14
- **状态管理**: Zustand (现有) + 新增同步状态管理
- **UI组件**: Antd 5.21 + 自定义编辑组件
- **数据存储**: 扩展现有SQLite + Prisma ORM
- **计算引擎**: 扩展现有FormulaEngine

## 3. 系统架构设计

### 3.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Presentation Layer)              │
├─────────────────────────────────────────────────────────────┤
│  EditableLabel  │  FormulaEditor  │  SyncStatusIndicator    │
│  组件           │  组件           │  组件                    │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Logic Layer)           │
├─────────────────────────────────────────────────────────────┤
│  NameSyncManager │  FormulaValidator │  DataPlantManager    │
│  名称同步管理器    │  公式验证器        │  数据植入管理器        │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access Layer)             │
├─────────────────────────────────────────────────────────────┤
│  CustomNameStore │  ModuleConfigAPI  │  SummaryTableAPI     │
│  自定义名称存储    │  模块配置API       │  汇总表API           │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (Data Storage Layer)            │
├─────────────────────────────────────────────────────────────┤
│  SQLite Database │  moduleConfigs.js │  LocalStorage        │
│  主数据库         │  配置文件          │  临时存储            │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心组件设计

#### 3.2.1 EditableLabel 组件
**功能**: 可编辑的标签组件，支持双击编辑
```javascript
// EditableLabel.js
const EditableLabel = ({
  value,           // 当前显示值
  defaultValue,    // 默认值
  onSave,         // 保存回调
  onCancel,       // 取消回调
  maxLength,      // 最大长度
  validator,      // 验证函数
  placeholder,    // 占位符
  editable = true // 是否可编辑
}) => {
  // 组件实现...
}
```

**状态管理**:
- `isEditing`: 是否处于编辑状态
- `tempValue`: 临时编辑值
- `isValid`: 当前输入是否有效
- `isSaving`: 是否正在保存

#### 3.2.2 FormulaEditor 组件
**功能**: 公式编辑器，支持语法高亮和验证
```javascript
// FormulaEditor.js
const FormulaEditor = ({
  formula,        // 当前公式
  variables,      // 可用变量列表
  onFormulaChange, // 公式变更回调
  onValidate,     // 验证回调
  readOnly = false // 是否只读
}) => {
  // 组件实现...
}
```

#### 3.2.3 NameSyncManager 业务逻辑
**功能**: 管理跨模块的名称同步
```javascript
// NameSyncManager.js
class NameSyncManager {
  // 同步字段名称到所有相关模块
  async syncFieldName(fieldId, newName, moduleId) {
    const affectedModules = this.getAffectedModules(fieldId);
    const syncTasks = affectedModules.map(module => 
      this.updateModuleFieldName(module, fieldId, newName)
    );
    
    try {
      await Promise.all(syncTasks);
      this.notifySyncSuccess(fieldId, newName);
    } catch (error) {
      this.handleSyncError(error, fieldId);
    }
  }
  
  // 获取受影响的模块列表
  getAffectedModules(fieldId) {
    // 实现逻辑...
  }
}
```

### 3.3 数据结构设计

#### 3.3.1 自定义名称存储结构
```javascript
// 扩展 moduleConfigs.js
export const MODULE_CONFIGS = {
  'module1_1': {
    // 现有配置保持不变...
    id: 'module1_1',
    name: 'Module 1.1',
    
    // 新增：自定义名称配置
    customNames: {
      // 公式自定义名称
      formulas: {
        'formula1_1': {
          defaultName: '公式1.1',
          customName: '净利润计算', // 用户自定义名称
          expression: '(data1_1+data1_2*data1_4)*data1_3', // 用户自定义表达式
          lastModified: '2025-07-30T10:00:00Z'
        },
        'formula1_2': {
          defaultName: '公式1.2',
          customName: '投资回报率',
          expression: 'data1_6/data1_1*100',
          lastModified: '2025-07-30T10:00:00Z'
        }
      },
      
      // 数据字段自定义名称
      dataFields: {
        'data1_1': {
          defaultName: '数据1',
          customName: '总收入', // 用户自定义名称
          fieldType: 'currency',
          lastModified: '2025-07-30T10:00:00Z'
        },
        'data1_2': {
          defaultName: '数据2',
          customName: '总支出',
          fieldType: 'currency',
          lastModified: '2025-07-30T10:00:00Z'
        }
      }
    },
    
    // 新增：同步配置
    syncConfig: {
      // 当前模块字段变更时，需要同步的目标模块
      syncTargets: {
        'data1_1': ['module2_1', 'module3_1', 'summary_table', 'weekly_report', 'monthly_report'],
        'data1_2': ['module2_1', 'summary_table', 'weekly_report', 'monthly_report']
      }
    }
  }
}
```

#### 3.3.2 同步状态管理
```javascript
// 新增：SyncStatusStore.js
export const useSyncStatusStore = create((set, get) => ({
  // 同步状态
  syncStatus: {
    isSync: false,
    lastSyncTime: null,
    pendingSync: [], // 待同步的字段
    syncErrors: [],  // 同步错误
    syncHistory: []  // 同步历史
  },
  
  // 开始同步
  startSync: (fieldId, newName) => {
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        isSync: true,
        pendingSync: [...state.syncStatus.pendingSync, { fieldId, newName, timestamp: Date.now() }]
      }
    }));
  },
  
  // 完成同步
  completeSync: (fieldId) => {
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        isSync: false,
        lastSyncTime: Date.now(),
        pendingSync: state.syncStatus.pendingSync.filter(item => item.fieldId !== fieldId),
        syncHistory: [...state.syncStatus.syncHistory, { fieldId, status: 'success', timestamp: Date.now() }]
      }
    }));
  },
  
  // 同步失败
  syncError: (fieldId, error) => {
    set(state => ({
      syncStatus: {
        ...state.syncStatus,
        isSync: false,
        syncErrors: [...state.syncStatus.syncErrors, { fieldId, error, timestamp: Date.now() }],
        pendingSync: state.syncStatus.pendingSync.filter(item => item.fieldId !== fieldId)
      }
    }));
  }
}));
```

### 3.4 数据库扩展设计

#### 3.4.1 Prisma Schema 扩展
```prisma
// 新增：自定义名称配置表
model CustomFieldName {
  id          Int      @id @default(autoincrement())
  moduleId    String   // 模块ID
  fieldId     String   // 字段ID
  fieldType   String   // 字段类型：'data' | 'formula'
  defaultName String   // 默认名称
  customName  String?  // 自定义名称
  expression  String?  // 公式表达式（仅公式字段）
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@unique([moduleId, fieldId])
  @@index([moduleId])
  @@index([fieldType])
}

// 新增：同步历史记录表
model SyncHistory {
  id          Int      @id @default(autoincrement())
  fieldId     String   // 字段ID
  oldName     String?  // 旧名称
  newName     String   // 新名称
  syncTargets String   // 同步目标模块（JSON格式）
  status      String   // 同步状态：'success' | 'failed' | 'partial'
  errorMsg    String?  // 错误信息
  createdAt   DateTime @default(now())
  
  @@index([fieldId])
  @@index([status])
  @@index([createdAt])
}
```

## 4. API设计

### 4.1 自定义名称管理API

#### 4.1.1 获取字段自定义名称
```javascript
// GET /api/custom-names/:moduleId
export async function getCustomNames(moduleId) {
  const customNames = await prisma.customFieldName.findMany({
    where: { moduleId, isActive: true }
  });

  return {
    success: true,
    data: customNames
  };
}
```

#### 4.1.2 更新字段自定义名称
```javascript
// PUT /api/custom-names/:moduleId/:fieldId
export async function updateCustomName(moduleId, fieldId, data) {
  const { customName, expression } = data;

  // 1. 数据验证
  const validation = validateCustomName(customName, expression);
  if (!validation.isValid) {
    return { success: false, error: validation.error };
  }

  // 2. 更新数据库
  const updated = await prisma.customFieldName.upsert({
    where: { moduleId_fieldId: { moduleId, fieldId } },
    update: { customName, expression, updatedAt: new Date() },
    create: { moduleId, fieldId, customName, expression }
  });

  // 3. 触发同步
  await triggerSync(moduleId, fieldId, customName);

  return { success: true, data: updated };
}
```

### 4.2 数据同步API

#### 4.2.1 触发同步
```javascript
// POST /api/sync/trigger
export async function triggerSync(moduleId, fieldId, newName) {
  const syncTargets = getSyncTargets(moduleId, fieldId);
  const syncTasks = [];

  for (const target of syncTargets) {
    syncTasks.push(updateTargetModule(target, fieldId, newName));
  }

  try {
    await Promise.all(syncTasks);

    // 记录同步历史
    await prisma.syncHistory.create({
      data: {
        fieldId,
        newName,
        syncTargets: JSON.stringify(syncTargets),
        status: 'success'
      }
    });

    return { success: true, syncedTargets: syncTargets };
  } catch (error) {
    // 记录同步错误
    await prisma.syncHistory.create({
      data: {
        fieldId,
        newName,
        syncTargets: JSON.stringify(syncTargets),
        status: 'failed',
        errorMsg: error.message
      }
    });

    throw error;
  }
}
```

### 4.3 汇总表植入API

#### 4.3.1 数据植入
```javascript
// POST /api/summary/plant
export async function plantToSummary(agentId, moduleData) {
  // 1. 数据验证
  const validation = validateModuleData(moduleData);
  if (!validation.isValid) {
    return { success: false, error: validation.error };
  }

  // 2. 数据转换
  const summaryData = transformToSummaryFormat(moduleData);

  // 3. 植入汇总表
  const result = await prisma.summaryTable.upsert({
    where: { agentId_moduleId: { agentId, moduleId: moduleData.moduleId } },
    update: summaryData,
    create: { agentId, ...summaryData }
  });

  return { success: true, data: result };
}
```

## 5. 性能优化策略

### 5.1 前端性能优化
- **组件懒加载**: 编辑器组件按需加载
- **防抖处理**: 输入框变更使用防抖，避免频繁验证
- **虚拟滚动**: 大量数据时使用虚拟滚动
- **状态缓存**: 缓存计算结果，避免重复计算

### 5.2 后端性能优化
- **批量更新**: 同步操作使用批量更新
- **索引优化**: 为查询字段添加数据库索引
- **缓存策略**: 配置数据使用内存缓存
- **异步处理**: 非关键同步操作异步执行

### 5.3 数据库优化
- **连接池**: 使用数据库连接池
- **查询优化**: 优化复杂查询语句
- **分页查询**: 大数据量查询使用分页
- **定期清理**: 定期清理历史数据

## 6. 错误处理与监控

### 6.1 错误处理策略
- **输入验证**: 前端和后端双重验证
- **事务回滚**: 同步失败时自动回滚
- **错误恢复**: 提供手动重试机制
- **用户提示**: 友好的错误提示信息

### 6.2 监控指标
- **同步成功率**: 监控数据同步的成功率
- **响应时间**: 监控API响应时间
- **错误率**: 监控系统错误率
- **用户操作**: 监控用户编辑操作频率

## 7. 安全考虑

### 7.1 输入安全
- **XSS防护**: 过滤用户输入的HTML标签
- **SQL注入防护**: 使用参数化查询
- **公式安全**: 限制公式表达式的函数调用
- **长度限制**: 限制输入字段的最大长度

### 7.2 数据安全
- **权限控制**: 验证用户操作权限
- **数据备份**: 重要操作前备份数据
- **审计日志**: 记录所有数据变更操作
- **加密存储**: 敏感数据加密存储

---

**文档状态**: ✅ 已完成
**下一步**: 开始组件开发和API实现
