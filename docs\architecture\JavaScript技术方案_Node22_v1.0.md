# JavaScript技术方案设计 - 财务管理系统 (Node.js 22兼容版)

## 1. 文档信息
- **项目名称**: 财务管理系统（代理商盈亏跟踪器）
- **版本**: v1.0 (JavaScript版本)
- **创建日期**: 2025年7月27日
- **负责人**: <PERSON> (工程师)
- **Node.js版本**: 22.17.1 (LTS) - 兼容用户的v22.14.0
- **技术栈**: 纯JavaScript，避免TypeScript版本冲突

## 2. 版本兼容性分析

### 2.1 Node.js版本选择
**用户当前版本**: Node.js v22.14.0
**推荐目标版本**: Node.js v22.17.1 (LTS)
**兼容性策略**: 
- 使用Node.js 18.18+的稳定特性
- 避免使用Node.js 23+的新特性
- 确保向下兼容到Node.js 18

### 2.2 技术栈版本锁定
```json
{
  "engines": {
    "node": ">=18.18.0 <23.0.0"
  },
  "dependencies": {
    "next": "^14.2.30",
    "react": "^18.3.1", 
    "react-dom": "^18.3.1",
    "prisma": "^5.20.0",
    "@prisma/client": "^5.20.0",
    "antd": "^5.21.0",
    "zustand": "^4.5.5"
  }
}
```

## 3. 纯JavaScript技术栈

### 3.1 前端技术栈 (JavaScript Only)
**框架选择**: **Next.js 14 + React 18 (纯JavaScript)**
- **理由**: 
  - 避免TypeScript版本冲突问题
  - Next.js 14对Node.js 22完全兼容
  - 成熟稳定的JavaScript生态
  - 开发效率高，学习成本低

**UI组件库**: **Ant Design 5.21 + CSS Modules**
- **Ant Design**: 提供完整的JavaScript组件库
- **CSS Modules**: 避免样式冲突，无需额外配置

**状态管理**: **Zustand 4.5**
- **理由**: 轻量级，纯JavaScript，无TypeScript依赖

### 3.2 后端技术栈 (JavaScript Only)
**运行环境**: **Node.js 22 + Next.js API Routes**
- **数据库**: **SQLite3 + Prisma 5.20 (JavaScript模式)**
- **ORM配置**: 使用JavaScript配置，避免类型定义冲突

### 3.3 开发工具链 (简化版)
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Playwright**: 端到端测试（强制使用）
- **Jest**: 单元测试框架

## 4. 项目结构设计 (JavaScript)

### 4.1 目录结构
```
财务管理系统2/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/
│   │   │   └── login/          # 登录页面
│   │   ├── dashboard/          # 主仪表盘
│   │   ├── reports/
│   │   │   ├── weekly/         # 周利润表
│   │   │   └── yearly/         # 年利润表
│   │   ├── summary/            # 汇总表
│   │   └── api/                # API路由
│   ├── components/             # React组件 (纯JS)
│   ├── lib/                    # 工具库 (纯JS)
│   ├── store/                  # Zustand状态管理
│   └── styles/                 # CSS样式文件
├── prisma/
│   ├── schema.prisma          # 数据库模型
│   └── migrations/            # 数据库迁移
├── public/                    # 静态资源
├── tests/                     # 测试文件
└── docs/                      # 项目文档
```

### 4.2 配置文件 (JavaScript)
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },
  eslint: {
    dirs: ['src'],
  },
}

module.exports = nextConfig
```

## 5. 数据库设计 (SQLite3 + Prisma JavaScript)

### 5.1 Prisma配置 (JavaScript模式)
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
  output   = "../src/lib/prisma"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// 用户表 (简化版)
model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  password  String   
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 代理商表
model Agent {
  id          Int        @id @default(autoincrement())
  name        String
  clientName  String?
  userName    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  customers   Customer[]
  periods     Period[]
}

// 客户表
model Customer {
  id          Int      @id @default(autoincrement())
  name        String
  userName    String
  agentId     Int
  agent       Agent    @relation(fields: [agentId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 8种数据字段 (JavaScript数字类型)
  data1       Float?   // 千万整数 (使用Float避免BigInt兼容问题)
  data2       Float?   // 十亿整数
  data3       Float?   // 百分比 (存储为小数)
  data4       Float?   // 高精度数字
  data5       Float?   // 百分比 (存储为小数)
  data6       Float?   // 计算结果 (只读)
  data7       Float?   // 计算结果 (只读)
  data8       Float?   // 计算结果 (只读)
  
  financialData FinancialData[]
}

// 财务数据表
model FinancialData {
  id          Int      @id @default(autoincrement())
  customerId  Int
  customer    Customer @relation(fields: [customerId], references: [id])
  
  // 公式相关 (JSON字符串存储)
  formulas    String?  // JSON格式存储公式配置
  results     String?  // JSON格式存储计算结果
  
  // 汇总数据
  totalSum    Float?
  roundedSum  Int?
  
  // 右侧统计区域
  quantity    Int?
  price       Float?
  income      Float?
  expense     Float?
  cashFlow    Float?
  lastWeekBalance Float?
  reduction   Float?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

## 6. 核心功能实现 (JavaScript)

### 6.1 公式计算引擎 (纯JavaScript)
```javascript
// src/lib/calculations.js

/**
 * 简化的公式计算引擎 - 纯JavaScript实现
 */
class FormulaEngine {
  /**
   * 安全的公式解析器
   * @param {string} expression - 公式表达式
   * @param {Object} data - 数据对象
   * @returns {number} 计算结果
   */
  static parseFormula(expression, data) {
    // 替换变量名为实际值
    let formula = expression;
    Object.entries(data).forEach(([key, value]) => {
      const numValue = parseFloat(value) || 0;
      formula = formula.replace(new RegExp(key, 'g'), numValue.toString());
    });
    
    // 安全执行数学表达式
    try {
      // 只允许基本数学运算
      const safeFormula = formula.replace(/[^0-9+\-*/.() ]/g, '');
      return Function('"use strict"; return (' + safeFormula + ')')();
    } catch (error) {
      console.error('公式计算错误:', error);
      return 0;
    }
  }
  
  /**
   * 批量计算客户数据
   * @param {Object} customer - 客户数据
   * @returns {Object} 计算结果
   */
  static calculateCustomerData(customer) {
    const results = {};
    
    // 计算数据6: 数据4 - 数据5
    if (customer.data4 !== null && customer.data5 !== null) {
      results.data6 = Math.round(customer.data4 - customer.data5);
    }
    
    // 计算数据7: 基于百分比计算
    if (customer.data1 && customer.data3) {
      results.data7 = Math.round(customer.data1 * customer.data3 / 100);
    }
    
    // 计算数据8: 综合计算
    if (customer.data2 && customer.data5) {
      results.data8 = customer.data2 * customer.data5 / 100;
    }
    
    return results;
  }
  
  /**
   * 数据格式化工具
   */
  static formatData = {
    // 格式化千万整数
    formatData1: (value) => {
      if (value === null || value === undefined) return '';
      return Math.round(value).toLocaleString();
    },
    
    // 格式化百分比
    formatData3: (value) => {
      if (value === null || value === undefined) return '';
      return (value * 100).toFixed(2) + '%';
    },
    
    // 格式化高精度数字
    formatData4: (value) => {
      if (value === null || value === undefined) return '';
      return parseFloat(value).toFixed(8);
    }
  };
}

module.exports = { FormulaEngine };
```

### 6.2 API路由设计 (JavaScript)
```javascript
// src/app/api/customers/route.js
import { NextResponse } from 'next/server';
import { PrismaClient } from '@/lib/prisma';
import { FormulaEngine } from '@/lib/calculations';

const prisma = new PrismaClient();

export async function POST(request) {
  try {
    const data = await request.json();
    
    // 创建客户数据
    const customer = await prisma.customer.create({
      data: {
        name: data.name,
        userName: data.userName,
        agentId: parseInt(data.agentId),
        data1: parseFloat(data.data1) || null,
        data2: parseFloat(data.data2) || null,
        data3: parseFloat(data.data3) || null,
        data4: parseFloat(data.data4) || null,
        data5: parseFloat(data.data5) || null,
      }
    });
    
    // 计算衍生数据
    const calculations = FormulaEngine.calculateCustomerData(customer);
    
    // 更新计算结果
    const updatedCustomer = await prisma.customer.update({
      where: { id: customer.id },
      data: {
        data6: calculations.data6,
        data7: calculations.data7,
        data8: calculations.data8,
      }
    });
    
    return NextResponse.json(updatedCustomer);
  } catch (error) {
    console.error('创建客户失败:', error);
    return NextResponse.json({ error: '创建客户失败' }, { status: 500 });
  }
}

export async function GET() {
  try {
    const customers = await prisma.customer.findMany({
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    return NextResponse.json(customers);
  } catch (error) {
    console.error('获取客户列表失败:', error);
    return NextResponse.json({ error: '获取客户列表失败' }, { status: 500 });
  }
}
```

## 7. 前端组件设计 (JavaScript + React)

### 7.1 状态管理 (Zustand JavaScript)
```javascript
// src/store/customerStore.js
import { create } from 'zustand';

export const useCustomerStore = create((set, get) => ({
  customers: [],
  loading: false,
  
  // Actions
  setCustomers: (customers) => set({ customers }),
  
  addCustomer: async (customerData) => {
    set({ loading: true });
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customerData),
      });
      
      if (response.ok) {
        const newCustomer = await response.json();
        set(state => ({ 
          customers: [...state.customers, newCustomer],
          loading: false 
        }));
      }
    } catch (error) {
      console.error('添加客户失败:', error);
      set({ loading: false });
    }
  },
  
  updateCustomer: async (id, updates) => {
    // 乐观更新
    set(state => ({
      customers: state.customers.map(c => 
        c.id === id ? { ...c, ...updates } : c
      )
    }));
    
    try {
      const response = await fetch(`/api/customers/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });
      
      if (response.ok) {
        const updatedCustomer = await response.json();
        set(state => ({
          customers: state.customers.map(c => 
            c.id === id ? updatedCustomer : c
          )
        }));
      }
    } catch (error) {
      console.error('更新客户失败:', error);
    }
  },
}));
```

## 8. 部署配置 (Node.js 22兼容)

### 8.1 package.json配置
```json
{
  "name": "financial-management-system",
  "version": "1.0.0",
  "engines": {
    "node": ">=18.18.0 <23.0.0"
  },
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate",
    "test": "jest",
    "test:e2e": "playwright test",
    "lint": "eslint src --ext .js,.jsx",
    "format": "prettier --write src"
  },
  "dependencies": {
    "next": "^14.2.30",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "prisma": "^5.20.0",
    "@prisma/client": "^5.20.0",
    "antd": "^5.21.0",
    "zustand": "^4.5.5"
  },
  "devDependencies": {
    "eslint": "^8.57.0",
    "eslint-config-next": "^14.2.30",
    "prettier": "^3.3.3",
    "@playwright/test": "^1.48.0",
    "jest": "^29.7.0"
  }
}
```

### 8.2 环境配置
```bash
# .env.local
DATABASE_URL="file:./dev.db"
NODE_ENV="development"
```

## 9. 开发计划 (JavaScript版本)

### 9.1 第一阶段 (1周): 基础搭建
- Next.js JavaScript项目初始化
- Prisma + SQLite3配置
- 基础UI组件开发 (Ant Design)
- 简单登录功能

### 9.2 第二阶段 (2周): 核心功能
- 主仪表盘开发
- 客户数据管理 (JavaScript)
- 基础公式计算引擎
- 报表页面开发

### 9.3 第三阶段 (1周): 测试和部署
- Playwright E2E测试
- 性能优化
- Context7环境部署
- 用户验收测试

## 10. 技术优势总结

### 10.1 JavaScript方案优势
- **版本兼容性**: 避免TypeScript版本冲突
- **开发效率**: 纯JavaScript，学习成本低
- **稳定性**: 使用成熟稳定的技术栈
- **维护性**: 代码简单直观，易于维护

### 10.2 Node.js 22兼容性
- **向下兼容**: 支持Node.js 18.18+
- **性能优化**: 利用Node.js 22的性能改进
- **长期支持**: Node.js 22是LTS版本
- **生态兼容**: 与现有npm包完全兼容

这个JavaScript技术方案完全避免了TypeScript版本冲突问题，使用纯JavaScript开发，确保与Node.js 22.14.0的完美兼容。

## 11. 具体实现步骤

### 11.1 项目初始化命令
```bash
# 1. 创建Next.js项目 (JavaScript)
npx create-next-app@14.2.30 财务管理系统2 --js --no-typescript --app --eslint --no-tailwind --no-src-dir

# 2. 进入项目目录
cd 财务管理系统2

# 3. 安装核心依赖
npm install prisma@5.20.0 @prisma/client@5.20.0 antd@5.21.0 zustand@4.5.5

# 4. 安装开发依赖
npm install -D @playwright/test@1.48.0 prettier@3.3.3

# 5. 初始化Prisma
npx prisma init --datasource-provider sqlite
```

### 11.2 关键配置文件

#### ESLint配置 (.eslintrc.json)
```json
{
  "extends": ["next/core-web-vitals"],
  "rules": {
    "no-unused-vars": "warn",
    "no-console": "off",
    "prefer-const": "error"
  }
}
```

#### Prettier配置 (.prettierrc)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

#### Playwright配置 (playwright.config.js)
```javascript
// @ts-check
const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',

  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### 11.3 核心组件示例

#### 登录页面组件 (app/login/page.js)
```javascript
'use client';

import { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      // 简单的登录验证
      if (values.username === 'admin' && values.password === '123456') {
        message.success('登录成功！');
        router.push('/dashboard');
      } else {
        message.error('账号或密码错误！');
      }
    } catch (error) {
      message.error('登录失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      backgroundColor: '#f0f2f5'
    }}>
      <Card title="财务管理系统登录" style={{ width: 400 }}>
        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              size="large"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
              block
            >
              进去
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}
```

#### 客户数据表格组件 (components/CustomerDataTable.js)
```javascript
'use client';

import { useState, useEffect } from 'react';
import { Table, Input, Button, Space, message } from 'antd';
import { PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { useCustomerStore } from '@/store/customerStore';
import { FormulaEngine } from '@/lib/calculations';

export default function CustomerDataTable({ agentId }) {
  const { customers, addCustomer, updateCustomer } = useCustomerStore();
  const [editingKey, setEditingKey] = useState('');

  const isEditing = (record) => record.id === editingKey;

  const edit = (record) => {
    setEditingKey(record.id);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const save = async (id) => {
    try {
      const row = customers.find(item => item.id === id);
      await updateCustomer(id, row);
      setEditingKey('');
      message.success('保存成功！');
    } catch (error) {
      message.error('保存失败！');
    }
  };

  const handleDataChange = (id, field, value) => {
    const customer = customers.find(c => c.id === id);
    const updatedCustomer = { ...customer, [field]: parseFloat(value) || null };

    // 自动计算衍生数据
    const calculations = FormulaEngine.calculateCustomerData(updatedCustomer);
    updateCustomer(id, { ...updatedCustomer, ...calculations });
  };

  const columns = [
    {
      title: '客户名',
      dataIndex: 'name',
      width: 120,
      render: (text, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            value={text}
            onChange={(e) => updateCustomer(record.id, { name: e.target.value })}
          />
        ) : text;
      },
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      width: 120,
      render: (text, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            value={text}
            onChange={(e) => updateCustomer(record.id, { userName: e.target.value })}
          />
        ) : text;
      },
    },
    {
      title: '数据1 (千万整数)',
      dataIndex: 'data1',
      width: 150,
      render: (value, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleDataChange(record.id, 'data1', e.target.value)}
          />
        ) : FormulaEngine.formatData.formatData1(value);
      },
    },
    {
      title: '数据2 (十亿整数)',
      dataIndex: 'data2',
      width: 150,
      render: (value, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleDataChange(record.id, 'data2', e.target.value)}
          />
        ) : FormulaEngine.formatData.formatData1(value);
      },
    },
    {
      title: '数据3 (百分比)',
      dataIndex: 'data3',
      width: 120,
      render: (value, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleDataChange(record.id, 'data3', e.target.value)}
          />
        ) : FormulaEngine.formatData.formatData3(value);
      },
    },
    {
      title: '数据4 (高精度)',
      dataIndex: 'data4',
      width: 150,
      render: (value, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            type="number"
            step="0.00000001"
            value={value}
            onChange={(e) => handleDataChange(record.id, 'data4', e.target.value)}
          />
        ) : FormulaEngine.formatData.formatData4(value);
      },
    },
    {
      title: '数据5 (百分比)',
      dataIndex: 'data5',
      width: 120,
      render: (value, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            type="number"
            step="0.01"
            value={value}
            onChange={(e) => handleDataChange(record.id, 'data5', e.target.value)}
          />
        ) : FormulaEngine.formatData.formatData3(value);
      },
    },
    {
      title: '数据6 (计算结果)',
      dataIndex: 'data6',
      width: 150,
      render: (value) => FormulaEngine.formatData.formatData1(value),
    },
    {
      title: '数据7 (计算结果)',
      dataIndex: 'data7',
      width: 150,
      render: (value) => FormulaEngine.formatData.formatData1(value),
    },
    {
      title: '数据8 (计算结果)',
      dataIndex: 'data8',
      width: 150,
      render: (value) => FormulaEngine.formatData.formatData4(value),
    },
    {
      title: '操作',
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Space>
            <Button size="small" onClick={() => save(record.id)}>
              保存
            </Button>
            <Button size="small" onClick={cancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Button size="small" onClick={() => edit(record)}>
            编辑
          </Button>
        );
      },
    },
  ];

  const addNewCustomer = async () => {
    const newCustomer = {
      name: '新客户',
      userName: '新用户',
      agentId: agentId,
      data1: 0,
      data2: 0,
      data3: 0,
      data4: 0,
      data5: 0,
    };

    await addCustomer(newCustomer);
    message.success('添加客户成功！');
  };

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={addNewCustomer}
        >
          添加客户行
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={customers}
        rowKey="id"
        pagination={false}
        scroll={{ x: 1500 }}
        size="small"
      />
    </div>
  );
}
```

### 11.4 Playwright测试示例

#### E2E测试 (tests/e2e/login.spec.js)
```javascript
const { test, expect } = require('@playwright/test');

test.describe('登录功能测试', () => {
  test('成功登录', async ({ page }) => {
    await page.goto('/login');

    // 填写登录信息
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', '123456');

    // 点击登录按钮
    await page.click('button[type="submit"]');

    // 验证跳转到仪表盘
    await expect(page).toHaveURL('/dashboard');
  });

  test('登录失败', async ({ page }) => {
    await page.goto('/login');

    // 填写错误的登录信息
    await page.fill('input[placeholder="用户名"]', 'wrong');
    await page.fill('input[placeholder="密码"]', 'wrong');

    // 点击登录按钮
    await page.click('button[type="submit"]');

    // 验证错误提示
    await expect(page.locator('.ant-message-error')).toBeVisible();
  });
});
```

## 12. 部署指南

### 12.1 Context7环境部署
```bash
#!/bin/bash
# deploy.sh

echo "开始部署财务管理系统 (JavaScript版本)..."

# 1. 检查Node.js版本
node_version=$(node -v)
echo "当前Node.js版本: $node_version"

# 2. 安装依赖
echo "安装依赖..."
npm ci

# 3. 生成Prisma客户端
echo "生成数据库客户端..."
npx prisma generate

# 4. 数据库迁移
echo "执行数据库迁移..."
npx prisma db push

# 5. 构建应用
echo "构建应用..."
npm run build

# 6. 启动应用
echo "启动应用..."
npm run start

echo "部署完成！应用运行在 http://localhost:3000"
```

### 12.2 生产环境优化
```javascript
// next.config.js (生产环境)
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['@prisma/client'],
  },
  eslint: {
    dirs: ['src'],
  },
  // 生产环境优化
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  // 静态资源优化
  images: {
    domains: [],
    formats: ['image/webp', 'image/avif'],
  },
}

module.exports = nextConfig
```

这个完整的JavaScript技术方案确保了与Node.js 22.14.0的完美兼容，避免了所有TypeScript相关的版本冲突问题，同时提供了完整的实现指南和部署方案。
