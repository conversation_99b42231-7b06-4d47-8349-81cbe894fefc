// 耐心等待的公式配置页面测试
const { chromium } = require('playwright');

async function testFormulaPatient() {
  console.log('🎯 耐心测试公式配置页面');
  console.log('🌐 URL: http://dlsykgzq.w1.luyouxia.net/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1500
  });
  
  const page = await browser.newPage();
  
  // 监听页面日志
  page.on('console', msg => {
    console.log(`🌐 页面日志 [${msg.type()}]: ${msg.text()}`);
  });
  
  try {
    // 1. 尝试访问页面，使用更宽松的设置
    console.log('📊 步骤1: 访问公式配置页面（耐心等待）...');
    
    try {
      await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config/', {
        waitUntil: 'domcontentloaded', // 更宽松的等待条件
        timeout: 60000 // 增加到60秒
      });
    } catch (error) {
      console.log(`⚠️ 初始加载超时，但继续尝试: ${error.message}`);
    }
    
    // 等待页面稳定
    console.log('⏳ 等待页面稳定...');
    await page.waitForTimeout(10000);
    
    const currentUrl = page.url();
    console.log(`📍 当前URL: ${currentUrl}`);
    
    // 2. 检查页面内容
    console.log('\n📊 步骤2: 检查页面内容...');
    
    const title = await page.title();
    console.log(`📄 页面标题: "${title}"`);
    
    // 检查页面是否有内容
    const bodyText = await page.locator('body').textContent();
    const hasContent = bodyText.length > 100;
    console.log(`📝 页面内容长度: ${bodyText.length} 字符`);
    console.log(`${hasContent ? '✅' : '❌'} 页面内容: ${hasContent ? '已加载' : '内容不足'}`);
    
    if (bodyText.includes('登录') || bodyText.includes('login')) {
      console.log('🔐 页面显示登录相关内容，可能需要认证');
    }
    
    // 3. 查找页面元素
    console.log('\n📊 步骤3: 查找页面元素...');
    
    // 等待并查找各种元素
    const elementChecks = [
      { selector: 'table', name: '表格' },
      { selector: 'input', name: '输入框' },
      { selector: 'button', name: '按钮' },
      { selector: 'strong', name: '结果显示' },
      { selector: '.formula-card', name: '公式卡片' },
      { selector: 'text=公式', name: '公式文本' },
      { selector: 'text=配置', name: '配置文本' },
      { selector: 'text=添加', name: '添加按钮' }
    ];
    
    for (const check of elementChecks) {
      try {
        const elements = await page.$$(check.selector);
        const count = elements.length;
        console.log(`${count > 0 ? '✅' : '❌'} ${check.name}: ${count} 个`);
        
        if (count > 0 && check.name === '结果显示') {
          // 显示前几个结果值
          for (let i = 0; i < Math.min(5, count); i++) {
            try {
              const text = await elements[i].textContent();
              const num = parseFloat(text);
              if (!isNaN(num)) {
                console.log(`   📊 结果${i+1}: ${num}`);
              }
            } catch (e) {}
          }
        }
      } catch (error) {
        console.log(`❌ ${check.name}: 检查失败`);
      }
    }
    
    // 4. 尝试基本交互
    console.log('\n📊 步骤4: 尝试基本交互...');
    
    // 查找并点击添加按钮
    try {
      const addButtons = await page.$$('button:has-text("添加"), button:has-text("新增")');
      if (addButtons.length > 0) {
        console.log(`🔘 找到 ${addButtons.length} 个添加按钮，尝试点击...`);
        await addButtons[0].click();
        await page.waitForTimeout(3000);
        console.log('✅ 添加按钮点击成功');
        
        // 重新检查输入框数量
        const inputs = await page.$$('input');
        console.log(`📝 点击后输入框数量: ${inputs.length}`);
        
        if (inputs.length >= 4) {
          await testQuickCalculation(page, inputs);
        }
      } else {
        console.log('❌ 未找到添加按钮');
      }
    } catch (error) {
      console.log(`❌ 交互测试失败: ${error.message}`);
    }
    
    // 5. 截图保存
    try {
      await page.screenshot({ path: 'formula-config-page.png', fullPage: true });
      console.log('📸 已保存页面截图: formula-config-page.png');
    } catch (error) {
      console.log(`❌ 截图失败: ${error.message}`);
    }
    
    // 6. 生成报告
    console.log('\n' + '='.repeat(60));
    console.log('🎯 公式配置页面测试报告');
    console.log('='.repeat(60));
    console.log(`📍 测试URL: ${currentUrl}`);
    console.log(`📄 页面标题: ${title}`);
    console.log(`📝 页面内容: ${hasContent ? '正常加载' : '加载不完整'}`);
    console.log('='.repeat(60));
    
    console.log('\n🎉 测试完成！');
    console.log('🔍 浏览器保持打开状态，请手动检查页面功能...');
    console.log('💡 手动测试建议：');
    console.log('   1. 检查页面是否完全加载');
    console.log('   2. 尝试添加客户数据');
    console.log('   3. 输入测试数据验证计算');
    console.log('   4. 检查公式编辑功能');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 快速计算测试
async function testQuickCalculation(page, inputs) {
  console.log('\n🧮 快速计算测试...');
  
  try {
    // 输入简单的测试数据
    const testData = [100, 2, 50, 1];
    
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      await inputs[i].fill(testData[i].toString());
      await page.waitForTimeout(500);
      console.log(`   📝 输入data${i+1}: ${testData[i]}`);
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    await page.waitForTimeout(3000);
    
    // 检查结果
    const results = await page.$$('strong');
    console.log(`   📊 找到 ${results.length} 个结果元素`);
    
    if (results.length > 0) {
      console.log('   📈 计算结果:');
      for (let i = 0; i < Math.min(5, results.length); i++) {
        try {
          const text = await results[i].textContent();
          const num = parseFloat(text);
          if (!isNaN(num)) {
            console.log(`      结果${i+1}: ${num}`);
            
            // 检查是否是预期的公式1.1结果: (100+2+1)+50 = 153
            if (num === 153) {
              console.log(`      ✅ 公式1.1验证成功: ${num}`);
            }
            // 检查是否是预期的公式2.1结果: 100*2 = 200
            if (num === 200) {
              console.log(`      ✅ 公式2.1验证成功: ${num}`);
            }
          }
        } catch (e) {}
      }
    } else {
      console.log('   ❌ 未找到计算结果');
    }
    
  } catch (error) {
    console.log(`   ❌ 快速计算测试失败: ${error.message}`);
  }
}

// 执行测试
testFormulaPatient().catch(console.error);
