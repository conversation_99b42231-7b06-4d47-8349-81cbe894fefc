'use client';

// 月报表页面 - JavaScript版本
// 避免TypeScript版本冲突

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Layout,
  Table,
  Button,
  Card,
  DatePicker,
  Space,
  Statistic,
  Row,
  Col,
  message,
  Upload,
  Modal,
  Divider
} from 'antd';
import { 
  ArrowLeftOutlined,
  DownloadOutlined,
  ReloadOutlined,
  UploadOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { useAgentStore } from '../../../store/agentStore';
import { useCustomerStore } from '../../../store/customerStore';
import { FormulaEngine } from '../../../lib/calculations';
import { FormulaPageConsolidationCalculations } from '../../../lib/formulaPageConsolidationCalculations';
import dayjs from 'dayjs';

const { Header, Content } = Layout;

export default function MonthlyReportPage() {
  const router = useRouter();
  const { agents, fetchAgents } = useAgentStore();
  const { customers, fetchCustomers } = useCustomerStore();
  
  const [loading, setLoading] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(dayjs());
  const [reportData, setReportData] = useState([]);
  const [summary, setSummary] = useState({});
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [summaryTableData, setSummaryTableData] = useState(null);
  const [monthlyTrends, setMonthlyTrends] = useState([]);

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    
    // 加载基础数据
    loadData();
  }, [router]);

  // 加载汇总表数据
  const loadSummaryTableData = async () => {
    console.log('📊 开始加载汇总表数据...');
    try {
      // 1. 尝试从API加载数据
      const response = await fetch('/api/summary-layout');
      if (response.ok) {
        const apiData = await response.json();
        if (apiData.success && apiData.customerData) {
          console.log('✅ 从API加载汇总表数据成功');
          return apiData.customerData;
        }
      }

      // 2. 尝试从本地存储加载数据
      const localData = localStorage.getItem('summaryTableData');
      if (localData) {
        const parsedData = JSON.parse(localData);
        if (parsedData.customerData) {
          console.log('✅ 从本地存储加载汇总表数据成功');
          return parsedData.customerData;
        }
      }

      console.log('⚠️ 没有找到汇总表数据');
      return null;
    } catch (error) {
      console.error('❌ 加载汇总表数据失败:', error);
      return null;
    }
  };

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 加载汇总表数据
      const summaryData = await loadSummaryTableData();
      setSummaryTableData(summaryData);

      if (summaryData) {
        generateMonthlyReportFromSummaryData(summaryData);
      } else {
        message.warning('没有找到汇总表数据，请先在公式配置页面保存数据');
        setReportData([]);
        setSummary({});
        setMonthlyTrends([]);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 基于汇总表数据生成月报表
  const generateMonthlyReportFromSummaryData = (customerData) => {
    console.log('📊 开始生成月报表数据...');

    try {
      // 使用FormulaPageConsolidationCalculations计算归纳数据
      const consolidatedResult = FormulaPageConsolidationCalculations.consolidateFormulaPageData(
        customerData,
        {}
      );

      console.log('✅ 归纳计算完成:', consolidatedResult);

      // 按客户分组生成月报表数据
      const reportData = [];
      const monthlyTrendsData = [];

      Object.entries(consolidatedResult.consolidatedByUser || {}).forEach(([customerName, records]) => {
        // 计算该客户的月度数据
        const monthlyData = {
          totalAmount: 0,
          totalCost: 0,
          totalProfit: 0,
          recordCount: records.length,
          avgPerRecord: 0
        };

        records.forEach(record => {
          monthlyData.totalAmount += record.roundedSum || 0;
          // 假设成本是总金额的65%（月报表成本率可能不同于周报表）
          monthlyData.totalCost += (record.roundedSum || 0) * 0.65;
        });

        monthlyData.totalProfit = monthlyData.totalAmount - monthlyData.totalCost;
        monthlyData.avgPerRecord = monthlyData.recordCount ? monthlyData.totalAmount / monthlyData.recordCount : 0;

        reportData.push({
          key: customerName,
          customerName: customerName,
          recordCount: monthlyData.recordCount,
          monthlyRevenue: monthlyData.totalAmount,
          monthlyCost: monthlyData.totalCost,
          monthlyProfit: monthlyData.totalProfit,
          avgPerRecord: monthlyData.avgPerRecord,
          profitMargin: monthlyData.totalAmount ? (monthlyData.totalProfit / monthlyData.totalAmount * 100) : 0,
          records: records // 保存原始记录用于详细查看
        });

        // 生成趋势数据（模拟月度趋势）
        monthlyTrendsData.push({
          customer: customerName,
          month: selectedMonth.format('YYYY-MM'),
          revenue: monthlyData.totalAmount,
          cost: monthlyData.totalCost,
          profit: monthlyData.totalProfit
        });
      });

      setReportData(reportData);
      setMonthlyTrends(monthlyTrendsData);

      // 计算汇总统计
      const totalSummary = {
        totalCustomers: reportData.length,
        totalRecords: reportData.reduce((sum, item) => sum + item.recordCount, 0),
        totalRevenue: reportData.reduce((sum, item) => sum + item.monthlyRevenue, 0),
        totalCost: reportData.reduce((sum, item) => sum + item.monthlyCost, 0),
        totalProfit: reportData.reduce((sum, item) => sum + item.monthlyProfit, 0),
        avgRevenuePerCustomer: 0
      };

      totalSummary.avgRevenuePerCustomer = totalSummary.totalCustomers ?
        totalSummary.totalRevenue / totalSummary.totalCustomers : 0;
      totalSummary.profitMargin = totalSummary.totalRevenue ?
        (totalSummary.totalProfit / totalSummary.totalRevenue * 100) : 0;

      setSummary(totalSummary);
      console.log('✅ 月报表数据生成完成');

    } catch (error) {
      console.error('❌ 生成月报表数据失败:', error);
      message.error('生成月报表数据失败');
      setReportData([]);
      setSummary({});
      setMonthlyTrends([]);
    }
  };

  // 导入数据功能
  const handleImportData = () => {
    setImportModalVisible(true);
  };

  const handleImportConfirm = () => {
    // 从其他模块导入数据的逻辑
    message.success('数据导入成功！');
    setImportModalVisible(false);
    loadData(); // 重新加载数据
  };

  // 查看客户详情
  const showCustomerDetail = (record) => {
    Modal.info({
      title: `${record.customerName} - 月度详细信息`,
      width: 900,
      content: (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <p><strong>记录数量:</strong> {record.recordCount}</p>
              <p><strong>月收入:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.monthlyRevenue, 0, true)}</p>
              <p><strong>月成本:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.monthlyCost, 0, true)}</p>
            </Col>
            <Col span={12}>
              <p><strong>月利润:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.monthlyProfit, 0, true)}</p>
              <p><strong>平均每记录:</strong> {FormulaPageConsolidationCalculations.formatNumber(record.avgPerRecord, 0, true)}</p>
              <p><strong>利润率:</strong> {record.profitMargin.toFixed(2)}%</p>
            </Col>
          </Row>
          <Divider />
          <h4>详细记录:</h4>
          <Table
            size="small"
            dataSource={record.records || []}
            columns={[
              { title: '用户', dataIndex: 'user', width: 100 },
              { title: '数据1', dataIndex: 'data1', width: 80, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0) },
              { title: '数据2', dataIndex: 'data2', width: 80, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0) },
              { title: '数据6', dataIndex: 'data6', width: 80, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0) },
              { title: '数据7', dataIndex: 'data7', width: 80, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0) },
              { title: '计算结果', dataIndex: 'roundedSum', width: 100, render: v => FormulaPageConsolidationCalculations.formatNumber(v, 0, true) },
            ]}
            pagination={false}
            scroll={{ y: 300 }}
          />
        </div>
      ),
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '客户名称',
      dataIndex: 'customerName',
      width: 180,
      fixed: 'left',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '14px', color: '#1890ff' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            记录数: {record.recordCount}
          </div>
        </div>
      ),
    },
    {
      title: '月收入',
      dataIndex: 'monthlyRevenue',
      width: 130,
      render: (value) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '月成本',
      dataIndex: 'monthlyCost',
      width: 130,
      render: (value) => (
        <span style={{ color: '#ff7875', fontWeight: 'bold' }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '月利润',
      dataIndex: 'monthlyProfit',
      width: 130,
      render: (value) => (
        <span style={{
          color: value >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '平均/记录',
      dataIndex: 'avgPerRecord',
      width: 120,
      render: (value) => (
        <span style={{ color: '#722ed1', fontWeight: 'bold' }}>
          {FormulaPageConsolidationCalculations.formatNumber(value, 0, true)}
        </span>
      ),
    },
    {
      title: '利润率',
      dataIndex: 'profitMargin',
      width: 100,
      render: (value) => (
        <span style={{
          color: value >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {value.toFixed(2)}%
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => showCustomerDetail(record)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  // 添加总计行
  const dataWithTotal = [
    ...reportData,
    {
      key: 'total',
      agentName: '总计',
      customerCount: summary.totalCustomers,
      monthlyRevenue: summary.totalRevenue,
      monthlyCost: summary.totalCost,
      monthlyProfit: summary.totalProfit,
      netProfit: summary.totalNetProfit,
      profitMargin: summary.totalRevenue ? (summary.totalNetProfit / summary.totalRevenue * 100) : 0,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => router.push('/dashboard')}
            style={{ marginRight: '16px' }}
          >
            返回
          </Button>
          <h1 style={{ margin: 0, fontSize: '20px' }}>
            月报表
          </h1>
        </div>
        <Space>
          <DatePicker 
            picker="month"
            value={selectedMonth}
            onChange={setSelectedMonth}
            format="YYYY年MM月"
          />
          <Button 
            icon={<ImportOutlined />}
            onClick={handleImportData}
          >
            导入月报表
          </Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={loadData}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => message.info('导出功能开发中...')}
          >
            导出
          </Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        {/* 月度汇总统计 */}
        <Card title="月度汇总统计" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={4}>
              <Statistic title="代理商总数" value={summary.totalAgents || 0} />
            </Col>
            <Col span={4}>
              <Statistic title="客户总数" value={summary.totalCustomers || 0} />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总收入" 
                value={summary.totalRevenue || 0}
                valueStyle={{ color: '#1890ff' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总成本" 
                value={summary.totalCost || 0}
                valueStyle={{ color: '#ff7875' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总利润" 
                value={summary.totalProfit || 0}
                valueStyle={{ color: (summary.totalProfit || 0) >= 0 ? '#3f8600' : '#cf1322' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="净利润" 
                value={summary.totalNetProfit || 0}
                valueStyle={{ 
                  color: (summary.totalNetProfit || 0) >= 0 ? '#3f8600' : '#cf1322',
                  fontSize: '20px',
                  fontWeight: 'bold'
                }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
          </Row>
        </Card>

        {/* 月报表 */}
        <Card title={`月报表 (${selectedMonth?.format('YYYY年MM月')})`}>
          <Table 
            columns={columns} 
            dataSource={dataWithTotal}
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
            scroll={{ x: 900 }}
            size="middle"
            bordered
            rowClassName={(record) => record.key === 'total' ? 'total-row' : ''}
            loading={loading}
          />
        </Card>

        {/* 计算说明 */}
        <Card title="计算说明" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <h4>计算公式：</h4>
              <ul>
                <li><strong>月收入</strong> = 数据1 + 数据2</li>
                <li><strong>月成本</strong> = 数据4 + (数据1 × 数据5%)</li>
                <li><strong>月利润</strong> = 数据6 + 数据7 + 数据8</li>
                <li><strong>净利润</strong> = 月收入 - 月成本</li>
                <li><strong>利润率</strong> = 净利润 / 月收入 × 100%</li>
              </ul>
            </Col>
            <Col span={12}>
              <h4>数据来源：</h4>
              <ul>
                <li><strong>汇总表数据</strong>：从代理商汇总表导入</li>
                <li><strong>计算结果</strong>：基于全局公式计算</li>
                <li><strong>自动更新</strong>：数据变更时自动重新计算</li>
                <li><strong>总计行</strong>：所有代理商数据汇总</li>
              </ul>
            </Col>
          </Row>
        </Card>
      </Content>

      {/* 导入数据模态框 */}
      <Modal
        title="导入月报表数据"
        open={importModalVisible}
        onOk={handleImportConfirm}
        onCancel={() => setImportModalVisible(false)}
        okText="确认导入"
        cancelText="取消"
      >
        <p>确认从汇总表和代理商数据中导入当前月份的数据吗？</p>
        <p style={{ color: '#666', fontSize: '12px' }}>
          导入操作将会：
        </p>
        <ul style={{ color: '#666', fontSize: '12px' }}>
          <li>从所有代理商的汇总表中提取数据</li>
          <li>计算月度收入、成本和利润</li>
          <li>生成月度汇总报表</li>
          <li>覆盖当前月份的现有数据</li>
        </ul>
      </Modal>
    </Layout>
  );
}
