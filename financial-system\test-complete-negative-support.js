// 完整的负数支持测试
// 测试数据输入和公式表达式的负数支持

console.log('🚀 完整负数支持功能测试\n');

// 1. 数据输入验证函数测试
function validateNumberInput(value, field = 'default') {
  const cleanValue = value.replace(/[^0-9.-]/g, '');
  let processedValue = cleanValue;
  const hasNegative = cleanValue.includes('-');
  if (hasNegative) {
    const withoutNegative = cleanValue.replace(/-/g, '');
    processedValue = cleanValue.startsWith('-') ? '-' + withoutNegative : withoutNegative;
  }

  const parts = processedValue.split('.');
  if (parts.length > 2) {
    const mergedDecimal = parts.slice(1).join('');
    processedValue = parts[0] + '.' + mergedDecimal;
  }

  let maxDecimalPlaces = 2;
  if (field === 'data3') {
    maxDecimalPlaces = 3;
  } else if (field === 'data4') {
    maxDecimalPlaces = 8;
  }

  const finalParts = processedValue.split('.');
  if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
    processedValue = finalParts[0] + '.' + finalParts[1].substring(0, maxDecimalPlaces);
  }

  if (processedValue.startsWith('.')) {
    processedValue = '0' + processedValue;
  } else if (processedValue.startsWith('-.')) {
    processedValue = '-0' + processedValue.substring(1);
  }

  return processedValue;
}

// 2. 公式表达式验证函数测试
function validateExpression(expr) {
  if (!expr.trim()) return true;
  
  try {
    const validPattern = /^[data\d+\-*/^().\s]+$/;
    if (!validPattern.test(expr)) {
      return false;
    }
    const hasValidContent = /data\d|[\d.-]+/.test(expr);
    return hasValidContent;
  } catch (error) {
    return false;
  }
}

// 测试数据输入负数支持
function testDataInputNegativeSupport() {
  console.log('📊 测试数据输入负数支持...\n');
  
  const inputTests = [
    { input: '-123.45', field: 'data1', expected: '-123.45', description: '数据1负数基本测试' },
    { input: '-123.456', field: 'data3', expected: '-123.456', description: '数据3负数3位小数' },
    { input: '-123.4567', field: 'data3', expected: '-123.456', description: '数据3负数小数截断' },
    { input: '-123.12345678', field: 'data4', expected: '-123.12345678', description: '数据4负数8位小数' },
    { input: '-123.123456789', field: 'data4', expected: '-123.12345678', description: '数据4负数小数截断' },
    { input: '-.123', field: 'data3', expected: '-0.123', description: '负数小数点开头' },
    { input: '--123.45', field: 'data1', expected: '-123.45', description: '多负号处理' },
    { input: 'abc-123.45def', field: 'data1', expected: '-123.45', description: '包含字母的负数' },
  ];

  let passed = 0;
  inputTests.forEach((test, index) => {
    const result = validateNumberInput(test.input, test.field);
    const success = result === test.expected;
    console.log(`${index + 1}. ${test.description}: ${success ? '✅' : '❌'} (${test.input} → ${result})`);
    if (success) passed++;
  });

  console.log(`\n数据输入测试结果: ${passed}/${inputTests.length} 通过\n`);
  return passed === inputTests.length;
}

// 测试公式表达式负数支持
function testFormulaExpressionNegativeSupport() {
  console.log('🧮 测试公式表达式负数支持...\n');
  
  const formulaTests = [
    { input: 'data1+(-123)', expected: true, description: '变量加负数' },
    { input: '-data1+data2', expected: true, description: '负变量加变量' },
    { input: 'data1*(-123.45)', expected: true, description: '变量乘负小数' },
    { input: '(data1+data2)*(-1)', expected: true, description: '复杂表达式乘负数' },
    { input: '(-data1+data2-data3)*data4', expected: true, description: '多负数复杂表达式' },
    { input: 'data1/(-2.5)', expected: true, description: '变量除负数' },
    { input: '-123.45', expected: true, description: '纯负数常量' },
    { input: 'data1+-123', expected: true, description: '加号后跟负数' },
  ];

  let passed = 0;
  formulaTests.forEach((test, index) => {
    const result = validateExpression(test.input);
    const success = result === test.expected;
    console.log(`${index + 1}. ${test.description}: ${success ? '✅' : '❌'} (${test.input} → ${result ? '有效' : '无效'})`);
    if (success) passed++;
  });

  console.log(`\n公式表达式测试结果: ${passed}/${formulaTests.length} 通过\n`);
  return passed === formulaTests.length;
}

// 综合场景测试
function testIntegratedScenarios() {
  console.log('🔄 综合场景测试...\n');
  
  const scenarios = [
    {
      description: '客户输入负数数据并使用负数公式',
      dataInputs: [
        { field: 'data1', value: '-1000.50', expected: '-1000.50' },
        { field: 'data2', value: '-500.25', expected: '-500.25' },
        { field: 'data3', value: '-0.123', expected: '-0.123' },
      ],
      formula: '(data1+data2)*data3+(-100)',
      formulaValid: true
    },
    {
      description: '混合正负数据和复杂公式',
      dataInputs: [
        { field: 'data1', value: '1000.00', expected: '1000.00' },
        { field: 'data2', value: '-500.00', expected: '-500.00' },
        { field: 'data4', value: '-0.12345678', expected: '-0.12345678' },
      ],
      formula: '(-data1+data2)/data4*(-1)',
      formulaValid: true
    }
  ];

  let passedScenarios = 0;
  scenarios.forEach((scenario, index) => {
    console.log(`场景 ${index + 1}: ${scenario.description}`);
    
    let scenarioPassed = true;
    
    // 测试数据输入
    scenario.dataInputs.forEach(input => {
      const result = validateNumberInput(input.value, input.field);
      const inputValid = result === input.expected;
      console.log(`  数据输入 ${input.field}: ${inputValid ? '✅' : '❌'} (${input.value} → ${result})`);
      if (!inputValid) scenarioPassed = false;
    });
    
    // 测试公式验证
    const formulaResult = validateExpression(scenario.formula);
    const formulaValid = formulaResult === scenario.formulaValid;
    console.log(`  公式验证: ${formulaValid ? '✅' : '❌'} (${scenario.formula} → ${formulaResult ? '有效' : '无效'})`);
    if (!formulaValid) scenarioPassed = false;
    
    console.log(`  场景结果: ${scenarioPassed ? '✅ 通过' : '❌ 失败'}\n`);
    if (scenarioPassed) passedScenarios++;
  });

  console.log(`综合场景测试结果: ${passedScenarios}/${scenarios.length} 通过\n`);
  return passedScenarios === scenarios.length;
}

// 运行所有测试
function runAllTests() {
  console.log('🎯 开始运行完整负数支持测试...\n');
  
  const dataInputResult = testDataInputNegativeSupport();
  const formulaExpressionResult = testFormulaExpressionNegativeSupport();
  const integratedResult = testIntegratedScenarios();
  
  console.log('📋 测试总结:');
  console.log(`✅ 数据输入负数支持: ${dataInputResult ? '通过' : '失败'}`);
  console.log(`✅ 公式表达式负数支持: ${formulaExpressionResult ? '通过' : '失败'}`);
  console.log(`✅ 综合场景测试: ${integratedResult ? '通过' : '失败'}`);
  
  const allPassed = dataInputResult && formulaExpressionResult && integratedResult;
  console.log(`\n🎊 总体结果: ${allPassed ? '所有测试通过！' : '部分测试失败！'}`);
  
  if (allPassed) {
    console.log('\n🎉 恭喜！完整的负数支持功能已经实现并测试通过！');
    console.log('\n📝 功能特性:');
    console.log('✅ 所有数据字段支持负数输入');
    console.log('✅ 数据3支持3位小数的负数');
    console.log('✅ 数据4支持8位小数的负数');
    console.log('✅ 公式表达式支持负数和负变量');
    console.log('✅ 智能负号位置处理');
    console.log('✅ 完整的输入验证和错误处理');
    
    console.log('\n🔧 请在浏览器中验证:');
    console.log('1. 访问 http://localhost:3000/formula-config');
    console.log('2. 在数据字段中输入负数（如 -123.45）');
    console.log('3. 编辑公式表达式，添加负数（如 data1+(-100)）');
    console.log('4. 验证数据保存和计算功能');
  }
  
  return allPassed;
}

// 执行测试
runAllTests();
