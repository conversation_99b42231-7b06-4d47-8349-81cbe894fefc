// 财务管理系统数据库模型
// JavaScript版本 - 避免TypeScript版本冲突

generator client {
  provider = "prisma-client-js"
  output   = "../lib/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表 (简化版)
model User {
  id        Int      @id @default(autoincrement())
  username  String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 代理商表
model Agent {
  id         Int        @id @default(autoincrement())
  name       String
  clientName String?
  userName   String?
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  customers  Customer[]
  periods    Period[]
}

// 客户表
model Customer {
  id        Int      @id @default(autoincrement())
  name      String
  userName  String
  agentId   Int
  agent     Agent    @relation(fields: [agentId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 8种数据字段 (JavaScript数字类型)
  data1 Float? // 千万整数 (使用Float避免BigInt兼容问题)
  data2 Float? // 十亿整数
  data3 Float? // 百分比 (存储为小数)
  data4 Float? // 高精度数字
  data5 Float? // 百分比 (存储为小数)
  data6 Float? // 计算结果 (只读)
  data7 Float? // 计算结果 (只读)
  data8 Float? // 计算结果 (只读)

  financialData FinancialData[]
}

// 周期管理表
model Period {
  id         Int      @id @default(autoincrement())
  agentId    Int
  agent      Agent    @relation(fields: [agentId], references: [id])
  periodType String // "1-3期" 或 "4-7期"
  startDate  DateTime
  endDate    DateTime
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

// 财务数据表
model FinancialData {
  id         Int      @id @default(autoincrement())
  customerId Int
  customer   Customer @relation(fields: [customerId], references: [id])

  // 公式相关 (JSON字符串存储)
  formulas String? // JSON格式存储公式配置
  results  String? // JSON格式存储计算结果

  // 汇总数据
  totalSum   Float?
  roundedSum Int?

  // 右侧统计区域
  quantity        Int?
  price           Float?
  income          Float?
  expense         Float?
  cashFlow        Float?
  lastWeekBalance Float?
  reduction       Float?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// 全局公式配置表
model GlobalFormula {
  id          Int      @id @default(autoincrement())
  name        String   @unique // 如: "formula1_1", "formula1_2"
  expression  String // 公式表达式
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
