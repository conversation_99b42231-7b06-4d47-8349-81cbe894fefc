// 7个数据模块的计算引擎
class ModuleCalculationEngine {
  
  /**
   * 模块1.1 - 基础财务数据模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule1(data) {
    const results = {};
    
    try {
      const {
        data1_1 = 0, // 总收入
        data1_2 = 0, // 总支出
        data1_3 = 0, // 利润率
        data1_4 = 0, // 精确金额
        data1_5 = 0, // 增长率
        data1_6 = 0, // 投资额
        data1_7 = 0  // 税率
      } = data;
      
      // 计算结果1: 净利润 = 总收入 - 总支出
      results.result1_1 = Number((data1_1 - data1_2).toFixed(8));
      
      // 计算结果2: 投资回报率 = (净利润 / 投资额) * 100
      results.result1_2 = data1_6 !== 0 ? Number(((results.result1_1 / data1_6) * 100).toFixed(8)) : 0;
      
      // 计算结果3: 税后利润 = 净利润 * (1 - 税率/100)
      results.result1_3 = Number((results.result1_1 * (1 - data1_7 / 100)).toFixed(8));
      
      // 计算结果4: 利润增长额 = 净利润 * 增长率 / 100
      results.result1_4 = Number((results.result1_1 * data1_5 / 100).toFixed(8));
      
      // 计算结果5: 综合评分 = (利润率 + 投资回报率 + 增长率) / 3
      results.result1_5 = Number(((data1_3 + results.result1_2 + data1_5) / 3).toFixed(8));
      
    } catch (error) {
      console.error('模块1.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 模块2.1 - 成本分析模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule2(data) {
    const results = {};
    
    try {
      const {
        data2_1 = 0, // 直接成本
        data2_2 = 0, // 间接成本
        data2_3 = 0, // 成本占比
        data2_4 = 0, // 单位成本
        data2_5 = 0, // 成本增长率
        data2_6 = 0, // 固定成本
        data2_7 = 0  // 变动成本
      } = data;
      
      // 计算结果1: 总成本 = 直接成本 + 间接成本
      results.result2_1 = Number((data2_1 + data2_2).toFixed(8));
      
      // 计算结果2: 成本效率 = 固定成本 / 总成本 * 100
      results.result2_2 = results.result2_1 !== 0 ? Number((data2_6 / results.result2_1 * 100).toFixed(8)) : 0;
      
      // 计算结果3: 成本节约额 = 总成本 * (1 - 成本增长率/100)
      results.result2_3 = Number((results.result2_1 * (1 - data2_5 / 100)).toFixed(8));
      
      // 计算结果4: 单位利润 = (收入假设 - 单位成本) 
      // 这里假设收入为总成本的120%
      const assumedRevenue = results.result2_1 * 1.2;
      results.result2_4 = Number((assumedRevenue - data2_4).toFixed(8));
      
      // 计算结果5: 成本控制指数 = (固定成本 + 变动成本) / 总成本 * 100
      results.result2_5 = results.result2_1 !== 0 ? Number(((data2_6 + data2_7) / results.result2_1 * 100).toFixed(8)) : 0;
      
    } catch (error) {
      console.error('模块2.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 模块3.1 - 简化报表模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule3(data) {
    const results = {};
    
    try {
      const {
        data3_1 = 0, // 营业收入
        data3_2 = 0, // 营业成本
        data3_3 = 0, // 毛利率
        data3_4 = 0  // 关键指标
      } = data;
      
      // 计算结果1: 综合评估分数 = (毛利率 * 0.4) + (关键指标 * 0.6)
      results.result3_1 = Number((data3_3 * 0.4 + data3_4 * 0.6).toFixed(8));
      
    } catch (error) {
      console.error('模块3.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 模块4.1 - 投资回报模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule4(data) {
    const results = {};
    
    try {
      const {
        data4_1 = 0, // 初始投资
        data4_2 = 0, // 累计投资
        data4_3 = 0, // 预期收益率
        data4_4 = 0, // 实际收益
        data4_5 = 0, // 风险系数
        data4_6 = 0, // 回收金额
        data4_7 = 0  // 投资周期(月)
      } = data;
      
      // 计算结果1: ROI投资回报率 = (实际收益 - 初始投资) / 初始投资 * 100
      results.result4_1 = data4_1 !== 0 ? Number(((data4_4 - data4_1) / data4_1 * 100).toFixed(8)) : 0;
      
      // 计算结果2: NPV净现值 = 实际收益 - 累计投资
      results.result4_2 = Number((data4_4 - data4_2).toFixed(8));
      
      // 计算结果3: IRR内部收益率 = 预期收益率 * (实际收益/累计投资)
      results.result4_3 = data4_2 !== 0 ? Number((data4_3 * (data4_4 / data4_2)).toFixed(8)) : 0;
      
      // 计算结果4: 回收期 = 初始投资 / (实际收益 / 投资周期)
      const monthlyReturn = data4_7 !== 0 ? data4_4 / data4_7 : 0;
      results.result4_4 = monthlyReturn !== 0 ? Number((data4_1 / monthlyReturn).toFixed(8)) : 0;
      
      // 计算结果5: 风险调整收益 = 实际收益 * (1 - 风险系数/100)
      results.result4_5 = Number((data4_4 * (1 - data4_5 / 100)).toFixed(8));
      
    } catch (error) {
      console.error('模块4.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 模块5.1 - 风险评估模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule5(data) {
    const results = {};
    
    try {
      const {
        data5_1 = 0, // 风险敞口
        data5_2 = 0, // 保险金额
        data5_3 = 0, // 风险概率
        data5_4 = 0, // 损失预期
        data5_5 = 0, // 风险容忍度
        data5_6 = 0, // 风险准备金
        data5_7 = 0  // 风险缓解率
      } = data;
      
      // 计算结果1: 风险价值VaR = 风险敞口 * 风险概率 / 100
      results.result5_1 = Number((data5_1 * data5_3 / 100).toFixed(8));
      
      // 计算结果2: 风险调整资本 = 风险敞口 - 保险金额 - 风险准备金
      results.result5_2 = Number((data5_1 - data5_2 - data5_6).toFixed(8));
      
      // 计算结果3: 预期损失 = 损失预期 * (1 - 风险缓解率/100)
      results.result5_3 = Number((data5_4 * (1 - data5_7 / 100)).toFixed(8));
      
      // 计算结果4: 风险收益比 = 预期损失 / 风险敞口 * 100
      results.result5_4 = data5_1 !== 0 ? Number((results.result5_3 / data5_1 * 100).toFixed(8)) : 0;
      
      // 计算结果5: 综合风险评级 = (风险概率 + 风险收益比 - 风险容忍度) / 3
      results.result5_5 = Number(((data5_3 + results.result5_4 - data5_5) / 3).toFixed(8));
      
    } catch (error) {
      console.error('模块5.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 模块6.1 - 现金流模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule6(data) {
    const results = {};
    
    try {
      const {
        data6_1 = 0, // 经营现金流
        data6_2 = 0, // 投资现金流
        data6_3 = 0, // 筹资现金流
        data6_4 = 0, // 现金周转率
        data6_5 = 0, // 现金比率
        data6_6 = 0, // 期初现金
        data6_7 = 0  // 最低现金需求
      } = data;
      
      // 计算结果1: 净现金流 = 经营现金流 + 投资现金流 + 筹资现金流
      results.result6_1 = Number((data6_1 + data6_2 + data6_3).toFixed(8));
      
      // 计算结果2: 现金流缺口 = 最低现金需求 - (期初现金 + 净现金流)
      results.result6_2 = Number((data6_7 - (data6_6 + results.result6_1)).toFixed(8));
      
      // 计算结果3: 现金周转天数 = 365 / 现金周转率
      results.result6_3 = data6_4 !== 0 ? Number((365 / data6_4).toFixed(8)) : 0;
      
      // 计算结果4: 现金流健康度 = (净现金流 / 最低现金需求) * 100
      results.result6_4 = data6_7 !== 0 ? Number((results.result6_1 / data6_7 * 100).toFixed(8)) : 0;
      
    } catch (error) {
      console.error('模块6.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 模块7.1 - 预算对比模块计算
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculateModule7(data) {
    const results = {};
    
    try {
      const {
        data7_1 = 0, // 预算收入
        data7_2 = 0, // 实际收入
        data7_3 = 0, // 预算支出
        data7_4 = 0, // 实际支出
        data7_5 = 0, // 预算执行率
        data7_6 = 0, // 差异分析
        data7_7 = 0  // 调整幅度
      } = data;
      
      // 计算结果1: 收入差异 = 实际收入 - 预算收入
      results.result7_1 = Number((data7_2 - data7_1).toFixed(8));
      
      // 计算结果2: 支出差异 = 实际支出 - 预算支出
      results.result7_2 = Number((data7_4 - data7_3).toFixed(8));
      
      // 计算结果3: 预算偏差率 = ((实际收入 - 预算收入) / 预算收入) * 100
      results.result7_3 = data7_1 !== 0 ? Number(((data7_2 - data7_1) / data7_1 * 100).toFixed(8)) : 0;
      
      // 计算结果4: 执行效率指数 = (预算执行率 + 调整幅度) / 2
      results.result7_4 = Number(((data7_5 + data7_7) / 2).toFixed(8));
      
    } catch (error) {
      console.error('模块7.1计算错误:', error);
    }
    
    return results;
  }
  
  /**
   * 统一计算接口
   * @param {number} moduleNumber - 模块编号 (1-7)
   * @param {Object} data - 输入数据
   * @returns {Object} 计算结果
   */
  static calculate(moduleNumber, data) {
    switch (moduleNumber) {
      case 1: return this.calculateModule1(data);
      case 2: return this.calculateModule2(data);
      case 3: return this.calculateModule3(data);
      case 4: return this.calculateModule4(data);
      case 5: return this.calculateModule5(data);
      case 6: return this.calculateModule6(data);
      case 7: return this.calculateModule7(data);
      default: 
        console.error('无效的模块编号:', moduleNumber);
        return {};
    }
  }
}

module.exports = ModuleCalculationEngine;
