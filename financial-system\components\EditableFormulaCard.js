'use client';

import React from 'react';
import { Card, Tag, Typography, Button, Space, Tooltip } from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  PlusOutlined 
} from '@ant-design/icons';

const { Text } = Typography;

// 可编辑公式卡片组件
const EditableFormulaCard = ({ formula, onEdit, onDelete }) => {
  const hasExpression = formula.expression && formula.expression.trim() !== '';
  const isEmpty = !hasExpression;

  // 卡片样式
  const cardStyle = {
    border: `2px solid ${
      isEmpty ? '#d9d9d9' : 
      formula.isValid ? '#52c41a' : '#ff4d4f'
    }`,
    borderRadius: '8px',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    minHeight: '120px',
    background: isEmpty ? '#fafafa' : '#ffffff'
  };

  // 悬停效果
  const handleMouseEnter = (e) => {
    e.currentTarget.style.transform = 'translateY(-2px)';
    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  };

  const handleMouseLeave = (e) => {
    e.currentTarget.style.transform = 'translateY(0)';
    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
  };

  return (
    <Card
      size="small"
      hoverable
      style={cardStyle}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onEdit}
      bodyStyle={{ 
        padding: '12px',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}
    >
      {/* 公式内容 */}
      <div className="formula-content">
        {/* 公式头部 */}
        <div 
          className="formula-header" 
          style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '8px'
          }}
        >
          <Tag 
            color={isEmpty ? "default" : formula.isValid ? "gold" : "red"}
            style={{
              backgroundColor: isEmpty ? '#f0f0f0' : formula.isValid ? '#FFD700' : '#ff4d4f',
              color: isEmpty ? '#666' : '#000',
              fontWeight: 'bold',
              border: 'none'
            }}
          >
            {formula.name}
          </Tag>
          
          {/* 状态图标 */}
          {!isEmpty && (
            <Tooltip title={formula.isValid ? '公式有效' : '公式语法错误'}>
              {formula.isValid ? 
                <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} /> :
                <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
              }
            </Tooltip>
          )}
        </div>
        
        {/* 公式表达式 */}
        <div className="formula-expression" style={{ marginBottom: '12px', minHeight: '40px' }}>
          {isEmpty ? (
            <div style={{ 
              textAlign: 'center', 
              padding: '20px 0',
              color: '#999',
              border: '2px dashed #d9d9d9',
              borderRadius: '6px',
              background: '#fafafa'
            }}>
              <PlusOutlined style={{ fontSize: '20px', marginBottom: '8px' }} />
              <br />
              <Text type="secondary">点击添加公式</Text>
            </div>
          ) : (
            <div>
              <Text 
                code 
                style={{
                  backgroundColor: '#FFF8DC',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  fontFamily: 'Courier New, monospace',
                  fontSize: '12px',
                  wordBreak: 'break-all',
                  display: 'block',
                  lineHeight: '1.4'
                }}
              >
                {formula.expression}
              </Text>
              
              {/* 错误提示 */}
              {!formula.isValid && (
                <Text 
                  type="danger" 
                  style={{ 
                    fontSize: '11px', 
                    marginTop: '4px',
                    display: 'block'
                  }}
                >
                  语法错误
                </Text>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      {!isEmpty && (
        <div 
          className="formula-actions"
          style={{ 
            display: 'flex', 
            justifyContent: 'center',
            borderTop: '1px solid #f0f0f0',
            paddingTop: '8px',
            marginTop: '8px'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <Space>
            <Tooltip title="编辑公式">
              <Button 
                type="text" 
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit();
                }}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
            
            <Tooltip title="删除公式">
              <Button 
                type="text" 
                size="small"
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                style={{ color: '#ff4d4f' }}
              />
            </Tooltip>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default EditableFormulaCard;
