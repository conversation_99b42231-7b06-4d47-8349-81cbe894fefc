'use client';

// EnhancedDynamicTable 组件 - 增强版动态表格
// 集成了可编辑标签、公式编辑器和汇总表植入功能

import React, { useState, useEffect } from 'react';
import { Table, Space, Card, Divider, message, Spin, Button } from 'antd';
import { FunctionOutlined, SettingOutlined } from '@ant-design/icons';
import EditableLabel from './EditableLabel';
import FormulaEditor from './FormulaEditor';
import FixedFormulaEngine from './FixedFormulaEngine';
import PlantToSummaryButton from './PlantToSummaryButton';
import { useSyncStatusStore } from '../store/syncStatusStore';
import { MODULE_CONFIGS } from '../config/moduleConfigs';
import { FormulaEngine } from '../lib/calculations';

const EnhancedDynamicTable = ({
  agentId,                    // 代理商ID
  moduleId,                   // 模块ID
  customers = [],             // 客户数据
  onCustomersChange,          // 客户数据变更回调
  editable = true,            // 是否可编辑
  showFormulas = true,        // 是否显示公式编辑器
  showPlantButton = true      // 是否显示植入按钮
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [customNames, setCustomNames] = useState({});
  const [formulas, setFormulas] = useState({});
  const [showFormulaEditor, setShowFormulaEditor] = useState(false);
  const [fixedFormulaResults, setFixedFormulaResults] = useState({});
  const [showFixedFormulaEngine, setShowFixedFormulaEngine] = useState(false);
  
  // 同步状态
  const { syncFieldName, getSyncStats } = useSyncStatusStore();
  const syncStats = getSyncStats();
  
  // 获取模块配置
  const moduleConfig = MODULE_CONFIGS[moduleId];
  
  // 初始化自定义名称和公式
  useEffect(() => {
    if (moduleConfig) {
      loadCustomNames();
    }
  }, [moduleId]); // 移除moduleConfig依赖，避免无限循环

  // 数据同步检查机制 - 已移除以避免无限循环
  // 原有的定时器检查功能已被移除
  
  // 加载自定义名称配置
  const loadCustomNames = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/custom-names/${moduleId}`);
      const result = await response.json();
      
      if (result.success) {
        setCustomNames(result.data.customNames);
        
        // 提取公式配置
        const formulaConfig = {};
        Object.keys(result.data.customNames.formulas || {}).forEach(formulaId => {
          const formula = result.data.customNames.formulas[formulaId];
          formulaConfig[formulaId] = {
            name: formula.customName || formula.defaultName,
            expression: formula.expression
          };
        });
        setFormulas(formulaConfig);
      }
    } catch (error) {
      console.error('加载自定义名称失败:', error);
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 保存字段自定义名称
  const handleSaveFieldName = async (fieldId, newName) => {
    try {
      const response = await fetch(`/api/custom-names/${moduleId}/${fieldId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ customName: newName })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // 更新本地状态
        setCustomNames(prev => ({
          ...prev,
          dataFields: {
            ...prev.dataFields,
            [fieldId]: {
              ...prev.dataFields?.[fieldId],
              customName: newName
            }
          }
        }));
        
        return true;
      } else {
        message.error(result.error || '保存失败');
        return false;
      }
    } catch (error) {
      console.error('保存字段名称失败:', error);
      message.error('保存失败');
      return false;
    }
  };
  
  // 保存公式配置
  const handleSaveFormula = async (formulaData) => {
    try {
      const formulaId = Object.keys(formulas).find(id => 
        formulas[id].name === formulaData.name
      ) || 'formula1_1';
      
      const response = await fetch(`/api/custom-names/${moduleId}/${formulaId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customName: formulaData.name,
          expression: formulaData.expression
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // 更新本地状态
        setFormulas(prev => ({
          ...prev,
          [formulaId]: {
            name: formulaData.name,
            expression: formulaData.expression
          }
        }));
        
        // 重新计算所有客户的结果
        recalculateResults();
        
        return true;
      } else {
        message.error(result.error || '保存失败');
        return false;
      }
    } catch (error) {
      console.error('保存公式失败:', error);
      message.error('保存失败');
      return false;
    }
  };
  
  // 重新计算结果
  const recalculateResults = () => {
    if (!customers || customers.length === 0) return;
    
    const updatedCustomers = customers.map(customer => {
      const newCustomer = { ...customer };
      
      // 计算每个结果字段
      Object.keys(moduleConfig.resultMapping || {}).forEach(resultKey => {
        const formulaName = moduleConfig.resultMapping[resultKey];
        const formulaId = Object.keys(formulas).find(id => 
          formulas[id].name === formulaName || 
          moduleConfig.defaultFormulas[formulaName]
        );
        
        if (formulaId && formulas[formulaId]) {
          try {
            const result = FormulaEngine.calculate(formulas[formulaId].expression, customer);
            newCustomer[resultKey] = result;
          } catch (error) {
            console.warn(`计算公式 ${formulaName} 失败:`, error);
            newCustomer[resultKey] = 0;
          }
        }
      });
      
      return newCustomer;
    });
    
    onCustomersChange?.(updatedCustomers);
  };
  
  // 植入成功回调
  const handlePlantSuccess = (result) => {
    message.success(`主汇总表更新成功！客户汇总值: ${result.updatedValue?.toLocaleString()}`);
    // 可以在这里刷新数据或执行其他操作
  };

  // 固定公式计算完成回调
  const handleFixedFormulaCalculation = (results) => {
    setFixedFormulaResults(results);

    // 如果有最终汇总值，更新客户数据
    if (results.finalSummary && customers.length > 0) {
      const updatedCustomers = customers.map((customer, index) => {
        if (index === 0) { // 更新第一个客户的最终汇总值
          return {
            ...customer,
            finalSummaryValue: results.finalSummary
          };
        }
        return customer;
      });

      onCustomersChange?.(updatedCustomers);
    }
  };

  // 固定公式参数变更回调
  const handleFixedFormulaParametersChange = (formulaId, paramKey, value) => {
    console.log(`固定公式参数变更: ${formulaId}.${paramKey} = ${value}`);
    // 可以在这里保存参数配置到后端
  };
  
  // 植入失败回调
  const handlePlantError = (error) => {
    console.error('植入失败:', error);
  };
  
  // 获取字段显示名称
  const getFieldDisplayName = (fieldId) => {
    const customField = customNames.dataFields?.[fieldId];
    if (customField && customField.customName) {
      return customField.customName;
    }
    
    const defaultField = moduleConfig?.fields?.[fieldId];
    return defaultField?.title || fieldId;
  };
  
  // 处理数据变更 - 增强调试版本
  const handleDataChange = (value, record, fieldKey, index) => {
    console.log(`数据变更触发: fieldKey=${fieldKey}, value=${value}, index=${index}`);
    console.log('当前customers状态:', customers);

    const newCustomers = [...customers];
    const processedValue = fieldKey.startsWith('data') ? (parseFloat(value) || 0) : value;

    newCustomers[index] = {
      ...newCustomers[index],
      [fieldKey]: processedValue
    };

    console.log(`数据变更后: ${fieldKey} = ${processedValue}`);
    console.log('更新后的customer:', newCustomers[index]);
    
    // 如果是数据字段变更，重新计算结果
    if (fieldKey.startsWith('data')) {
      // 计算结果字段
      Object.keys(moduleConfig.resultMapping || {}).forEach(resultKey => {
        const formulaName = moduleConfig.resultMapping[resultKey];
        const formulaId = Object.keys(formulas).find(id => 
          formulas[id].name === formulaName
        );
        
        if (formulaId && formulas[formulaId]) {
          try {
            const result = FormulaEngine.calculate(formulas[formulaId].expression, newCustomers[index]);
            newCustomers[index][resultKey] = result;
          } catch (error) {
            console.warn(`计算公式 ${formulaName} 失败:`, error);
            newCustomers[index][resultKey] = 0;
          }
        }
      });
    }

    console.log('调用onCustomersChange，传递数据:', newCustomers);
    onCustomersChange?.(newCustomers);

    // 验证状态是否更新
    setTimeout(() => {
      console.log('状态更新后验证 - customers:', customers);
    }, 100);
  };
  
  // 构建表格列配置
  const buildTableColumns = () => {
    if (!moduleConfig) return [];
    
    const columns = [
      {
        title: '客户名',
        dataIndex: 'name',
        key: 'name',
        width: 120,
        fixed: 'left',
        render: (value, record, index) => (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleDataChange(e.target.value, record, 'name', index)}
            style={{
              width: '100%',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              padding: '4px 8px'
            }}
            disabled={!editable}
          />
        )
      },
      {
        title: '用户名',
        dataIndex: 'userName',
        key: 'userName',
        width: 120,
        render: (value, record, index) => (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleDataChange(e.target.value, record, 'userName', index)}
            style={{
              width: '100%',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              padding: '4px 8px'
            }}
            disabled={!editable}
          />
        )
      }
    ];
    
    // 添加数据字段列
    Object.keys(moduleConfig.fields).forEach(fieldKey => {
      if (fieldKey.startsWith('data')) {
        columns.push({
          title: (
            <EditableLabel
              value={getFieldDisplayName(fieldKey)}
              defaultValue={moduleConfig.fields[fieldKey].title}
              onSave={(newName) => handleSaveFieldName(fieldKey, newName)}
              maxLength={20}
              editable={editable}
              style={{ minWidth: '80px' }}
            />
          ),
          dataIndex: fieldKey,
          key: fieldKey,
          width: 120,
          render: (value, record, index) => (
            <input
              type="number"
              value={value || ''}
              onChange={(e) => {
                console.log(`输入字段onChange触发: ${fieldKey} = ${e.target.value}`);
                handleDataChange(e.target.value, record, fieldKey, index);
              }}
              onBlur={(e) => {
                console.log(`输入字段onBlur触发: ${fieldKey} = ${e.target.value}`);
                handleDataChange(e.target.value, record, fieldKey, index);
              }}
              onInput={(e) => {
                console.log(`输入字段onInput触发: ${fieldKey} = ${e.target.value}`);
                handleDataChange(e.target.value, record, fieldKey, index);
              }}
              style={{
                width: '100%',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                padding: '4px 8px'
              }}
              disabled={!editable}
              step={moduleConfig.fields[fieldKey].step || 1}
            />
          )
        });
      }
    });
    
    // 添加结果字段列
    Object.keys(moduleConfig.fields).forEach(fieldKey => {
      if (fieldKey.startsWith('result')) {
        columns.push({
          title: getFieldDisplayName(fieldKey),
          dataIndex: fieldKey,
          key: fieldKey,
          width: 120,
          render: (value) => (
            <span style={{ 
              color: '#52c41a', 
              fontWeight: 'bold',
              fontFamily: 'monospace'
            }}>
              {typeof value === 'number' ? value.toFixed(2) : '0.00'}
            </span>
          )
        });
      }
    });
    
    return columns;
  };
  
  // 获取可用变量列表
  const getAvailableVariables = () => {
    if (!moduleConfig) return [];
    
    return Object.keys(moduleConfig.fields)
      .filter(key => key.startsWith('data'))
      .map(key => ({
        id: key,
        name: getFieldDisplayName(key),
        type: moduleConfig.fields[key].format === 'percentage' ? 'percentage' : 'number'
      }));
  };
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large">
          <div style={{ marginTop: '20px' }}>加载配置中...</div>
        </Spin>
      </div>
    );
  }
  
  return (
    <div style={{ padding: '16px' }}>
      {/* 公式编辑器区域 */}
      {showFormulas && (
        <Card 
          title={
            <Space>
              <FunctionOutlined />
              <span>公式配置</span>
              <Button
                size="small"
                icon={<SettingOutlined />}
                onClick={() => setShowFixedFormulaEngine(!showFixedFormulaEngine)}
              >
                {showFixedFormulaEngine ? '隐藏固定公式' : '显示固定公式'}
              </Button>
              <Button
                size="small"
                icon={<SettingOutlined />}
                onClick={() => setShowFormulaEditor(!showFormulaEditor)}
              >
                {showFormulaEditor ? '隐藏' : '显示'}编辑器
              </Button>
            </Space>
          }
          style={{ marginBottom: '16px' }}
          size="small"
        >
          {/* 固定公式引擎 */}
          {showFixedFormulaEngine && moduleConfig?.fixedFormulas && (
            <div style={{ marginBottom: '16px' }}>
              <FixedFormulaEngine
                key={JSON.stringify(customers[0])} // 强制重新渲染当数据变化时
                moduleConfig={moduleConfig}
                customerData={customers[0] || {}} // 使用第一个客户的数据
                onCalculationComplete={handleFixedFormulaCalculation}
                onFormulaParametersChange={handleFixedFormulaParametersChange}
              />
              <Divider />
            </div>
          )}

          {/* 传统公式编辑器 */}
          {showFormulaEditor && (
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.keys(formulas).map(formulaId => (
                <FormulaEditor
                  key={formulaId}
                  formula={formulas[formulaId]?.expression || ''}
                  formulaName={formulas[formulaId]?.name || `公式${formulaId.replace('formula', '').replace('_', '.')}`}
                  variables={getAvailableVariables()}
                  onSave={handleSaveFormula}
                  readOnly={!editable}
                  style={{ marginBottom: '8px' }}
                />
              ))}
            </Space>
          )}
        </Card>
      )}
      
      {/* 数据表格区域 */}
      <Card 
        title={
          <Space>
            <span>{moduleConfig?.displayName || '数据表格'}</span>
            {syncStats.isSync && (
              <span style={{ color: '#1890ff', fontSize: '12px' }}>
                (同步中...)
              </span>
            )}
          </Space>
        }
        extra={
          showPlantButton && (
            <PlantToSummaryButton
              agentId={agentId}
              moduleId={moduleId}
              customerData={customers}
              onPlantSuccess={handlePlantSuccess}
              onPlantError={handlePlantError}
              disabled={!customers || customers.length === 0}
            />
          )
        }
        size="small"
      >
        <Table
          columns={buildTableColumns()}
          dataSource={customers}
          rowKey={(record, index) => `row_${index}`}
          pagination={false}
          scroll={{ x: 'max-content' }}
          size="small"
          bordered
        />
        
        {/* 同步状态信息 */}
        {syncStats.totalSynced > 0 && (
          <div style={{ 
            marginTop: '8px', 
            fontSize: '12px', 
            color: '#666',
            textAlign: 'right'
          }}>
            同步统计: 总计 {syncStats.totalSynced} 次, 
            成功率 {syncStats.successRate}%, 
            平均耗时 {syncStats.avgDuration}ms
          </div>
        )}
      </Card>
    </div>
  );
};

export default EnhancedDynamicTable;
