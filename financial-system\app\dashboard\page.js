'use client';

// 主仪表盘页面 - JavaScript版本
// 避免TypeScript版本冲突

import { useState, useEffect } from 'react';
import { 
  Layout, 
  Table, 
  Button, 
  Input, 
  DatePicker, 
  Select, 
  Space, 
  Card, 
  message,
  Popconfirm 
} from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  DeleteOutlined,
  BarChartOutlined,
  FileTextOutlined,
  LogoutOutlined,
  ImportOutlined,
  CalculatorOutlined
} from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useAgentStore } from '../../store/agentStore';

const { Header, Content } = Layout;
const { TextArea } = Input;
const { Option } = Select;

export default function DashboardPage() {
  const router = useRouter();
  const { agents, fetchAgents, addAgent, updateAgent, deleteAgent } = useAgentStore();
  
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [remarks, setRemarks] = useState('');
  const [editingKey, setEditingKey] = useState('');

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    // 加载代理商数据
    fetchAgents().catch(error => {
      console.error('加载代理商数据失败:', error);
    });
  }, []); // 移除router和fetchAgents依赖，避免无限循环

  // 退出登录
  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('username');
    message.success('已退出登录');
    router.push('/login');
  };

  // 添加新代理商
  const handleAddAgent = async () => {
    try {
      const newAgent = {
        name: '新代理商',
        clientName: '新客户',
        userName: '新用户'
      };
      await addAgent(newAgent);
      message.success('添加代理商成功！');
    } catch (error) {
      message.error('添加代理商失败！');
    }
  };

  // 删除代理商
  const handleDeleteAgent = async (id) => {
    try {
      await deleteAgent(id);
      message.success('删除代理商成功！');
    } catch (error) {
      message.error('删除代理商失败！');
    }
  };

  // 保存数据
  const handleSaveData = async () => {
    setLoading(true);
    try {
      // 这里可以添加保存逻辑
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      message.success('数据保存成功！');
    } catch (error) {
      message.error('数据保存失败！');
    } finally {
      setLoading(false);
    }
  };

  // 编辑代理商
  const isEditing = (record) => record.id === editingKey;

  const edit = (record) => {
    setEditingKey(record.id);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const save = async (id) => {
    try {
      const row = agents.find(item => item.id === id);
      await updateAgent(id, row);
      setEditingKey('');
      message.success('保存成功！');
    } catch (error) {
      message.error('保存失败！');
    }
  };

  // 处理字段更新
  const handleFieldUpdate = (id, field, value) => {
    // 这里应该更新本地状态，而不是直接调用API
    // 临时解决方案：直接更新agents数组
    const updatedAgents = agents.map(agent =>
      agent.id === id ? { ...agent, [field]: value } : agent
    );
    // 注意：这里需要使用store的方法来更新状态
    // 暂时先这样处理，后续会完善
  };

  // 表格列定义
  const columns = [
    {
      title: '代理名/客户名/用户名',
      dataIndex: 'name',
      width: 200,
      render: (text, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Input
            value={text}
            onChange={(e) => handleFieldUpdate(record.id, 'name', e.target.value)}
            placeholder="代理名"
          />
        ) : text;
      },
    },
    {
      title: '日期编辑1 (1-3期)',
      dataIndex: 'period1',
      width: 150,
      render: (_, record) => (
        <Button 
          type="link" 
          onClick={() => router.push(`/summary/${record.id}?period=1-3`)}
        >
          编辑1-3期
        </Button>
      ),
    },
    {
      title: '日期编辑2 (4-7期)',
      dataIndex: 'period2',
      width: 150,
      render: (_, record) => (
        <Button 
          type="link" 
          onClick={() => router.push(`/summary/${record.id}?period=4-7`)}
        >
          编辑4-7期
        </Button>
      ),
    },
    {
      title: '周期选择',
      dataIndex: 'periodType',
      width: 120,
      render: (text, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Select
            value={text || '1-3期'}
            onChange={(value) => handleFieldUpdate(record.id, 'periodType', value)}
            style={{ width: '100%' }}
          >
            <Option value="1-3期">1-3期</Option>
            <Option value="4-7期">4-7期</Option>
          </Select>
        ) : (text || '1-3期');
      },
    },
    {
      title: '代理商数据录入表格',
      dataIndex: 'dataEntry',
      width: 180,
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            onClick={() => router.push(`/summary/${record.id}`)}
          >
            数据录入
          </Button>
          <Button
            size="small"
            onClick={() => router.push('/modules')}
          >
            7模块管理
          </Button>
        </Space>
      ),
    },
    {
      title: '操作',
      width: 150,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Space>
            <Button size="small" onClick={() => save(record.id)}>
              保存
            </Button>
            <Button size="small" onClick={cancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Space>
            <Button size="small" onClick={() => edit(record)}>
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个代理商吗？"
              onConfirm={() => handleDeleteAgent(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
          财务管理系统
        </h1>
        <Space>
          <Button
            type="primary"
            icon={<CalculatorOutlined />}
            onClick={() => router.push('/formula-config')}
          >
            界面3 - 公式配置
          </Button>
          <Button
            type="primary"
            icon={<BarChartOutlined />}
            onClick={() => router.push('/reports/weekly')}
          >
            公司周利润表
          </Button>
          <Button
            type="primary"
            icon={<FileTextOutlined />}
            onClick={() => router.push('/reports/yearly')}
          >
            公司年利润表
          </Button>
          <Button
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            退出
          </Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <Card title="主仪表盘" style={{ marginBottom: '24px' }}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* 日期选择和备注区域 */}
            <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
              <div>
                <label style={{ marginRight: '8px' }}>选择日期:</label>
                <DatePicker 
                  value={selectedDate}
                  onChange={setSelectedDate}
                  placeholder="选择日期"
                />
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ marginRight: '8px' }}>备注 (最大30字符):</label>
                <TextArea
                  value={remarks}
                  onChange={(e) => setRemarks(e.target.value.slice(0, 30))}
                  placeholder="请输入备注信息"
                  maxLength={30}
                  rows={2}
                  showCount
                />
              </div>
            </div>

            {/* 操作按钮区域 */}
            <div style={{ display: 'flex', gap: '16px' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddAgent}
              >
                添加代理商
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading}
                onClick={handleSaveData}
              >
                保存数据
              </Button>
              <Button
                icon={<ImportOutlined />}
                onClick={() => router.push('/reports/monthly')}
              >
                导入月报表
              </Button>
            </div>

            {/* 代理商数据表格 */}
            <Table 
              columns={columns} 
              dataSource={agents}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
              scroll={{ x: 1000 }}
              size="middle"
              bordered
            />
          </Space>
        </Card>
      </Content>
    </Layout>
  );
}
