// 直接测试公式配置页面功能和计算准确性
const { chromium } = require('playwright');

async function testFormulaConfigDirect() {
  console.log('🎯 直接测试公式配置页面');
  console.log('🌐 URL: http://dlsykgzq.w1.luyouxia.net/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const page = await browser.newPage();
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log(`🔴 页面错误: ${error.message}`);
  });
  
  // 监听控制台日志
  page.on('console', msg => {
    if (msg.type() === 'error') {
      console.log(`🔴 控制台错误: ${msg.text()}`);
    }
  });
  
  try {
    // 1. 直接访问公式配置页面
    console.log('📊 步骤1: 访问公式配置页面...');
    const response = await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config/', {
      waitUntil: 'networkidle',
      timeout: 30000
    });
    
    const status = response.status();
    const currentUrl = page.url();
    console.log(`📍 响应状态: ${status}`);
    console.log(`📍 当前URL: ${currentUrl}`);
    
    if (status !== 200 || currentUrl.includes('login')) {
      console.log('❌ 页面需要登录或无法访问');
      console.log('🔄 尝试绕过登录限制...');
      
      // 尝试等待页面加载完成
      await page.waitForTimeout(5000);
      
      // 检查是否最终加载了内容
      const hasContent = await page.locator('body').textContent();
      if (hasContent.includes('公式') || hasContent.includes('配置') || hasContent.includes('计算')) {
        console.log('✅ 页面内容已加载');
      } else {
        console.log('❌ 页面内容未加载，可能需要登录');
        return;
      }
    } else {
      console.log('✅ 页面访问成功');
    }
    
    await page.waitForTimeout(3000);
    
    // 2. 分析页面结构
    console.log('\n📊 步骤2: 分析页面结构...');
    
    // 检查页面标题
    const title = await page.title();
    console.log(`📄 页面标题: "${title}"`);
    
    // 查找公式相关元素
    const formulaElements = await page.$$('.formula-card, [class*="formula"], button:has-text("公式"), text=公式');
    console.log(`📐 找到 ${formulaElements.length} 个公式相关元素`);
    
    // 查找表格
    const tables = await page.$$('table');
    console.log(`📊 找到 ${tables.length} 个表格`);
    
    // 查找输入框
    const inputs = await page.$$('input');
    console.log(`📝 找到 ${inputs.length} 个输入框`);
    
    // 查找按钮
    const buttons = await page.$$('button');
    console.log(`🔘 找到 ${buttons.length} 个按钮`);
    
    // 3. 测试公式功能
    console.log('\n📊 步骤3: 测试公式功能...');
    
    if (formulaElements.length > 0) {
      console.log('🔍 测试公式编辑功能...');
      
      // 点击第一个公式元素
      await formulaElements[0].click();
      await page.waitForTimeout(2000);
      
      // 检查是否打开了模态框或编辑界面
      const hasModal = await page.locator('.ant-modal, [role="dialog"], textarea, .modal').isVisible({ timeout: 3000 });
      console.log(`${hasModal ? '✅' : '❌'} 公式编辑界面: ${hasModal ? '打开成功' : '未打开'}`);
      
      if (hasModal) {
        // 尝试获取公式表达式
        const textarea = page.locator('textarea').first();
        if (await textarea.isVisible()) {
          const expression = await textarea.inputValue();
          console.log(`📐 公式表达式: "${expression}"`);
        }
        
        // 关闭模态框
        const closeButtons = await page.$$('button:has-text("取消"), button:has-text("关闭"), .ant-modal-close, [aria-label="Close"]');
        if (closeButtons.length > 0) {
          await closeButtons[0].click();
          await page.waitForTimeout(1000);
        }
      }
    }
    
    // 4. 测试数据输入和计算功能
    console.log('\n📊 步骤4: 测试数据输入和计算功能...');
    
    // 查找添加按钮
    const addButtons = await page.$$('button:has-text("添加"), button:has-text("新增"), button:has-text("添加客户")');
    console.log(`🔘 找到 ${addButtons.length} 个添加按钮`);
    
    if (addButtons.length > 0) {
      console.log('📝 点击添加按钮...');
      await addButtons[0].click();
      await page.waitForTimeout(2000);
      
      // 重新获取输入框（可能新增了）
      const allInputs = await page.$$('input[type="text"], input[type="number"], table input');
      console.log(`📊 当前输入框数量: ${allInputs.length}`);
      
      if (allInputs.length >= 4) {
        console.log('🧮 开始计算准确性测试...');
        
        // 测试用例1: 基础正数计算
        await testCalculationAccuracy(page, allInputs, {
          data1: 100, data2: 2, data3: 50, data4: 1
        }, '基础正数测试');
        
        await page.waitForTimeout(2000);
        
        // 测试用例2: 负数计算
        await testCalculationAccuracy(page, allInputs, {
          data1: -100, data2: -2, data3: -50, data4: -1
        }, '负数测试');
        
        await page.waitForTimeout(2000);
        
        // 测试用例3: 混合计算
        await testCalculationAccuracy(page, allInputs, {
          data1: 100, data2: -2, data3: 50, data4: -1
        }, '正负混合测试');
        
      } else {
        console.log('❌ 输入框数量不足，无法进行计算测试');
      }
    } else {
      console.log('❌ 未找到添加按钮');
    }
    
    // 5. 生成测试报告
    console.log('\n' + '='.repeat(60));
    console.log('🎯 公式配置页面测试报告');
    console.log('='.repeat(60));
    console.log(`✅ 页面访问: ${status === 200 ? '成功' : '需要检查'}`);
    console.log(`✅ 页面结构: ${formulaElements.length > 0 ? '完整' : '需要检查'}`);
    console.log(`✅ 输入功能: ${inputs.length > 0 ? '可用' : '不可用'}`);
    console.log(`✅ 按钮功能: ${buttons.length > 0 ? '可用' : '不可用'}`);
    console.log('='.repeat(60));
    
    console.log('\n🎉 测试完成！');
    console.log('🔍 浏览器保持打开状态，您可以手动验证结果...');
    console.log('💡 建议手动测试：');
    console.log('   1. 点击公式卡片查看公式表达式');
    console.log('   2. 输入测试数据验证计算结果');
    console.log('   3. 测试负数输入和计算');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 测试计算准确性
async function testCalculationAccuracy(page, inputs, testData, testName) {
  console.log(`\n🧮 ${testName}:`);
  console.log(`📊 测试数据: data1=${testData.data1}, data2=${testData.data2}, data3=${testData.data3}, data4=${testData.data4}`);
  
  try {
    // 输入测试数据（假设前几个输入框对应data1-data4）
    const dataFields = [testData.data1, testData.data2, testData.data3, testData.data4];
    
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      if (dataFields[i] !== undefined) {
        await inputs[i].clear();
        await inputs[i].fill(dataFields[i].toString());
        await page.waitForTimeout(300);
        
        const actualValue = await inputs[i].inputValue();
        console.log(`   📝 输入data${i+1}: 期望${dataFields[i]}, 实际${actualValue}`);
      }
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    await page.waitForTimeout(2000);
    
    // 获取计算结果
    const results = await page.$$('strong, .result, [class*="result"]');
    console.log(`   📈 找到 ${results.length} 个结果显示元素`);
    
    if (results.length > 0) {
      console.log('   📊 计算结果:');
      
      const calculatedResults = [];
      for (let i = 0; i < Math.min(10, results.length); i++) {
        try {
          const text = await results[i].textContent();
          const num = parseFloat(text);
          if (!isNaN(num) && isFinite(num)) {
            calculatedResults.push(num);
            console.log(`      结果${i+1}: ${num}`);
          }
        } catch (e) {
          // 忽略无法读取的元素
        }
      }
      
      // 验证预期结果
      console.log('   🔍 验证计算准确性:');
      
      // 公式1.1: (data1+data2+data4)+data3
      const expected1_1 = (testData.data1 + testData.data2 + testData.data4) + testData.data3;
      const found1_1 = calculatedResults.find(r => Math.abs(r - expected1_1) < 0.01);
      console.log(`      公式1.1 (data1+data2+data4)+data3: 预期${expected1_1}, ${found1_1 ? `✅找到${found1_1}` : '❌未找到'}`);
      
      // 公式2.1: data1*data2
      const expected2_1 = testData.data1 * testData.data2;
      const found2_1 = calculatedResults.find(r => Math.abs(r - expected2_1) < 0.01);
      console.log(`      公式2.1 data1*data2: 预期${expected2_1}, ${found2_1 ? `✅找到${found2_1}` : '❌未找到'}`);
      
      // 公式2.2: data2*data4
      const expected2_2 = testData.data2 * testData.data4;
      const found2_2 = calculatedResults.find(r => Math.abs(r - expected2_2) < 0.01);
      console.log(`      公式2.2 data2*data4: 预期${expected2_2}, ${found2_2 ? `✅找到${found2_2}` : '❌未找到'}`);
      
      // 公式3.1: data1
      const expected3_1 = testData.data1;
      const found3_1 = calculatedResults.find(r => Math.abs(r - expected3_1) < 0.01);
      console.log(`      公式3.1 data1: 预期${expected3_1}, ${found3_1 ? `✅找到${found3_1}` : '❌未找到'}`);
      
      // 计算成功率
      const verifications = [found1_1, found2_1, found2_2, found3_1];
      const successCount = verifications.filter(v => v !== undefined).length;
      const successRate = (successCount / 4 * 100).toFixed(1);
      
      console.log(`   📊 ${testName}验证结果: ${successCount}/4 正确 (${successRate}%)`);
      
    } else {
      console.log('   ❌ 未找到计算结果显示元素');
    }
    
  } catch (error) {
    console.log(`   ❌ ${testName}异常: ${error.message}`);
  }
}

// 执行测试
testFormulaConfigDirect().catch(console.error);
