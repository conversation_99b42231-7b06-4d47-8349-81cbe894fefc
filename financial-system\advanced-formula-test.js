// 高级公式计算自动化测试脚本
// 解决输入框定位问题，全面测试所有公式计算

const { chromium } = require('playwright');

class AdvancedFormulaTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
    this.baseUrl = 'http://localhost:3000';
  }

  // 启动浏览器
  async setup() {
    console.log('🚀 启动高级公式测试...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 2000,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    this.page = await context.newPage();
    
    // 监听页面日志
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🔴 页面错误: ${msg.text()}`);
      }
    });
  }

  // 访问页面
  async navigateToPage() {
    console.log('📊 访问公式配置页面...');
    await this.page.goto(`${this.baseUrl}/formula-config`);
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForTimeout(3000);
    
    const pageLoaded = await this.page.locator('text=公式配置网格').isVisible();
    if (pageLoaded) {
      console.log('✅ 页面加载成功');
      return true;
    } else {
      console.log('❌ 页面加载失败');
      return false;
    }
  }

  // 智能输入数据 - 改进的输入策略
  async smartInputData(data, testName) {
    console.log(`\n📝 ${testName} - 智能输入数据: ${JSON.stringify(data)}`);
    
    try {
      // 确保有客户行
      const addButton = this.page.locator('button:has-text("添加客户")').first();
      if (await addButton.isVisible()) {
        await addButton.click();
        await this.page.waitForTimeout(2000);
        console.log('✅ 添加客户行成功');
      }

      // 获取所有输入框
      const allInputs = await this.page.$$('table input');
      console.log(`📋 找到 ${allInputs.length} 个输入框`);

      // 清空所有输入框
      for (const input of allInputs) {
        try {
          await input.clear();
        } catch (e) {
          // 忽略无法清空的输入框
        }
      }

      // 策略1: 通过表格结构定位输入框
      await this.inputByTableStructure(data);
      
      // 策略2: 通过占位符文本定位
      await this.inputByPlaceholder(data);
      
      // 策略3: 通过JavaScript直接设置值
      await this.inputByJavaScript(data);

      // 触发计算
      console.log('⚡ 触发计算...');
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(3000);

    } catch (error) {
      console.log(`❌ 输入数据异常: ${error.message}`);
    }
  }

  // 策略1: 通过表格结构定位输入框
  async inputByTableStructure(data) {
    console.log('🎯 策略1: 通过表格结构定位输入框');
    
    try {
      // 查找第一个数据表格的第一行
      const firstDataRow = this.page.locator('table tbody tr').first();
      
      for (let i = 1; i <= 7; i++) {
        const fieldName = `data${i}`;
        const value = data[fieldName];
        
        if (value !== undefined) {
          // 尝试通过列索引定位（跳过客户名、用户名列）
          const cellIndex = i + 1; // 假设前面有客户名列
          const input = firstDataRow.locator(`td:nth-child(${cellIndex}) input`);
          
          if (await input.isVisible({ timeout: 1000 })) {
            await input.fill(value.toString());
            console.log(`   ✅ 表格结构输入 ${fieldName}: ${value}`);
          }
        }
      }
    } catch (error) {
      console.log(`   ⚠️ 表格结构输入失败: ${error.message}`);
    }
  }

  // 策略2: 通过占位符文本定位
  async inputByPlaceholder(data) {
    console.log('🎯 策略2: 通过占位符文本定位');
    
    for (let i = 1; i <= 7; i++) {
      const fieldName = `data${i}`;
      const value = data[fieldName];
      
      if (value !== undefined) {
        const selectors = [
          `input[placeholder*="数据${i}"]`,
          `input[placeholder*="data${i}"]`,
          `input[title*="数据${i}"]`,
          `input[aria-label*="数据${i}"]`
        ];
        
        for (const selector of selectors) {
          try {
            const input = this.page.locator(selector).first();
            if (await input.isVisible({ timeout: 500 })) {
              await input.fill(value.toString());
              console.log(`   ✅ 占位符输入 ${fieldName}: ${value}`);
              break;
            }
          } catch (e) {
            // 继续尝试下一个选择器
          }
        }
      }
    }
  }

  // 策略3: 通过JavaScript直接设置值
  async inputByJavaScript(data) {
    console.log('🎯 策略3: 通过JavaScript直接设置值');
    
    try {
      await this.page.evaluate((testData) => {
        // 查找所有输入框
        const inputs = document.querySelectorAll('table input');
        
        // 尝试通过输入框的位置或属性来匹配
        inputs.forEach((input, index) => {
          const placeholder = input.placeholder || '';
          const title = input.title || '';
          const ariaLabel = input.getAttribute('aria-label') || '';
          
          // 匹配数据字段
          for (let i = 1; i <= 7; i++) {
            const fieldName = `data${i}`;
            const value = testData[fieldName];
            
            if (value !== undefined) {
              if (placeholder.includes(`数据${i}`) || 
                  placeholder.includes(`data${i}`) ||
                  title.includes(`数据${i}`) ||
                  ariaLabel.includes(`数据${i}`)) {
                input.value = value.toString();
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));
                console.log(`JS设置 ${fieldName}: ${value}`);
              }
            }
          }
        });
        
        // 如果上面的方法不行，尝试按顺序设置（跳过前几个非数据输入框）
        const dataInputs = Array.from(inputs).slice(2, 9); // 假设前2个是客户名等
        Object.entries(testData).forEach(([key, value], index) => {
          if (dataInputs[index] && value !== undefined) {
            dataInputs[index].value = value.toString();
            dataInputs[index].dispatchEvent(new Event('input', { bubbles: true }));
            dataInputs[index].dispatchEvent(new Event('change', { bubbles: true }));
          }
        });
        
      }, data);
      
      console.log('   ✅ JavaScript设置完成');
    } catch (error) {
      console.log(`   ⚠️ JavaScript设置失败: ${error.message}`);
    }
  }

  // 获取计算结果
  async getCalculationResults() {
    try {
      const results = await this.page.evaluate(() => {
        const resultData = {};
        
        // 获取所有strong元素中的数字
        const strongElements = document.querySelectorAll('strong');
        strongElements.forEach((element, index) => {
          const text = element.textContent.trim();
          const num = parseFloat(text);
          if (!isNaN(num) && isFinite(num)) {
            resultData[`strong_${index}`] = num;
          }
        });
        
        // 获取表格中的结果
        const tables = document.querySelectorAll('table');
        tables.forEach((table, tableIndex) => {
          const rows = table.querySelectorAll('tbody tr');
          rows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, cellIndex) => {
              const strongInCell = cell.querySelector('strong');
              if (strongInCell) {
                const text = strongInCell.textContent.trim();
                const num = parseFloat(text);
                if (!isNaN(num) && isFinite(num)) {
                  resultData[`table_${tableIndex}_row_${rowIndex}_col_${cellIndex}`] = num;
                }
              }
            });
          });
        });
        
        return resultData;
      });
      
      return results;
    } catch (error) {
      console.log(`❌ 获取结果异常: ${error.message}`);
      return {};
    }
  }

  // 验证公式计算结果
  verifyFormulaResults(testData, pageResults, testName) {
    console.log(`\n🔍 验证${testName}的公式计算结果:`);
    console.log('='.repeat(60));
    
    // 定义预期的公式结果
    const expectedResults = {
      '公式1.1 (data1+data2+data4)+data3': this.calculateFormula('(data1+data2+data4)+data3', testData),
      '公式1.2 (data1+data2+data4)+data3': this.calculateFormula('(data1+data2+data4)+data3', testData),
      '公式2.1 data1*data2': this.calculateFormula('data1*data2', testData),
      '公式2.2 data2*data4': this.calculateFormula('data2*data4', testData),
      '公式3.1 data1': this.calculateFormula('data1', testData)
    };
    
    let correctCount = 0;
    let totalCount = Object.keys(expectedResults).length;
    
    for (const [formulaDesc, expected] of Object.entries(expectedResults)) {
      console.log(`\n📐 ${formulaDesc}:`);
      console.log(`   预期结果: ${expected}`);
      
      // 在页面结果中查找匹配的值
      let found = false;
      let matchedLocation = null;
      
      for (const [location, actual] of Object.entries(pageResults)) {
        if (Math.abs(actual - expected) < 0.01) {
          console.log(`   ✅ 找到匹配结果: ${actual} (位置: ${location})`);
          found = true;
          matchedLocation = location;
          correctCount++;
          break;
        }
      }
      
      if (!found) {
        // 查找最接近的值
        let closestValue = null;
        let closestLocation = null;
        let minDiff = Infinity;
        
        for (const [location, actual] of Object.entries(pageResults)) {
          const diff = Math.abs(actual - expected);
          if (diff < minDiff) {
            minDiff = diff;
            closestValue = actual;
            closestLocation = location;
          }
        }
        
        console.log(`   ❌ 未找到精确匹配`);
        if (closestValue !== null) {
          console.log(`   📊 最接近的值: ${closestValue} (位置: ${closestLocation}, 差值: ${minDiff.toFixed(2)})`);
        }
      }
    }
    
    const successRate = ((correctCount / totalCount) * 100).toFixed(1);
    console.log(`\n📊 ${testName} 验证结果: ${correctCount}/${totalCount} 正确 (${successRate}%)`);
    
    // 记录测试结果
    this.testResults.push({
      testName,
      correctCount,
      totalCount,
      successRate: parseFloat(successRate),
      testData,
      pageResults
    });
    
    console.log('='.repeat(60));
  }

  // 计算公式预期结果
  calculateFormula(expression, data) {
    try {
      // 使用默认值0替换未定义的数据
      const safeData = {
        data1: data.data1 !== undefined ? data.data1 : 0,
        data2: data.data2 !== undefined ? data.data2 : 0,
        data3: data.data3 !== undefined ? data.data3 : 0,
        data4: data.data4 !== undefined ? data.data4 : 0,
        data5: data.data5 !== undefined ? data.data5 : 0,
        data6: data.data6 !== undefined ? data.data6 : 0,
        data7: data.data7 !== undefined ? data.data7 : 0
      };

      let safeExpression = expression
        .replace(/data1/g, `(${safeData.data1})`)
        .replace(/data2/g, `(${safeData.data2})`)
        .replace(/data3/g, `(${safeData.data3})`)
        .replace(/data4/g, `(${safeData.data4})`)
        .replace(/data5/g, `(${safeData.data5})`)
        .replace(/data6/g, `(${safeData.data6})`)
        .replace(/data7/g, `(${safeData.data7})`);

      const result = eval(safeExpression);
      return isNaN(result) || !isFinite(result) ? 0 : Math.round(result);
    } catch (error) {
      console.log(`❌ 计算公式失败: ${expression}, 错误: ${error.message}`);
      return 0;
    }
  }

  // 执行单个测试用例
  async runSingleTest(testData, testName) {
    console.log(`\n🧪 执行测试: ${testName}`);
    console.log(`📊 测试数据: ${JSON.stringify(testData)}`);

    // 输入测试数据
    await this.smartInputData(testData, testName);

    // 获取页面计算结果
    const pageResults = await this.getCalculationResults();
    console.log(`📈 页面结果数量: ${Object.keys(pageResults).length}`);

    // 验证计算结果
    this.verifyFormulaResults(testData, pageResults, testName);

    // 等待一段时间再进行下一个测试
    await this.page.waitForTimeout(2000);
  }

  // 执行全面测试
  async runComprehensiveTests() {
    console.log('🎯 开始执行全面公式计算测试...\n');

    try {
      // 启动浏览器并访问页面
      await this.setup();
      const pageLoaded = await this.navigateToPage();

      if (!pageLoaded) {
        console.log('❌ 页面加载失败，测试终止');
        return;
      }

      // 定义测试用例
      const testCases = [
        {
          name: '基础正数测试',
          data: { data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 }
        },
        {
          name: '基础负数测试',
          data: { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 }
        },
        {
          name: '正负混合测试',
          data: { data1: 100, data2: -2, data3: 50, data4: -1, data5: 25, data6: -80, data7: 3 }
        },
        {
          name: '零值测试',
          data: { data1: 0, data2: -2, data3: 0, data4: 1, data5: 0, data6: -80, data7: 0 }
        },
        {
          name: '小数测试',
          data: { data1: 10.5, data2: 2.5, data3: 5.25, data4: 1.75, data5: 3.33, data6: 8.88, data7: 0.5 }
        },
        {
          name: '大数值测试',
          data: { data1: 10000, data2: 500, data3: 2500, data4: 100, data5: 750, data6: 8000, data7: 300 }
        }
      ];

      // 执行每个测试用例
      for (const testCase of testCases) {
        await this.runSingleTest(testCase.data, testCase.name);
      }

      // 生成测试报告
      this.generateTestReport();

      console.log('\n🎉 所有测试完成！');
      console.log('🔍 浏览器保持打开状态，请手动验证结果...');
      console.log('📋 详细测试结果已在控制台输出');

    } catch (error) {
      console.error('❌ 测试执行异常:', error);
    } finally {
      // 保持浏览器打开
      console.log('\n⏳ 浏览器保持打开状态...');
      console.log('按 Ctrl+C 退出测试');

      // 等待用户手动关闭
      await new Promise(() => {});
    }
  }

  // 生成测试报告
  generateTestReport() {
    console.log('\n📋 生成详细测试报告...');
    console.log('='.repeat(80));
    console.log('🎯 财务管理系统公式计算全面测试报告');
    console.log('='.repeat(80));

    const totalTests = this.testResults.length;
    const avgSuccessRate = this.testResults.reduce((sum, result) => sum + result.successRate, 0) / totalTests;

    console.log(`📊 测试统计:`);
    console.log(`   总测试用例: ${totalTests}`);
    console.log(`   平均成功率: ${avgSuccessRate.toFixed(1)}%`);

    console.log('\n📈 各测试用例详细结果:');
    this.testResults.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.testName}`);
      console.log(`   测试数据: ${JSON.stringify(result.testData)}`);
      console.log(`   验证结果: ${result.correctCount}/${result.totalCount} 正确`);
      console.log(`   成功率: ${result.successRate}%`);

      if (result.successRate < 100) {
        console.log(`   ⚠️ 需要关注的测试用例`);
      } else {
        console.log(`   ✅ 完全通过`);
      }
    });

    // 总结
    console.log('\n🎯 测试总结:');
    if (avgSuccessRate >= 90) {
      console.log('🎉 测试结果优秀！公式计算功能运行良好。');
      console.log('✅ 负数处理、正数计算、混合运算都表现正常。');
    } else if (avgSuccessRate >= 70) {
      console.log('⚠️ 测试结果良好，但有一些问题需要关注。');
    } else {
      console.log('❌ 测试结果不理想，需要检查公式计算逻辑。');
    }

    console.log('\n📋 建议:');
    console.log('1. 手动在浏览器中验证计算结果');
    console.log('2. 检查页面中显示的具体数值');
    console.log('3. 验证公式表达式是否正确配置');

    console.log('='.repeat(80));
  }
}

// 执行测试
async function main() {
  const tester = new AdvancedFormulaTest();
  await tester.runComprehensiveTests();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = AdvancedFormulaTest;
