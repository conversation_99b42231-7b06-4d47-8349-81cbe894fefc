// 客户归纳汇总计算工具类
// 处理客户数据归纳、股东数据整合和特定计算逻辑

export class CustomerConsolidationCalculations {
  
  /**
   * 主要的客户数据归纳函数
   * @param {Array} customers - 所有客户数据
   * @param {Array} shareholderData - 股东数据
   * @param {Object} manualInputs - 手动输入数据
   * @returns {Object} 归纳后的数据结构
   */
  static consolidateCustomerData(customers = [], shareholderData = [], manualInputs = {}) {
    if (!customers || customers.length === 0) {
      return this.getEmptyConsolidatedData();
    }

    // 1. 按客户名称归纳客户数据
    const consolidatedCustomers = this.consolidateByCustomerName(customers);
    
    // 2. 整合股东数据
    const customersWithShareholders = this.integrateShareholders(consolidatedCustomers, shareholderData);
    
    // 3. 计算每个归纳客户的汇总值
    const calculatedCustomers = this.calculateCustomerTotals(customersWithShareholders);
    
    // 4. 计算右侧汇总统计
    const rightSummary = this.calculateRightSummary(calculatedCustomers, manualInputs);
    
    return {
      consolidatedCustomers: calculatedCustomers,
      rightSummary: rightSummary,
      metadata: {
        totalCustomers: calculatedCustomers.length,
        originalCustomerCount: customers.length,
        consolidationRatio: customers.length > 0 ? (calculatedCustomers.length / customers.length) : 0,
        lastUpdated: new Date()
      }
    };
  }

  /**
   * 按客户名称归纳客户数据
   * @param {Array} customers - 原始客户数据
   * @returns {Array} 归纳后的客户数据
   */
  static consolidateByCustomerName(customers) {
    const customerGroups = {};
    
    customers.forEach(customer => {
      const customerName = customer.name || customer.customer || '未命名客户';
      
      if (!customerGroups[customerName]) {
        customerGroups[customerName] = {
          customerName: customerName,
          userName: customer.userName || customer.user || '',
          originalCustomers: [],
          // 数据字段汇总
          data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
          // 结果字段汇总
          result1: 0, result2: 0, result3: 0,
          // 股东信息
          shareholders: []
        };
      }
      
      const group = customerGroups[customerName];
      group.originalCustomers.push(customer);
      
      // 累加数据字段
      for (let i = 1; i <= 7; i++) {
        const fieldName = `data${i}`;
        group[fieldName] += parseFloat(customer[fieldName]) || 0;
      }
      
      // 累加结果字段
      for (let i = 1; i <= 3; i++) {
        const fieldName = `result${i}`;
        group[fieldName] += parseFloat(customer[fieldName]) || 0;
      }
    });
    
    return Object.values(customerGroups);
  }

  /**
   * 整合股东数据到归纳客户中
   * @param {Array} consolidatedCustomers - 归纳后的客户数据
   * @param {Array} shareholderData - 股东数据
   * @returns {Array} 整合股东后的客户数据
   */
  static integrateShareholders(consolidatedCustomers, shareholderData = []) {
    if (!shareholderData || shareholderData.length === 0) {
      return consolidatedCustomers;
    }

    // 按股东名称归纳股东数据
    const shareholderGroups = {};
    
    shareholderData.forEach(shareholder => {
      const shareholderName = shareholder.name || '未命名股东';
      
      if (!shareholderGroups[shareholderName]) {
        shareholderGroups[shareholderName] = {
          name: shareholderName,
          ratio: 0,
          result1: 0,
          result2: 0,
          originalShareholders: []
        };
      }
      
      const group = shareholderGroups[shareholderName];
      group.originalShareholders.push(shareholder);
      group.ratio += parseFloat(shareholder.ratio) || 0;
      group.result1 += parseFloat(shareholder.result1) || 0;
      group.result2 += parseFloat(shareholder.result2) || 0;
    });

    // 将归纳后的股东数据分配给对应的客户
    return consolidatedCustomers.map(customer => {
      // 查找与客户名称匹配的股东
      const matchingShareholders = Object.values(shareholderGroups).filter(
        shareholder => shareholder.name === customer.customerName || 
                     shareholder.name.includes(customer.customerName) ||
                     customer.customerName.includes(shareholder.name)
      );
      
      return {
        ...customer,
        shareholders: matchingShareholders
      };
    });
  }

  /**
   * 计算每个归纳客户的汇总值
   * @param {Array} customers - 客户数据
   * @returns {Array} 计算后的客户数据
   */
  static calculateCustomerTotals(customers) {
    return customers.map(customer => {
      // 计算总和：所有结果字段的总和
      const totalSum = customer.result1 + customer.result2 + customer.result3;
      
      // 四舍五入总和
      const roundedSum = Math.round(totalSum);
      
      // 计算收入：数量 × 价格 (data1 × data2)
      const income = customer.data1 * customer.data2;
      
      // 计算付出：数量 × 价格 (使用不同的数据字段，如data6 × data7)
      const expense = customer.data6 * customer.data7;
      
      return {
        ...customer,
        totalSum,
        roundedSum,
        calculatedIncome: income,
        calculatedExpense: expense
      };
    });
  }

  /**
   * 计算右侧汇总统计
   * @param {Array} customers - 计算后的客户数据
   * @param {Object} manualInputs - 手动输入数据
   * @returns {Object} 右侧汇总数据
   */
  static calculateRightSummary(customers, manualInputs = {}) {
    // 计算总收入和总付出
    const totalIncome = customers.reduce((sum, customer) => 
      sum + (customer.calculatedIncome || 0), 0);
    
    const totalExpense = customers.reduce((sum, customer) => 
      sum + (customer.calculatedExpense || 0), 0);
    
    // 获取手动输入数据
    const savedInputs = this.getManualInputs();
    const currentInputs = { ...savedInputs, ...manualInputs };
    
    const summary = {
      收入: totalIncome,
      付出: totalExpense,
      上周余额: currentInputs.上周余额 || 0,
      减免: currentInputs.减免 || 0,
      总计: 0
    };
    
    // 计算总计
    summary.总计 = summary.收入 + summary.付出 + summary.上周余额 + summary.减免;
    
    return summary;
  }

  /**
   * 获取手动输入数据
   * @returns {Object} 手动输入的数据
   */
  static getManualInputs() {
    try {
      const stored = localStorage.getItem('consolidatedSummaryManualInputs');
      return stored ? JSON.parse(stored) : { 上周余额: 0, 减免: 0 };
    } catch (error) {
      console.warn('获取手动输入数据失败:', error);
      return { 上周余额: 0, 减免: 0 };
    }
  }

  /**
   * 保存手动输入数据
   * @param {Object} inputs - 要保存的手动输入数据
   * @param {number} agentId - 代理商ID
   */
  static async saveManualInputs(inputs, agentId = null) {
    try {
      // 保存到localStorage作为备份
      localStorage.setItem('consolidatedSummaryManualInputs', JSON.stringify(inputs));

      // 如果有代理商ID，保存到数据库
      if (agentId) {
        const response = await fetch('/api/summary/consolidated', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            agentId,
            manualInputs: inputs
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '保存到数据库失败');
        }
      }

      return true;
    } catch (error) {
      console.error('保存手动输入数据失败:', error);
      throw error;
    }
  }

  /**
   * 从API获取客户归纳汇总数据
   * @param {number} agentId - 代理商ID
   * @returns {Object} API返回的完整数据
   */
  static async fetchConsolidatedData(agentId) {
    try {
      const response = await fetch(`/api/summary/consolidated?agentId=${agentId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '获取数据失败');
      }

      return await response.json();
    } catch (error) {
      console.error('获取客户归纳汇总数据失败:', error);
      throw error;
    }
  }

  /**
   * 数字格式化
   * @param {number} value - 要格式化的数字
   * @param {number} decimals - 小数位数
   * @returns {string} 格式化后的字符串
   */
  static formatNumber(value, decimals = 2) {
    if (value === null || value === undefined || isNaN(value)) {
      return '0';
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
      return '0';
    }

    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  /**
   * 获取空的归纳数据结构
   * @returns {Object} 空数据结构
   */
  static getEmptyConsolidatedData() {
    return {
      consolidatedCustomers: [],
      rightSummary: {
        收入: 0, 付出: 0, 上周余额: 0, 减免: 0, 总计: 0
      },
      metadata: {
        totalCustomers: 0,
        originalCustomerCount: 0,
        consolidationRatio: 0,
        lastUpdated: new Date()
      }
    };
  }

  /**
   * 导出数据为Excel格式
   * @param {Object} consolidatedData - 归纳数据
   * @returns {Object} Excel导出数据
   */
  static prepareExportData(consolidatedData) {
    const { consolidatedCustomers, rightSummary } = consolidatedData;
    
    // 准备表格数据
    const tableData = consolidatedCustomers.map(customer => ({
      '客户名': customer.customerName,
      '用户名': customer.userName,
      '数据1': customer.data1,
      '数据2': customer.data2,
      '数据6': customer.data6,
      '数据7': customer.data7,
      '结果1': customer.result1,
      '结果2': customer.result2,
      '结果3': customer.result3,
      '总和': customer.totalSum,
      '总和(四舍五入)': customer.roundedSum
    }));
    
    // 添加汇总行
    tableData.push({
      '客户名': '汇总统计',
      '用户名': '',
      '数据1': '',
      '数据2': '',
      '数据6': '',
      '数据7': '',
      '结果1': '',
      '结果2': '',
      '结果3': '',
      '总和': '',
      '总和(四舍五入)': ''
    });
    
    // 添加右侧汇总数据
    Object.entries(rightSummary).forEach(([key, value]) => {
      tableData.push({
        '客户名': key,
        '用户名': this.formatNumber(value, 0),
        '数据1': '', '数据2': '', '数据6': '', '数据7': '',
        '结果1': '', '结果2': '', '结果3': '', '总和': '', '总和(四舍五入)': ''
      });
    });
    
    return {
      data: tableData,
      filename: `客户归纳汇总表_${new Date().toISOString().split('T')[0]}.xlsx`
    };
  }
}
