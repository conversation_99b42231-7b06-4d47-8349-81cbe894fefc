'use client';

// 登录页面 - JavaScript版本
// 避免TypeScript版本冲突

import { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const onFinish = async (values) => {
    setLoading(true);
    try {
      // 简单的登录验证 (后续可以改为API调用)
      if (values.username === 'admin' && values.password === '123456') {
        message.success('登录成功！');
        // 存储登录状态到localStorage
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('username', values.username);
        router.push('/dashboard');
      } else {
        message.error('账号或密码错误！');
      }
    } catch (error) {
      message.error('登录失败，请重试！');
      console.error('登录错误:', error);
    } finally {
      setLoading(false);
    }
  };

  const onFinishFailed = (errorInfo) => {
    console.log('表单验证失败:', errorInfo);
    message.error('请检查输入信息！');
  };

  // 处理回车键登录
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      const form = event.target.closest('form');
      if (form) {
        const formData = new FormData(form);
        const values = {
          username: formData.get('username'),
          password: formData.get('password')
        };
        if (values.username && values.password) {
          onFinish(values);
        }
      }
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      backgroundColor: '#f0f2f5',
      padding: '20px'
    }}>
      <Card 
        title={
          <div style={{ textAlign: 'center', fontSize: '24px', fontWeight: 'bold' }}>
            财务管理系统登录
          </div>
        } 
        style={{ 
          width: 400, 
          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
          borderRadius: '8px'
        }}
      >
        <Form
          name="login"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符!' }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="请输入用户名" 
              size="large"
              onKeyPress={handleKeyPress}
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符!' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              size="large"
              onKeyPress={handleKeyPress}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              size="large"
              block
              style={{
                height: '48px',
                fontSize: '16px',
                fontWeight: 'bold'
              }}
            >
              进去
            </Button>
          </Form.Item>
          
          <div style={{ 
            textAlign: 'center', 
            color: '#666', 
            fontSize: '14px',
            marginTop: '16px'
          }}>
            <p>默认账号: admin</p>
            <p>默认密码: 123456</p>
          </div>
        </Form>
      </Card>
    </div>
  );
}
