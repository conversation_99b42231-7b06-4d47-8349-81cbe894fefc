// 动态公式引擎 - 支持用户自定义公式编辑和计算
export class FormulaEngine {
  // 验证表达式语法
  static validateExpression(expression) {
    try {
      if (!expression || expression.trim() === '') {
        return { isValid: false, error: '表达式不能为空' };
      }

      // 清理表达式
      const cleanExpression = expression.trim();

      // 检查允许的字符 - 只允许数字、运算符、括号、变量
      const allowedPattern = /^[data1-7+\-*/.() 0-9]+$/;
      if (!allowedPattern.test(cleanExpression)) {
        return { isValid: false, error: '表达式包含不允许的字符，只能使用 data1-data7、+、-、*、/、()' };
      }

      // 检查变量格式
      const variablePattern = /data[1-7]/g;
      const variables = cleanExpression.match(variablePattern) || [];
      const validVariables = ['data1', 'data2', 'data3', 'data4', 'data5', 'data6', 'data7'];
      
      for (const variable of variables) {
        if (!validVariables.includes(variable)) {
          return { isValid: false, error: `无效的变量: ${variable}，请使用 data1 到 data7` };
        }
      }

      // 检查括号匹配
      let parenthesesCount = 0;
      for (const char of cleanExpression) {
        if (char === '(') parenthesesCount++;
        if (char === ')') parenthesesCount--;
        if (parenthesesCount < 0) {
          return { isValid: false, error: '括号不匹配' };
        }
      }
      if (parenthesesCount !== 0) {
        return { isValid: false, error: '括号不匹配' };
      }

      // 尝试用测试数据解析表达式
      const testData = { 
        data1: 100, data2: 2.5, data3: 3.0, data4: 1.2, 
        data5: 2.0, data6: 800, data7: 1.5 
      };
      
      const testResult = this.calculateExpression(cleanExpression, testData);
      if (!testResult.success) {
        return { isValid: false, error: testResult.error };
      }

      return { isValid: true, message: '表达式语法正确' };
    } catch (error) {
      return { isValid: false, error: `语法错误: ${error.message}` };
    }
  }

  // 计算表达式结果
  static calculateExpression(expression, data) {
    try {
      if (!expression || expression.trim() === '') {
        return { success: false, error: '表达式为空' };
      }

      let calculationExpression = expression.trim();
      
      // 替换变量为实际数值，确保负数正确处理
      const validVariables = ['data1', 'data2', 'data3', 'data4', 'data5', 'data6', 'data7'];
      // 按变量名长度排序，避免替换冲突（如data1被data10替换）
      const sortedVariables = validVariables.sort((a, b) => b.length - a.length);

      sortedVariables.forEach(variable => {
        const value = data[variable] || 0;
        const regex = new RegExp(`\\b${variable}\\b`, 'g');
        // 用括号包围数值，确保负数和复杂表达式正确处理
        calculationExpression = calculationExpression.replace(regex, `(${value})`);
      });

      // 验证替换后的表达式只包含数字和运算符（包括负数）
      const safePattern = /^[0-9+\-*/.() ]+$/;
      if (!safePattern.test(calculationExpression)) {
        throw new Error('表达式包含非法字符');
      }

      console.log(`公式计算: ${expression} -> ${calculationExpression}`);

      // 安全计算 - 使用Function构造器而不是eval
      const result = Function(`"use strict"; return (${calculationExpression})`)();
      
      // 检查计算结果有效性
      if (!isFinite(result)) {
        throw new Error('计算结果无效（无穷大或NaN）');
      }

      // 保留两位小数精度，避免浮点数精度问题
      const roundedResult = Math.round(result * 100) / 100;

      console.log(`计算结果: ${result} -> ${roundedResult}`);

      return {
        success: true,
        result: roundedResult,
        calculationExpression: calculationExpression,
        originalExpression: expression
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        calculationExpression: calculationExpression || expression,
        originalExpression: expression
      };
    }
  }

  // 批量计算多个公式
  static calculateMultipleFormulas(formulas, customerData) {
    const results = {};
    
    Object.entries(formulas).forEach(([formulaId, formula]) => {
      if (formula.isValid && formula.expression) {
        results[formulaId] = this.calculateExpression(formula.expression, customerData);
      } else {
        results[formulaId] = {
          success: false,
          error: '公式无效或为空',
          result: 0
        };
      }
    });

    return results;
  }

  // 获取表达式中使用的变量
  static getUsedVariables(expression) {
    if (!expression) return [];
    
    const variablePattern = /data[1-7]/g;
    const matches = expression.match(variablePattern) || [];
    return [...new Set(matches)]; // 去重
  }

  // 格式化计算结果
  static formatResult(result, decimals = 2) {
    if (typeof result !== 'number' || !isFinite(result)) {
      return '错误';
    }

    // 如果是整数，不显示小数点
    if (Number.isInteger(result)) {
      return result.toLocaleString();
    }

    // 如果是小数，根据大小决定显示精度
    if (Math.abs(result) >= 1000) {
      return result.toLocaleString(undefined, { 
        minimumFractionDigits: 0, 
        maximumFractionDigits: decimals 
      });
    } else {
      return result.toLocaleString(undefined, { 
        minimumFractionDigits: 0, 
        maximumFractionDigits: decimals 
      });
    }
  }

  // 生成示例公式
  static getExampleFormulas() {
    return {
      'formula_1_1': {
        id: 'formula_1_1',
        name: '公式1.1',
        expression: '(data1+data2*data4)*data3',
        description: '复合计算公式',
        isValid: true
      },
      'formula_1_2': {
        id: 'formula_1_2',
        name: '公式1.2',
        expression: 'data1+data2*data4',
        description: '基础加乘公式',
        isValid: true
      },
      'formula_1_3': {
        id: 'formula_1_3',
        name: '公式1.3',
        expression: 'data1*data2',
        description: '简单乘积',
        isValid: true
      },
      'formula_2_1': {
        id: 'formula_2_1',
        name: '公式2.1',
        expression: 'data3+data4+data5',
        description: '三数相加',
        isValid: true
      },
      'formula_2_2': {
        id: 'formula_2_2',
        name: '公式2.2',
        expression: '(data1+data2)*data5',
        description: '和的乘积',
        isValid: true
      },
      'formula_2_3': {
        id: 'formula_2_3',
        name: '公式2.3',
        expression: 'data6/data7',
        description: '除法运算',
        isValid: true
      },
      'formula_3_1': {
        id: 'formula_3_1',
        name: '公式3.1',
        expression: 'data1',
        description: '直接取值',
        isValid: true
      },
      'formula_3_2': {
        id: 'formula_3_2',
        name: '公式3.2',
        expression: 'data2*data4*data6',
        description: '三数相乘',
        isValid: true
      },
      'formula_3_3': {
        id: 'formula_3_3',
        name: '公式3.3',
        expression: '(data1+data3)*(data2+data4)',
        description: '双重括号运算',
        isValid: true
      }
    };
  }

  // 验证数据有效性
  static validateData(data) {
    const requiredFields = ['data1', 'data2', 'data3', 'data4', 'data5', 'data6', 'data7'];
    const errors = [];

    requiredFields.forEach(field => {
      const value = data[field];
      if (value === undefined || value === null) {
        errors.push(`${field} 不能为空`);
      } else if (typeof value !== 'number' || !isFinite(value)) {
        errors.push(`${field} 必须是有效数字`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  // 计算性能统计
  static calculatePerformanceStats(formulas, customers) {
    const totalFormulas = Object.keys(formulas).length;
    const totalCustomers = customers.length;
    const totalCalculations = totalFormulas * totalCustomers;
    
    const validFormulas = Object.values(formulas).filter(f => f.isValid).length;
    const invalidFormulas = totalFormulas - validFormulas;

    return {
      totalFormulas,
      validFormulas,
      invalidFormulas,
      totalCustomers,
      totalCalculations,
      efficiency: totalFormulas > 0 ? (validFormulas / totalFormulas * 100).toFixed(2) : 0
    };
  }
}

// 默认导出
export default FormulaEngine;
