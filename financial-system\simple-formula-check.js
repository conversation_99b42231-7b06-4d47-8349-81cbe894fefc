// 简单的公式页面检查
const { chromium } = require('playwright');

async function simpleFormulaCheck() {
  console.log('🔍 简单检查公式配置页面');
  console.log('🌐 URL: http://dlsykgzq.w1.luyouxia.net/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 3000
  });
  
  const page = await browser.newPage();
  
  try {
    console.log('📊 正在访问页面...');
    
    // 使用最简单的方式访问页面
    await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config/', {
      timeout: 0 // 无超时限制
    });
    
    console.log('✅ 页面访问成功');
    
    // 等待页面加载
    await page.waitForTimeout(10000);
    
    const title = await page.title();
    const url = page.url();
    
    console.log(`📄 页面标题: "${title}"`);
    console.log(`📍 当前URL: ${url}`);
    
    // 检查页面内容
    const bodyText = await page.locator('body').textContent();
    console.log(`📝 页面内容长度: ${bodyText.length} 字符`);
    
    if (bodyText.includes('公式')) {
      console.log('✅ 页面包含"公式"相关内容');
    }
    
    if (bodyText.includes('配置')) {
      console.log('✅ 页面包含"配置"相关内容');
    }
    
    if (bodyText.includes('登录') || bodyText.includes('login')) {
      console.log('⚠️ 页面可能需要登录');
    }
    
    // 查找基本元素
    const inputs = await page.$$('input');
    const buttons = await page.$$('button');
    const tables = await page.$$('table');
    const strongs = await page.$$('strong');
    
    console.log(`📝 输入框: ${inputs.length} 个`);
    console.log(`🔘 按钮: ${buttons.length} 个`);
    console.log(`📊 表格: ${tables.length} 个`);
    console.log(`💪 Strong元素: ${strongs.length} 个`);
    
    if (strongs.length > 0) {
      console.log('📈 Strong元素内容（可能是计算结果）:');
      for (let i = 0; i < Math.min(10, strongs.length); i++) {
        try {
          const text = await strongs[i].textContent();
          const num = parseFloat(text);
          if (!isNaN(num)) {
            console.log(`   结果${i+1}: ${num}`);
          }
        } catch (e) {}
      }
    }
    
    // 如果有按钮，尝试点击第一个
    if (buttons.length > 0) {
      console.log('\n🔘 尝试点击第一个按钮...');
      try {
        const buttonText = await buttons[0].textContent();
        console.log(`按钮文本: "${buttonText}"`);
        
        await buttons[0].click();
        await page.waitForTimeout(3000);
        console.log('✅ 按钮点击成功');
        
        // 重新检查元素数量
        const newInputs = await page.$$('input');
        const newStrongs = await page.$$('strong');
        
        console.log(`📝 点击后输入框: ${newInputs.length} 个`);
        console.log(`💪 点击后Strong元素: ${newStrongs.length} 个`);
        
        if (newInputs.length > inputs.length) {
          console.log('✅ 点击按钮后增加了输入框');
        }
        
      } catch (error) {
        console.log(`❌ 按钮点击失败: ${error.message}`);
      }
    }
    
    console.log('\n🎉 检查完成！');
    console.log('🔍 浏览器保持打开状态，请手动检查页面功能...');
    console.log('💡 请手动验证：');
    console.log('   1. 页面是否完全加载');
    console.log('   2. 是否可以添加客户数据');
    console.log('   3. 输入数据后是否有计算结果');
    console.log('   4. 公式计算是否准确');
    console.log('按 Ctrl+C 退出');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 检查异常:', error);
    
    // 即使出错也保持浏览器打开
    console.log('\n🔍 浏览器保持打开状态，请手动检查...');
    await new Promise(() => {});
  }
}

// 执行检查
simpleFormulaCheck().catch(console.error);
