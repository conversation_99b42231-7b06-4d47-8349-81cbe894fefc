// Redis连接测试API
// 用于验证Redis云服务连接是否正常

import { NextResponse } from 'next/server';
import { getRedisClient, stateManager } from '../../../lib/redis';

export async function GET(request) {
  try {
    console.log('开始Redis连接测试...');
    
    // 获取Redis客户端
    const redis = getRedisClient();
    
    // 测试基本连接
    console.log('测试Redis基本连接...');
    const pingResult = await redis.ping();
    console.log('Redis PING结果:', pingResult);
    
    if (pingResult !== 'PONG') {
      throw new Error('Redis连接失败: PING命令未返回PONG');
    }
    
    // 测试写入操作
    console.log('测试Redis写入操作...');
    const testKey = 'test:connection:' + Date.now();
    const testValue = JSON.stringify({
      message: 'Redis连接测试',
      timestamp: Date.now(),
      source: 'financial-system'
    });
    
    await redis.setex(testKey, 60, testValue); // 60秒过期
    console.log('Redis写入成功:', testKey);
    
    // 测试读取操作
    console.log('测试Redis读取操作...');
    const retrievedValue = await redis.get(testKey);
    console.log('Redis读取成功:', retrievedValue);
    
    if (!retrievedValue) {
      throw new Error('Redis读取失败: 无法获取刚写入的数据');
    }
    
    const parsedValue = JSON.parse(retrievedValue);
    
    // 测试删除操作
    console.log('测试Redis删除操作...');
    const deleteResult = await redis.del(testKey);
    console.log('Redis删除结果:', deleteResult);
    
    // 测试状态管理器
    console.log('测试状态管理器...');
    const testFormulas = {
      'test_formula_1': 'data1 * data2',
      'test_formula_2': 'data3 + data4'
    };
    
    const saveResult = await stateManager.saveFormulaState(999, testFormulas);
    console.log('状态管理器保存结果:', saveResult);
    
    const loadResult = await stateManager.getFormulaState(999);
    console.log('状态管理器加载结果:', loadResult);
    
    // 清理测试数据
    await stateManager.clearState(999, 'formulas');
    
    // 获取Redis信息
    const info = await redis.info('server');
    const redisVersion = info.match(/redis_version:([^\r\n]+)/)?.[1] || 'unknown';
    
    return NextResponse.json({
      success: true,
      message: 'Redis云服务连接测试成功！',
      details: {
        ping: pingResult,
        writeTest: '成功',
        readTest: '成功',
        deleteTest: deleteResult === 1 ? '成功' : '失败',
        stateManagerTest: saveResult && loadResult ? '成功' : '失败',
        redisVersion: redisVersion,
        testData: parsedValue,
        timestamp: Date.now()
      }
    });
    
  } catch (error) {
    console.error('Redis连接测试失败:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Redis连接测试失败',
      details: {
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      }
    }, { status: 500 });
  }
}

// 获取Redis状态信息
export async function POST(request) {
  try {
    const redis = getRedisClient();
    
    // 获取Redis信息
    const info = await redis.info();
    const memory = await redis.info('memory');
    const stats = await redis.info('stats');
    
    // 获取当前数据库中的键数量
    const dbSize = await redis.dbsize();
    
    return NextResponse.json({
      success: true,
      redisInfo: {
        server: info,
        memory: memory,
        stats: stats,
        dbSize: dbSize,
        timestamp: Date.now()
      }
    });
    
  } catch (error) {
    console.error('获取Redis信息失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '获取Redis信息失败',
      details: error.message
    }, { status: 500 });
  }
}
