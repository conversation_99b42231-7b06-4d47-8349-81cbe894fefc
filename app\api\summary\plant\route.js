// API路由 - 汇总表数据植入
// POST /api/summary/plant - 将模块数据植入汇总表

import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { MODULE_CONFIGS } from '../../../../config/moduleConfigs';
import { FormulaEngine } from '../../../../lib/calculations';

/**
 * 将模块数据植入汇总表
 * POST /api/summary/plant
 * Body: { agentId: number, moduleId: string, customerData: object[] }
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { agentId, moduleId, customerData } = body;
    
    // 验证输入参数
    if (!agentId || !moduleId || !customerData) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }
    
    if (!Array.isArray(customerData) || customerData.length === 0) {
      return NextResponse.json(
        { success: false, error: '客户数据不能为空' },
        { status: 400 }
      );
    }
    
    // 验证模块配置
    const moduleConfig = MODULE_CONFIGS[moduleId];
    if (!moduleConfig) {
      return NextResponse.json(
        { success: false, error: '模块配置不存在' },
        { status: 404 }
      );
    }
    
    // 验证代理商是否存在
    const agent = await prisma.agent.findUnique({
      where: { id: parseInt(agentId) }
    });
    
    if (!agent) {
      return NextResponse.json(
        { success: false, error: '代理商不存在' },
        { status: 404 }
      );
    }
    
    const plantResults = [];
    const errors = [];
    
    // 处理每个客户数据
    for (const customer of customerData) {
      try {
        const plantResult = await plantCustomerData(agentId, moduleId, customer, moduleConfig);
        plantResults.push(plantResult);
      } catch (error) {
        console.error(`植入客户数据失败:`, error);
        errors.push({
          customerName: customer.name || '未知客户',
          error: error.message
        });
      }
    }
    
    // 统计结果
    const successCount = plantResults.filter(r => r.success).length;
    const failedCount = errors.length;
    
    return NextResponse.json({
      success: failedCount === 0,
      data: {
        agentId: parseInt(agentId),
        moduleId,
        summary: {
          total: customerData.length,
          success: successCount,
          failed: failedCount
        },
        results: plantResults,
        errors: errors
      }
    });
    
  } catch (error) {
    console.error('汇总表植入失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 植入单个客户数据到汇总表
 * @param {number} agentId - 代理商ID
 * @param {string} moduleId - 模块ID
 * @param {object} customerData - 客户数据
 * @param {object} moduleConfig - 模块配置
 * @returns {Promise<object>} 植入结果
 */
async function plantCustomerData(agentId, moduleId, customerData, moduleConfig) {
  const startTime = Date.now();
  
  try {
    // 1. 数据验证
    const validation = validateCustomerData(customerData, moduleConfig);
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.error}`);
    }
    
    // 2. 查找或创建客户记录
    let customer = await prisma.customer.findFirst({
      where: {
        agentId: parseInt(agentId),
        name: customerData.name,
        userName: customerData.userName || customerData.name
      }
    });
    
    if (!customer) {
      customer = await prisma.customer.create({
        data: {
          agentId: parseInt(agentId),
          name: customerData.name,
          userName: customerData.userName || customerData.name
        }
      });
    }
    
    // 3. 计算公式结果
    const calculatedResults = calculateModuleResults(customerData, moduleConfig);
    
    // 4. 准备汇总表数据
    const summaryData = prepareSummaryData(customerData, calculatedResults, moduleConfig);
    
    // 5. 植入到对应的模块表
    const moduleResult = await plantToModuleTable(customer.id, moduleId, customerData, calculatedResults);
    
    // 6. 更新汇总表
    const summaryResult = await updateSummaryTable(customer.id, moduleId, summaryData);
    
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      customerId: customer.id,
      customerName: customer.name,
      moduleId,
      moduleResult,
      summaryResult,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    return {
      success: false,
      customerName: customerData.name || '未知客户',
      moduleId,
      error: error.message,
      duration
    };
  }
}

/**
 * 验证客户数据
 * @param {object} customerData - 客户数据
 * @param {object} moduleConfig - 模块配置
 * @returns {object} 验证结果
 */
function validateCustomerData(customerData, moduleConfig) {
  // 检查必要字段
  if (!customerData.name || customerData.name.trim().length === 0) {
    return { isValid: false, error: '客户名称不能为空' };
  }
  
  // 检查数据字段
  const requiredDataFields = Object.keys(moduleConfig.fields).filter(
    key => key.startsWith('data') && moduleConfig.fields[key].editable
  );
  
  for (const fieldKey of requiredDataFields) {
    const value = customerData[fieldKey];
    if (value === undefined || value === null || value === '') {
      return { isValid: false, error: `字段 ${fieldKey} 不能为空` };
    }
    
    if (typeof value !== 'number' || isNaN(value)) {
      return { isValid: false, error: `字段 ${fieldKey} 必须是有效数字` };
    }
  }
  
  return { isValid: true };
}

/**
 * 计算模块结果
 * @param {object} customerData - 客户数据
 * @param {object} moduleConfig - 模块配置
 * @returns {object} 计算结果
 */
function calculateModuleResults(customerData, moduleConfig) {
  const results = {};
  
  try {
    // 获取自定义公式或使用默认公式
    const formulas = moduleConfig.customNames?.formulas || {};
    const defaultFormulas = moduleConfig.defaultFormulas || {};
    
    // 计算每个结果字段
    Object.keys(moduleConfig.resultMapping || {}).forEach(resultKey => {
      const formulaName = moduleConfig.resultMapping[resultKey];
      
      // 查找对应的公式
      let formulaExpression = null;
      
      // 先查找自定义公式
      const customFormula = Object.values(formulas).find(f => 
        f.customName === formulaName || f.defaultName === formulaName
      );
      
      if (customFormula && customFormula.expression) {
        formulaExpression = customFormula.expression;
      } else if (defaultFormulas[formulaName]) {
        formulaExpression = defaultFormulas[formulaName];
      }
      
      if (formulaExpression) {
        try {
          const result = FormulaEngine.calculate(formulaExpression, customerData);
          results[resultKey] = result;
        } catch (calcError) {
          console.warn(`计算公式 ${formulaName} 失败:`, calcError);
          results[resultKey] = 0;
        }
      } else {
        results[resultKey] = 0;
      }
    });
    
  } catch (error) {
    console.error('计算模块结果失败:', error);
  }
  
  return results;
}

/**
 * 准备汇总表数据
 * @param {object} customerData - 客户数据
 * @param {object} calculatedResults - 计算结果
 * @param {object} moduleConfig - 模块配置
 * @returns {object} 汇总表数据
 */
function prepareSummaryData(customerData, calculatedResults, moduleConfig) {
  const summaryData = {
    // 基础信息
    customerName: customerData.name,
    userName: customerData.userName || customerData.name,
    
    // 数据字段（使用自定义名称）
    ...customerData,
    
    // 计算结果
    ...calculatedResults,
    
    // 汇总统计
    totalSum: Object.values(calculatedResults).reduce((sum, val) => sum + (val || 0), 0),
    
    // 元数据
    moduleId: moduleConfig.id,
    moduleName: moduleConfig.displayName,
    lastUpdated: new Date()
  };
  
  return summaryData;
}

/**
 * 植入到模块表
 * @param {number} customerId - 客户ID
 * @param {string} moduleId - 模块ID
 * @param {object} customerData - 客户数据
 * @param {object} calculatedResults - 计算结果
 * @returns {Promise<object>} 植入结果
 */
async function plantToModuleTable(customerId, moduleId, customerData, calculatedResults) {
  try {
    // 根据模块ID确定表名
    const tableName = getModuleTableName(moduleId);
    
    if (!tableName) {
      throw new Error(`未知的模块ID: ${moduleId}`);
    }
    
    // 准备数据
    const moduleData = {
      customerId,
      ...customerData,
      ...calculatedResults,
      updatedAt: new Date()
    };
    
    // 移除非数据库字段
    delete moduleData.name;
    delete moduleData.userName;
    
    // 使用upsert操作
    const result = await prisma[tableName].upsert({
      where: { customerId },
      update: moduleData,
      create: moduleData
    });
    
    return {
      success: true,
      tableName,
      recordId: result.id
    };
    
  } catch (error) {
    console.error('植入模块表失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 更新汇总表
 * @param {number} customerId - 客户ID
 * @param {string} moduleId - 模块ID
 * @param {object} summaryData - 汇总数据
 * @returns {Promise<object>} 更新结果
 */
async function updateSummaryTable(customerId, moduleId, summaryData) {
  try {
    // 这里可以根据需要更新专门的汇总表
    // 暂时使用FinancialData表作为汇总表
    
    const result = await prisma.financialData.upsert({
      where: { customerId },
      update: {
        formulas: JSON.stringify(summaryData),
        results: JSON.stringify(summaryData),
        totalSum: summaryData.totalSum,
        updatedAt: new Date()
      },
      create: {
        customerId,
        formulas: JSON.stringify(summaryData),
        results: JSON.stringify(summaryData),
        totalSum: summaryData.totalSum
      }
    });
    
    return {
      success: true,
      recordId: result.id
    };
    
  } catch (error) {
    console.error('更新汇总表失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 获取模块对应的数据库表名
 * @param {string} moduleId - 模块ID
 * @returns {string} 表名
 */
function getModuleTableName(moduleId) {
  const tableMap = {
    'module1_1': 'module1',
    'module2_1': 'module2',
    'module3_1': 'module3',
    'module4_1': 'module4',
    'module5_1': 'module5',
    'module6_1': 'module6',
    'module7_1': 'module7'
  };
  
  return tableMap[moduleId] || null;
}
