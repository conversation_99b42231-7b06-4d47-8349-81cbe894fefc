// 状态管理API - 使用Redis解决React状态管理问题
// 提供实时状态同步和持久化

import { NextResponse } from 'next/server';
import { stateManager } from '../../../lib/redis';

// 获取状态
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const type = searchParams.get('type');
    
    if (!agentId || !type) {
      return NextResponse.json(
        { error: '缺少必要参数: agentId 和 type' },
        { status: 400 }
      );
    }
    
    console.log(`获取状态请求: agentId=${agentId}, type=${type}`);
    
    let state = null;
    
    switch (type) {
      case 'formulas':
        state = await stateManager.getFormulaState(agentId);
        break;
      case 'editing':
        state = await stateManager.getEditingState(agentId);
        break;
      default:
        return NextResponse.json(
          { error: '不支持的状态类型' },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      success: true,
      agentId: parseInt(agentId),
      type,
      state,
      timestamp: Date.now()
    });
    
  } catch (error) {
    console.error('获取状态失败:', error);
    return NextResponse.json(
      { 
        error: '获取状态失败',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// 保存状态
export async function POST(request) {
  try {
    const { agentId, type, state } = await request.json();
    
    if (!agentId || !type || state === undefined) {
      return NextResponse.json(
        { error: '缺少必要参数: agentId, type, state' },
        { status: 400 }
      );
    }
    
    console.log(`保存状态请求: agentId=${agentId}, type=${type}`);
    
    // 设置锁防止并发冲突
    const lockAcquired = await stateManager.setLock(agentId, type, 10);
    if (!lockAcquired) {
      return NextResponse.json(
        { error: '状态正在被其他操作修改，请稍后重试' },
        { status: 409 }
      );
    }
    
    let success = false;
    
    try {
      switch (type) {
        case 'formulas':
          success = await stateManager.saveFormulaState(agentId, state);
          break;
        case 'editing':
          success = await stateManager.saveEditingState(agentId, state);
          break;
        default:
          return NextResponse.json(
            { error: '不支持的状态类型' },
            { status: 400 }
          );
      }
      
      if (success) {
        return NextResponse.json({
          success: true,
          agentId: parseInt(agentId),
          type,
          message: '状态保存成功',
          timestamp: Date.now()
        });
      } else {
        throw new Error('状态保存失败');
      }
      
    } finally {
      // 释放锁
      await stateManager.releaseLock(agentId, type);
    }
    
  } catch (error) {
    console.error('保存状态失败:', error);
    return NextResponse.json(
      { 
        error: '保存状态失败',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// 清除状态
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const type = searchParams.get('type');
    
    if (!agentId || !type) {
      return NextResponse.json(
        { error: '缺少必要参数: agentId 和 type' },
        { status: 400 }
      );
    }
    
    console.log(`清除状态请求: agentId=${agentId}, type=${type}`);
    
    const success = await stateManager.clearState(agentId, type);
    
    if (success) {
      return NextResponse.json({
        success: true,
        agentId: parseInt(agentId),
        type,
        message: '状态清除成功',
        timestamp: Date.now()
      });
    } else {
      throw new Error('状态清除失败');
    }
    
  } catch (error) {
    console.error('清除状态失败:', error);
    return NextResponse.json(
      { 
        error: '清除状态失败',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
