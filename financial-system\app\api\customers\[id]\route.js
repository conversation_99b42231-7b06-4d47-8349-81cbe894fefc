// 单个客户API路由 - JavaScript版本
// 避免TypeScript版本冲突

import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';

// 获取单个客户
export async function GET(request, { params }) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的客户ID' }, 
        { status: 400 }
      );
    }
    
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        agent: true,
        financialData: true,
      }
    });
    
    if (!customer) {
      return NextResponse.json(
        { error: '客户不存在' }, 
        { status: 404 }
      );
    }
    
    return NextResponse.json(customer);
  } catch (error) {
    console.error('获取客户失败:', error);
    return NextResponse.json(
      { error: '获取客户失败' }, 
      { status: 500 }
    );
  }
}

// 更新客户 - 彻底修复版本
export async function PATCH(request, { params }) {
  try {
    console.log('单个客户更新API调用开始');
    const id = parseInt(params.id);
    const data = await request.json();

    console.log(`更新客户 ${id}:`, data);

    if (isNaN(id)) {
      console.error('无效的客户ID:', params.id);
      return NextResponse.json(
        { error: '无效的客户ID' },
        { status: 400 }
      );
    }

    // 检查客户是否存在
    const existingCustomer = await prisma.customer.findUnique({
      where: { id }
    });

    if (!existingCustomer) {
      console.error(`客户 ${id} 不存在`);
      return NextResponse.json(
        { error: '客户不存在' },
        { status: 404 }
      );
    }

    // 准备更新数据，过滤undefined值
    const updateData = {};
    if (data.name !== undefined) updateData.name = data.name?.trim() || '';
    if (data.userName !== undefined) updateData.userName = data.userName?.trim() || '';
    if (data.data1 !== undefined) updateData.data1 = data.data1 ? parseFloat(data.data1) : 0;
    if (data.data2 !== undefined) updateData.data2 = data.data2 ? parseFloat(data.data2) : 0;
    if (data.data3 !== undefined) updateData.data3 = data.data3 ? parseFloat(data.data3) : 0;
    if (data.data4 !== undefined) updateData.data4 = data.data4 ? parseFloat(data.data4) : 0;
    if (data.data5 !== undefined) updateData.data5 = data.data5 ? parseFloat(data.data5) : 0;
    if (data.data6 !== undefined) updateData.data6 = data.data6 ? parseFloat(data.data6) : 0;
    if (data.data7 !== undefined) updateData.data7 = data.data7 ? parseFloat(data.data7) : 0;
    if (data.data8 !== undefined) updateData.data8 = data.data8 ? parseFloat(data.data8) : 0;

    console.log(`客户 ${id} 最终更新数据:`, updateData);

    // 更新客户数据
    const updatedCustomer = await prisma.customer.update({
      where: { id },
      data: updateData,
      include: {
        agent: true,
        financialData: true,
      }
    });

    console.log(`客户 ${id} 更新成功:`, updatedCustomer);
    return NextResponse.json({
      success: true,
      customer: updatedCustomer
    });

  } catch (error) {
    console.error('更新客户失败:', error);

    // 详细的错误信息
    let errorMessage = '更新客户失败';
    let statusCode = 500;

    if (error.code === 'P2002') {
      errorMessage = '客户名称已存在';
      statusCode = 409;
    } else if (error.code === 'P2025') {
      errorMessage = '要更新的客户不存在';
      statusCode = 404;
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: error.message,
        code: error.code || 'UNKNOWN'
      },
      { status: statusCode }
    );
  }
}

// 删除客户
export async function DELETE(request, { params }) {
  try {
    const id = parseInt(params.id);
    
    if (isNaN(id)) {
      return NextResponse.json(
        { error: '无效的客户ID' }, 
        { status: 400 }
      );
    }
    
    // 检查客户是否存在
    const existingCustomer = await prisma.customer.findUnique({
      where: { id },
      include: {
        financialData: true
      }
    });
    
    if (!existingCustomer) {
      return NextResponse.json(
        { error: '客户不存在' }, 
        { status: 404 }
      );
    }
    
    // 使用事务删除客户及其关联数据
    await prisma.$transaction(async (tx) => {
      // 先删除财务数据
      await tx.financialData.deleteMany({
        where: { customerId: id }
      });
      
      // 再删除客户
      await tx.customer.delete({
        where: { id }
      });
    });
    
    return NextResponse.json({ message: '客户删除成功' });
  } catch (error) {
    console.error('删除客户失败:', error);
    return NextResponse.json(
      { error: '删除客户失败' }, 
      { status: 500 }
    );
  }
}
