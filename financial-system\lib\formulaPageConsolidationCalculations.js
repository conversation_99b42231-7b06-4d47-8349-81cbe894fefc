// 公式配置页面专用的客户归纳汇总计算工具类
// 处理group1-group7格式的数据归纳、股东数据整合和特定计算逻辑

export class FormulaPageConsolidationCalculations {
  
  /**
   * 主要的公式配置页面数据归纳函数
   * @param {Object} customerData - 公式配置页面的客户数据 (group1-group7格式)
   * @param {Object} manualInputs - 手动输入数据
   * @returns {Object} 归纳后的数据结构
   */
  static consolidateFormulaPageData(customerData = {}, manualInputs = {}) {
    console.log('FormulaPageConsolidationCalculations: 开始处理数据', { customerData, manualInputs });

    if (!customerData || Object.keys(customerData).length === 0) {
      console.log('FormulaPageConsolidationCalculations: 客户数据为空，返回空数据');
      return this.getEmptyConsolidatedData();
    }

    // 1. 将group格式转换为统一的客户数组
    const allCustomers = this.convertGroupDataToCustomerArray(customerData);
    console.log('FormulaPageConsolidationCalculations: 转换后的客户数组', allCustomers);

    // 2. 直接整合股东数据（不按客户名合并）
    const customersWithShareholders = this.integrateShareholders(allCustomers);
    console.log('FormulaPageConsolidationCalculations: 整合股东后的数据', customersWithShareholders);

    // 3. 计算每个客户的汇总值
    const calculatedCustomers = this.calculateCustomerTotals(customersWithShareholders);
    console.log('FormulaPageConsolidationCalculations: 计算后的客户数据', calculatedCustomers);

    // 4. 合并客户名和股东名相同的数据
    const mergedByName = this.mergeCustomerAndShareholderByName(calculatedCustomers);
    console.log('FormulaPageConsolidationCalculations: 合并客户名和股东名后的数据', mergedByName);

    // 5. 按合并后的名称分组显示（用于界面展示）
    const groupedByName = this.groupMergedDataByName(mergedByName);
    console.log('FormulaPageConsolidationCalculations: 按名称分组后的数据', groupedByName);

    // 6. 计算右侧汇总统计
    const rightSummary = this.calculateRightSummary(calculatedCustomers, manualInputs);

    return {
      consolidatedByUser: groupedByName, // 按合并后的名称分组
      consolidatedCustomers: mergedByName,
      rightSummary: rightSummary,
      metadata: {
        totalCustomers: calculatedCustomers.length,
        originalCustomerCount: allCustomers.length,
        consolidationRatio: allCustomers.length > 0 ? (calculatedCustomers.length / allCustomers.length) : 0,
        groupCount: Object.keys(customerData).length,
        lastUpdated: new Date()
      }
    };
  }

  /**
   * 将group格式的数据转换为统一的客户数组
   * @param {Object} customerData - group1-group7格式的数据
   * @returns {Array} 统一格式的客户数组
   */
  static convertGroupDataToCustomerArray(customerData) {
    const allCustomers = [];

    Object.entries(customerData).forEach(([groupKey, customers]) => {
      if (Array.isArray(customers)) {
        customers.forEach(customer => {
          // 转换为统一格式
          const normalizedCustomer = {
            // 基本信息
            name: customer.customer || customer.name || '未命名客户',
            customerName: customer.customer || customer.name || '未命名客户',
            userName: customer.user || customer.userName || '',
            groupKey: groupKey,
            originalKey: customer.key,

            // 数据字段 (确保都是数字)
            data1: parseFloat(customer.data1) || 0,
            data2: parseFloat(customer.data2) || 0,
            data3: parseFloat(customer.data3) || 0,
            data4: parseFloat(customer.data4) || 0,
            data5: parseFloat(customer.data5) || 0,
            data6: parseFloat(customer.data6) || 0,
            data7: parseFloat(customer.data7) || 0,

            // 结果字段
            result1: parseFloat(customer.result1) || 0,
            result2: parseFloat(customer.result2) || 0,
            result3: parseFloat(customer.result3) || 0,

            // 股东信息
            shareholders: customer.shareholders || []
          };

          // 只添加有实际数据的客户（至少有一个非零数据字段或结果字段，或者有股东信息）
          const hasData = normalizedCustomer.data1 !== 0 ||
                         normalizedCustomer.data2 !== 0 ||
                         normalizedCustomer.data3 !== 0 ||
                         normalizedCustomer.data4 !== 0 ||
                         normalizedCustomer.data5 !== 0 ||
                         normalizedCustomer.data6 !== 0 ||
                         normalizedCustomer.data7 !== 0 ||
                         normalizedCustomer.result1 !== 0 ||
                         normalizedCustomer.result2 !== 0 ||
                         normalizedCustomer.result3 !== 0 ||
                         (normalizedCustomer.shareholders && normalizedCustomer.shareholders.length > 0);

          if (hasData) {
            allCustomers.push(normalizedCustomer);
          }
        });
      }
    });

    return allCustomers;
  }

  /**
   * 按用户名分组已计算的客户数据（用于界面展示）
   * @param {Array} calculatedCustomers - 已计算的客户数据
   * @returns {Object} 按用户名分组的客户数据
   */
  static groupCalculatedCustomersByUser(calculatedCustomers) {
    const userGroups = {};

    calculatedCustomers.forEach(customer => {
      const userName = customer.userName || '未命名用户';

      if (!userGroups[userName]) {
        userGroups[userName] = [];
      }

      userGroups[userName].push(customer);
    });

    return userGroups;
  }

  /**
   * 按客户名分组已计算的客户数据（用于界面展示）
   * @param {Array} calculatedCustomers - 已计算的客户数据
   * @returns {Object} 按客户名分组的客户数据
   */
  static groupCalculatedCustomersByCustomerName(calculatedCustomers) {
    const customerGroups = {};

    calculatedCustomers.forEach(customer => {
      const customerName = customer.customerName || customer.name || '未命名客户';

      if (!customerGroups[customerName]) {
        customerGroups[customerName] = [];
      }

      customerGroups[customerName].push(customer);
    });

    return customerGroups;
  }

  /**
   * 按客户名称归纳客户数据
   * @param {Array} customers - 统一格式的客户数据
   * @returns {Array} 归纳后的客户数据
   */
  static consolidateByCustomerName(customers) {
    const customerGroups = {};
    
    customers.forEach(customer => {
      const customerName = customer.name || '未命名客户';
      
      if (!customerGroups[customerName]) {
        customerGroups[customerName] = {
          customerName: customerName,
          userName: customer.userName || '',
          originalCustomers: [],
          originalCount: 0,
          // 数据字段汇总
          data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
          // 结果字段汇总
          result1: 0, result2: 0, result3: 0,
          // 股东信息
          allShareholders: []
        };
      }
      
      const group = customerGroups[customerName];
      group.originalCustomers.push(customer);
      group.originalCount++;
      
      // 累加数据字段
      for (let i = 1; i <= 7; i++) {
        const fieldName = `data${i}`;
        group[fieldName] += customer[fieldName];
      }
      
      // 累加结果字段
      for (let i = 1; i <= 3; i++) {
        const fieldName = `result${i}`;
        group[fieldName] += customer[fieldName];
      }
      
      // 收集所有股东
      if (customer.shareholders && customer.shareholders.length > 0) {
        group.allShareholders.push(...customer.shareholders);
      }
    });
    
    return Object.values(customerGroups);
  }

  /**
   * 整合股东数据到客户中
   * @param {Array} customers - 客户数据（可能是原始数据或归纳后的数据）
   * @returns {Array} 整合股东后的客户数据
   */
  static integrateShareholders(customers) {
    return customers.map(customer => {
      // 处理原始客户数据（有shareholders字段）
      const shareholderData = customer.allShareholders || customer.shareholders || [];

      if (!shareholderData || shareholderData.length === 0) {
        return {
          ...customer,
          consolidatedShareholders: []
        };
      }

      // 按股东名称归纳股东数据
      const shareholderGroups = {};

      shareholderData.forEach(shareholder => {
        const shareholderName = shareholder.name || '未命名股东';
        
        if (!shareholderGroups[shareholderName]) {
          shareholderGroups[shareholderName] = {
            name: shareholderName,
            ratio: 0,
            result1: 0,
            result2: 0,
            originalShareholders: []
          };
        }
        
        const group = shareholderGroups[shareholderName];
        group.originalShareholders.push(shareholder);
        group.ratio += parseFloat(shareholder.ratio) || 0;
        group.result1 += parseFloat(shareholder.result1) || 0;
        group.result2 += parseFloat(shareholder.result2) || 0;
      });

      return {
        ...customer,
        consolidatedShareholders: Object.values(shareholderGroups)
      };
    });
  }

  /**
   * 计算每个归纳客户的汇总值
   * @param {Array} customers - 客户数据
   * @returns {Array} 计算后的客户数据
   */
  static calculateCustomerTotals(customers) {
    return customers.map(customer => {
      // 计算总和：所有结果字段的总和
      const totalSum = customer.result1 + customer.result2 + customer.result3;
      
      // 四舍五入总和
      const roundedSum = Math.round(totalSum);
      
      // 计算收入：数量 × 价格 (data1 × data2)
      const income = customer.data1 * customer.data2;
      
      // 计算付出：数量 × 价格 (使用不同的数据字段，如data6 × data7)
      const expense = customer.data6 * customer.data7;
      
      return {
        ...customer,
        totalSum,
        roundedSum,
        calculatedIncome: income,
        calculatedExpense: expense
      };
    });
  }

  /**
   * 计算右侧汇总统计
   * @param {Array} customers - 计算后的客户数据
   * @param {Object} manualInputs - 手动输入数据
   * @returns {Object} 右侧汇总数据
   */
  static calculateRightSummary(customers, manualInputs = {}) {
    // 获取手动输入数据
    const savedInputs = this.getManualInputs();
    const currentInputs = { ...savedInputs, ...manualInputs };

    // 计算收入和付出金额（数量 × 价格）
    const incomeAmount = (currentInputs.收入数量 || 0) * (currentInputs.收入价格 || 0);
    const expenseAmount = (currentInputs.付出数量 || 0) * (currentInputs.付出价格 || 0);

    const summary = {
      收入: {
        数量: currentInputs.收入数量 || 0,
        价格: currentInputs.收入价格 || 0,
        金额: incomeAmount,
        显示金额: incomeAmount
      },
      付出: {
        数量: currentInputs.付出数量 || 0,
        价格: currentInputs.付出价格 || 0,
        金额: expenseAmount,
        显示金额: expenseAmount
      },
      上周余额: {
        数量: null,
        价格: null,
        金额: currentInputs.上周余额1 || 0,
        显示金额: currentInputs.上周余额2 || 0
      },
      减免: {
        数量: null,
        价格: null,
        金额: currentInputs.减免1 || 0,
        显示金额: currentInputs.减免2 || 0
      }
    };

    // 计算总计
    const totalAmount = summary.收入.金额 + summary.付出.金额 + summary.上周余额.金额 + summary.减免.金额;
    const totalDisplayAmount = summary.收入.显示金额 + summary.付出.显示金额 + summary.上周余额.显示金额 + summary.减免.显示金额;

    summary.总计 = {
      数量: null,
      价格: null,
      金额: totalAmount,
      显示金额: totalDisplayAmount
    };

    return summary;
  }

  /**
   * 获取手动输入数据
   * @returns {Object} 手动输入的数据
   */
  static getManualInputs() {
    try {
      const stored = localStorage.getItem('formulaPageSummaryManualInputs');
      return stored ? JSON.parse(stored) : {
        收入数量: 0,
        收入价格: 0,
        付出数量: 0,
        付出价格: 0,
        上周余额1: 0,
        上周余额2: 0,
        减免1: 0,
        减免2: 0
      };
    } catch (error) {
      console.warn('获取手动输入数据失败:', error);
      return {
        收入数量: 0,
        收入价格: 0,
        付出数量: 0,
        付出价格: 0,
        上周余额1: 0,
        上周余额2: 0,
        减免1: 0,
        减免2: 0
      };
    }
  }

  /**
   * 保存手动输入数据
   * @param {Object} inputs - 要保存的手动输入数据
   */
  static async saveManualInputs(inputs) {
    try {
      localStorage.setItem('formulaPageSummaryManualInputs', JSON.stringify(inputs));
      return true;
    } catch (error) {
      console.error('保存手动输入数据失败:', error);
      throw error;
    }
  }

  /**
   * 四舍五入到百位
   * @param {number} value - 要四舍五入的数字
   * @returns {number} 四舍五入到百位的数字
   */
  static roundToHundreds(value) {
    if (value === null || value === undefined || isNaN(value)) {
      return 0;
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
      return 0;
    }

    // 四舍五入到百位：除以100，四舍五入，再乘以100
    return Math.round(num / 100) * 100;
  }

  /**
   * 数字格式化
   * @param {number} value - 要格式化的数字
   * @param {number} decimals - 小数位数
   * @param {boolean} roundToHundreds - 是否四舍五入到百位
   * @returns {string} 格式化后的字符串
   */
  static formatNumber(value, decimals = 2, roundToHundreds = false) {
    if (value === null || value === undefined || isNaN(value)) {
      return '0';
    }

    let num = parseFloat(value);
    if (isNaN(num)) {
      return '0';
    }

    // 如果需要四舍五入到百位
    if (roundToHundreds) {
      num = this.roundToHundreds(num);
    }

    return num.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  /**
   * 获取空的归纳数据结构
   * @returns {Object} 空数据结构
   */
  static getEmptyConsolidatedData() {
    return {
      consolidatedByUser: {},
      consolidatedCustomers: [],
      rightSummary: {
        收入: { 数量: 0, 价格: 0, 金额: 0, 显示金额: 0 },
        付出: { 数量: 0, 价格: 0, 金额: 0, 显示金额: 0 },
        上周余额: { 数量: null, 价格: null, 金额: 0, 显示金额: 0 },
        减免: { 数量: null, 价格: null, 金额: 0, 显示金额: 0 },
        总计: { 数量: null, 价格: null, 金额: 0, 显示金额: 0 }
      },
      metadata: {
        totalCustomers: 0,
        originalCustomerCount: 0,
        consolidationRatio: 0,
        groupCount: 0,
        lastUpdated: new Date()
      }
    };
  }

  /**
   * 导出数据为Excel格式
   * @param {Object} consolidatedData - 归纳数据
   * @returns {Object} Excel导出数据
   */
  static prepareExportData(consolidatedData) {
    const { consolidatedCustomers, rightSummary } = consolidatedData;
    
    // 准备表格数据
    const tableData = consolidatedCustomers.map(customer => ({
      '客户名': customer.customerName,
      '用户名': customer.userName,
      '原始客户数': customer.originalCount,
      '数据1': customer.data1,
      '数据2': customer.data2,
      '数据6': customer.data6,
      '数据7': customer.data7,
      '结果1': customer.result1,
      '结果2': customer.result2,
      '结果3': customer.result3,
      '总和': customer.totalSum,
      '总和(四舍五入)': customer.roundedSum,
      '股东数量': customer.consolidatedShareholders ? customer.consolidatedShareholders.length : 0
    }));
    
    // 添加汇总行
    tableData.push({
      '客户名': '汇总统计',
      '用户名': '', '原始客户数': '', '数据1': '', '数据2': '', '数据6': '', '数据7': '',
      '结果1': '', '结果2': '', '结果3': '', '总和': '', '总和(四舍五入)': '', '股东数量': ''
    });
    
    // 添加右侧汇总数据
    Object.entries(rightSummary).forEach(([key, value]) => {
      tableData.push({
        '客户名': key,
        '用户名': this.formatNumber(value, 0),
        '原始客户数': '', '数据1': '', '数据2': '', '数据6': '', '数据7': '',
        '结果1': '', '结果2': '', '结果3': '', '总和': '', '总和(四舍五入)': '', '股东数量': ''
      });
    });
    
    return {
      data: tableData,
      filename: `公式配置页面客户归纳汇总表_${new Date().toISOString().split('T')[0]}.xlsx`
    };
  }

  /**
   * 合并客户名和股东名相同的数据
   * @param {Array} calculatedCustomers - 已计算的客户数据
   * @returns {Array} 合并后的数据
   */
  static mergeCustomerAndShareholderByName(calculatedCustomers) {
    const nameGroups = {};

    // 首先按名称分组所有数据
    calculatedCustomers.forEach(customer => {
      const customerName = customer.customerName || customer.name || '未命名';

      // 添加客户数据
      if (!nameGroups[customerName]) {
        nameGroups[customerName] = {
          name: customerName,
          customerRecords: [],
          shareholderRecords: [],
          totalData1: 0, totalData2: 0, totalData3: 0, totalData4: 0,
          totalData5: 0, totalData6: 0, totalData7: 0,
          totalResult1: 0, totalResult2: 0, totalResult3: 0,
          allShareholders: [],
          userNames: new Set()
        };
      }

      const group = nameGroups[customerName];
      group.customerRecords.push(customer);
      group.userNames.add(customer.userName);

      // 累加客户数据
      group.totalData1 += customer.data1 || 0;
      group.totalData2 += customer.data2 || 0;
      group.totalData3 += customer.data3 || 0;
      group.totalData4 += customer.data4 || 0;
      group.totalData5 += customer.data5 || 0;
      group.totalData6 += customer.data6 || 0;
      group.totalData7 += customer.data7 || 0;
      group.totalResult1 += customer.result1 || 0;
      group.totalResult2 += customer.result2 || 0;
      group.totalResult3 += customer.result3 || 0;

      // 收集股东信息，查找与客户名相同的股东
      if (customer.consolidatedShareholders) {
        customer.consolidatedShareholders.forEach(shareholder => {
          group.allShareholders.push(shareholder);

          // 如果股东名与客户名相同，将股东数据也加入到该组
          if (shareholder.name === customerName) {
            group.shareholderRecords.push({
              ...shareholder,
              type: 'shareholder',
              originalCustomer: customer.customerName,
              userName: customer.userName
            });

            // 累加股东的结果数据
            group.totalResult1 += shareholder.result1 || 0;
            group.totalResult2 += shareholder.result2 || 0;
          }
        });
      }
    });

    // 转换为合并后的数据数组
    return Object.values(nameGroups).map(group => ({
      customerName: group.name,
      name: group.name,
      userName: Array.from(group.userNames).join(', '),
      userNames: Array.from(group.userNames),

      // 合并后的数据
      data1: group.totalData1,
      data2: group.totalData2,
      data3: group.totalData3,
      data4: group.totalData4,
      data5: group.totalData5,
      data6: group.totalData6,
      data7: group.totalData7,

      // 合并后的结果（包含股东同名数据）
      result1: group.totalResult1,
      result2: group.totalResult2,
      result3: group.totalResult3,

      // 计算总和
      totalSum: group.totalResult1 + group.totalResult2 + group.totalResult3,
      roundedSum: Math.round(group.totalResult1 + group.totalResult2 + group.totalResult3),

      // 原始记录信息
      originalCustomerRecords: group.customerRecords,
      originalShareholderRecords: group.shareholderRecords,
      consolidatedShareholders: group.allShareholders,
      originalCount: group.customerRecords.length,
      shareholderCount: group.shareholderRecords.length,

      // 标记这是合并数据
      isMerged: true,
      mergeInfo: {
        customerCount: group.customerRecords.length,
        shareholderCount: group.shareholderRecords.length,
        totalRecords: group.customerRecords.length + group.shareholderRecords.length
      }
    }));
  }

  /**
   * 按合并后的名称分组数据
   * @param {Array} mergedData - 合并后的数据
   * @returns {Object} 按名称分组的数据
   */
  static groupMergedDataByName(mergedData) {
    const groups = {};

    mergedData.forEach(item => {
      const name = item.name || item.customerName || '未命名';

      if (!groups[name]) {
        groups[name] = [];
      }

      groups[name].push(item);
    });

    return groups;
  }
}
