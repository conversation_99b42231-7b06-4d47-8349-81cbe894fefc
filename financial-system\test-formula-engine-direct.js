// 直接测试DynamicFormulaEngine的负数计算准确性
// 不依赖浏览器，直接测试计算引擎

const path = require('path');

// 模拟DynamicFormulaEngine类
class DynamicFormulaEngine {
  
  /**
   * 计算公式表达式
   * @param {string} expression - 公式表达式，如 "(data1+data2+data4)+data3"
   * @param {object} data - 数据对象，包含 data1, data2, data3 等字段
   * @returns {number} 计算结果
   */
  static calculateExpression(expression, data = {}) {
    if (!expression || typeof expression !== 'string') {
      return 0;
    }

    try {
      // 清理表达式，移除多余空格
      let cleanExpression = expression.trim();
      
      if (!cleanExpression) {
        return 0;
      }

      // 替换数据变量
      const dataValues = {
        data1: data.data1 || 0,
        data2: data.data2 || 0,
        data3: data.data3 || 0,
        data4: data.data4 || 0,
        data5: data.data5 || 0,
        data6: data.data6 || 0,
        data7: data.data7 || 0
      };

      // 按照变量名长度排序，避免替换冲突（如data10被data1替换）
      const sortedKeys = Object.keys(dataValues).sort((a, b) => b.length - a.length);
      
      for (const key of sortedKeys) {
        const regex = new RegExp(`\\b${key}\\b`, 'g');
        cleanExpression = cleanExpression.replace(regex, dataValues[key]);
      }

      // 处理幂运算 (^)
      cleanExpression = this.handlePowerOperations(cleanExpression);

      // 验证表达式安全性
      if (!this.isExpressionSafe(cleanExpression)) {
        console.warn('不安全的表达式:', cleanExpression);
        return 0;
      }

      // 计算结果
      const result = eval(cleanExpression);
      
      // 确保结果是数字
      if (isNaN(result) || !isFinite(result)) {
        return 0;
      }

      // 返回整数结果
      return Math.round(result);
      
    } catch (error) {
      console.error('公式计算错误:', error, '表达式:', expression);
      return 0;
    }
  }

  /**
   * 处理幂运算
   * 将 a^b 转换为 Math.pow(a, b)
   */
  static handlePowerOperations(expression) {
    // 处理幂运算，支持括号和数字
    return expression.replace(/(\d+(?:\.\d+)?|\([^)]+\))\s*\^\s*(\d+(?:\.\d+)?|\([^)]+\))/g, 
      (match, base, exponent) => {
        return `Math.pow(${base}, ${exponent})`;
      }
    );
  }

  /**
   * 验证表达式安全性
   * 只允许数字、基本运算符和Math函数
   */
  static isExpressionSafe(expression) {
    // 允许的字符：数字、小数点、基本运算符、括号、Math函数、空格
    const safePattern = /^[\d+\-*/().\s,Math.pow]+$/;
    return safePattern.test(expression);
  }

  /**
   * 根据公式名称和数据计算结果
   * @param {string} formulaName - 公式名称，如 "公式1.1"
   * @param {object} formulaExpressions - 公式表达式映射
   * @param {object} data - 数据对象
   * @returns {number} 计算结果
   */
  static calculateByFormulaName(formulaName, formulaExpressions, data) {
    const expression = formulaExpressions[formulaName];
    if (!expression) {
      console.warn(`公式 ${formulaName} 未找到表达式`);
      return 0;
    }
    
    return this.calculateExpression(expression, data);
  }
}

class FormulaEngineTester {
  constructor() {
    this.testResults = [];
    this.formulaExpressions = {
      '公式1.1': '(data1+data2+data4)+data3',
      '公式1.2': '(data1+data2+data4)+data3',
      '公式2.1': 'data1*data2',
      '公式2.2': 'data2*data4',
      '公式3.1': 'data1',
      '公式4.1': 'data1/data2',
      '公式4.2': 'data2/data4',
      '公式5.1': 'data1-data2',
      '公式5.2': 'data2-data4',
      '公式6.1': 'data1^2',
      '公式6.2': 'data2^2',
      '公式7.1': 'data1',
      '公式7.2': 'data2*data4',
      '公式7.3': 'data3+data5'
    };
  }

  logResult(testName, passed, details = '') {
    const result = {
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
  }

  // 手动计算预期结果
  calculateExpected(expression, data) {
    try {
      // 安全的表达式计算
      let safeExpression = expression
        .replace(/data1/g, `(${data.data1})`)
        .replace(/data2/g, `(${data.data2})`)
        .replace(/data3/g, `(${data.data3})`)
        .replace(/data4/g, `(${data.data4})`)
        .replace(/data5/g, `(${data.data5})`)
        .replace(/data6/g, `(${data.data6})`)
        .replace(/data7/g, `(${data.data7})`)
        .replace(/\^/g, '**'); // 处理幂运算
      
      const result = eval(safeExpression);
      return Math.round(result); // 四舍五入到整数
    } catch (error) {
      console.log(`❌ 计算预期结果失败: ${expression}, 错误: ${error.message}`);
      return 0;
    }
  }

  // 测试所有公式的负数计算
  testAllFormulasWithNegatives() {
    console.log('🧮 开始测试所有公式的负数计算准确性...\n');
    
    const testCases = [
      {
        name: '全正数',
        data: { data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 }
      },
      {
        name: '全负数',
        data: { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 }
      },
      {
        name: '混合正负数',
        data: { data1: 100, data2: -2, data3: 50, data4: -1, data5: 25, data6: -80, data7: 3 }
      },
      {
        name: '零值混合',
        data: { data1: 0, data2: -2, data3: 0, data4: 1, data5: 0, data6: -80, data7: 0 }
      },
      {
        name: '大负数',
        data: { data1: -1000, data2: -50, data3: -200, data4: -10, data5: -75, data6: -500, data7: -25 }
      },
      {
        name: '小数负数',
        data: { data1: -100.5, data2: -2.75, data3: -50.123, data4: -1.12345678, data5: -25.99, data6: -80.5, data7: -3.25 }
      }
    ];

    for (const testCase of testCases) {
      console.log(`📊 测试场景: ${testCase.name}`);
      console.log(`输入数据: ${JSON.stringify(testCase.data)}`);
      
      let scenarioErrors = 0;
      
      // 测试每个公式
      for (const [formulaName, expression] of Object.entries(this.formulaExpressions)) {
        // 计算预期结果
        const expected = this.calculateExpected(expression, testCase.data);
        
        // 使用DynamicFormulaEngine计算
        const actual = DynamicFormulaEngine.calculateExpression(expression, testCase.data);
        
        // 比较结果
        const passed = expected === actual;
        if (!passed) {
          scenarioErrors++;
          this.logResult(
            `${testCase.name}-${formulaName}`, 
            false, 
            `表达式: ${expression}, 预期: ${expected}, 实际: ${actual}`
          );
        } else {
          console.log(`  ✅ ${formulaName}: ${actual} (正确)`);
        }
      }
      
      if (scenarioErrors === 0) {
        this.logResult(`${testCase.name}-整体`, true, '所有公式计算正确');
      } else {
        this.logResult(`${testCase.name}-整体`, false, `${scenarioErrors} 个公式计算错误`);
      }
      
      console.log(''); // 空行分隔
    }
  }

  // 测试特殊边界情况
  testEdgeCases() {
    console.log('🔍 测试边界情况...\n');
    
    const edgeCases = [
      {
        name: '除零测试',
        data: { data1: 100, data2: 0, data3: 50, data4: 0, data5: 25, data6: 80, data7: 3 },
        formula: 'data1/data2'
      },
      {
        name: '负数除法',
        data: { data1: -100, data2: -2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 },
        formula: 'data1/data2'
      },
      {
        name: '负数幂运算',
        data: { data1: -10, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 },
        formula: 'data1^data2'
      },
      {
        name: '复杂嵌套表达式',
        data: { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 },
        formula: '((data1+data2)*data3)/(data4-data5)'
      }
    ];

    for (const testCase of edgeCases) {
      console.log(`🧪 ${testCase.name}:`);
      console.log(`  表达式: ${testCase.formula}`);
      console.log(`  数据: ${JSON.stringify(testCase.data)}`);
      
      try {
        const result = DynamicFormulaEngine.calculateExpression(testCase.formula, testCase.data);
        const expected = this.calculateExpected(testCase.formula, testCase.data);
        
        const passed = result === expected;
        this.logResult(
          testCase.name, 
          passed, 
          `结果: ${result}, 预期: ${expected}`
        );
      } catch (error) {
        this.logResult(testCase.name, false, `异常: ${error.message}`);
      }
      
      console.log('');
    }
  }

  // 生成测试报告
  generateReport() {
    console.log('\n📋 公式引擎测试报告');
    console.log('='.repeat(50));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(2) : 0;
    
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${passedTests}`);
    console.log(`失败测试: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log('='.repeat(50));
    
    // 失败测试详情
    const failedTestsList = this.testResults.filter(r => !r.passed);
    if (failedTestsList.length > 0) {
      console.log('\n🔴 失败测试详情:');
      failedTestsList.forEach((result, index) => {
        console.log(`${index + 1}. ${result.test}`);
        console.log(`   ${result.details}`);
      });
    } else {
      console.log('\n✅ 所有测试通过！公式引擎负数处理正确。');
    }
    
    console.log('\n' + '='.repeat(50));
  }

  // 运行所有测试
  runAllTests() {
    console.log('🎯 开始公式引擎直接测试...\n');
    
    this.testAllFormulasWithNegatives();
    this.testEdgeCases();
    this.generateReport();
  }
}

// 执行测试
function main() {
  const tester = new FormulaEngineTester();
  tester.runAllTests();
}

if (require.main === module) {
  main();
}

module.exports = FormulaEngineTester;
