const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// 数据库文件路径
const dbPath = path.join(process.cwd(), 'data', 'financial.db');
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 创建数据库连接
const db = new Database(dbPath);

// 启用外键约束
db.pragma('foreign_keys = ON');

// 创建表结构
const initDatabase = () => {
  // 客户表
  db.exec(`
    CREATE TABLE IF NOT EXISTS customers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT UNIQUE NOT NULL,
      group_key TEXT NOT NULL,
      customer_name TEXT DEFAULT '',
      user_name TEXT DEFAULT '',
      data1 REAL DEFAULT 0,
      data2 REAL DEFAULT 0,
      data3 REAL DEFAULT 0,
      data4 REAL DEFAULT 0,
      data5 REAL DEFAULT 0,
      data6 REAL DEFAULT 0,
      data7 REAL DEFAULT 0,
      result1 REAL DEFAULT 0,
      result2 REAL DEFAULT 0,
      result3 REAL DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 股东表
  db.exec(`
    CREATE TABLE IF NOT EXISTS shareholders (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      key TEXT UNIQUE NOT NULL,
      customer_key TEXT NOT NULL,
      name TEXT DEFAULT '',
      ratio REAL DEFAULT 0.1,
      result1 REAL DEFAULT 0,
      result2 REAL DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (customer_key) REFERENCES customers (key) ON DELETE CASCADE
    )
  `);

  // 公式表
  db.exec(`
    CREATE TABLE IF NOT EXISTS formulas (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      expression TEXT DEFAULT '',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // 创建索引
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_customers_group_key ON customers (group_key);
    CREATE INDEX IF NOT EXISTS idx_shareholders_customer_key ON shareholders (customer_key);
    CREATE INDEX IF NOT EXISTS idx_formulas_name ON formulas (name);
  `);

  console.log('数据库初始化完成');
};

// 客户数据操作
const customerOperations = {
  // 获取所有客户数据
  getAll: () => {
    const stmt = db.prepare('SELECT * FROM customers ORDER BY group_key, id');
    return stmt.all();
  },

  // 根据组获取客户数据
  getByGroup: (groupKey) => {
    const stmt = db.prepare('SELECT * FROM customers WHERE group_key = ? ORDER BY id');
    return stmt.all(groupKey);
  },

  // 创建客户
  create: (customerData) => {
    const stmt = db.prepare(`
      INSERT INTO customers (key, group_key, customer_name, user_name, data1, data2, data3, data4, data5, data6, data7, result1, result2, result3)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    return stmt.run(
      customerData.key,
      customerData.group_key,
      customerData.customer || '',
      customerData.user || '',
      customerData.data1 || 0,
      customerData.data2 || 0,
      customerData.data3 || 0,
      customerData.data4 || 0,
      customerData.data5 || 0,
      customerData.data6 || 0,
      customerData.data7 || 0,
      customerData.result1 || 0,
      customerData.result2 || 0,
      customerData.result3 || 0
    );
  },

  // 更新客户
  update: (key, customerData) => {
    const stmt = db.prepare(`
      UPDATE customers 
      SET customer_name = ?, user_name = ?, data1 = ?, data2 = ?, data3 = ?, data4 = ?, data5 = ?, data6 = ?, data7 = ?, result1 = ?, result2 = ?, result3 = ?, updated_at = CURRENT_TIMESTAMP
      WHERE key = ?
    `);
    return stmt.run(
      customerData.customer || '',
      customerData.user || '',
      customerData.data1 || 0,
      customerData.data2 || 0,
      customerData.data3 || 0,
      customerData.data4 || 0,
      customerData.data5 || 0,
      customerData.data6 || 0,
      customerData.data7 || 0,
      customerData.result1 || 0,
      customerData.result2 || 0,
      customerData.result3 || 0,
      key
    );
  },

  // 删除客户
  delete: (key) => {
    const stmt = db.prepare('DELETE FROM customers WHERE key = ?');
    return stmt.run(key);
  }
};

// 股东数据操作
const shareholderOperations = {
  // 获取客户的所有股东
  getByCustomer: (customerKey) => {
    const stmt = db.prepare('SELECT * FROM shareholders WHERE customer_key = ? ORDER BY id');
    return stmt.all(customerKey);
  },

  // 创建股东
  create: (shareholderData) => {
    const stmt = db.prepare(`
      INSERT INTO shareholders (key, customer_key, name, ratio, result1, result2)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    return stmt.run(
      shareholderData.key,
      shareholderData.customer_key,
      shareholderData.name || '',
      shareholderData.ratio || 0.1,
      shareholderData.result1 || 0,
      shareholderData.result2 || 0
    );
  },

  // 更新股东
  update: (key, shareholderData) => {
    const stmt = db.prepare(`
      UPDATE shareholders 
      SET name = ?, ratio = ?, result1 = ?, result2 = ?, updated_at = CURRENT_TIMESTAMP
      WHERE key = ?
    `);
    return stmt.run(
      shareholderData.name || '',
      shareholderData.ratio || 0.1,
      shareholderData.result1 || 0,
      shareholderData.result2 || 0,
      key
    );
  },

  // 删除股东
  delete: (key) => {
    const stmt = db.prepare('DELETE FROM shareholders WHERE key = ?');
    return stmt.run(key);
  }
};

// 公式数据操作
const formulaOperations = {
  // 获取所有公式
  getAll: () => {
    const stmt = db.prepare('SELECT * FROM formulas ORDER BY name');
    return stmt.all();
  },

  // 更新公式
  upsert: (name, expression) => {
    const stmt = db.prepare(`
      INSERT INTO formulas (name, expression) VALUES (?, ?)
      ON CONFLICT(name) DO UPDATE SET expression = ?, updated_at = CURRENT_TIMESTAMP
    `);
    return stmt.run(name, expression, expression);
  }
};

// 初始化数据库
initDatabase();

// ES6模块导出
export {
  db,
  customerOperations,
  shareholderOperations,
  formulaOperations
};
