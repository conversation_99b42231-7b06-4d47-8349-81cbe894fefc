'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>ton, Card, Typography, Space, Row, Col, Table, Input, message } from 'antd';
import { ArrowLeftOutlined, SaveOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import FormulaEditModal from '../../components/FormulaEditModal';
import ShareholderTable from '../../components/ShareholderTable';
import FormulaPageSummaryTable from '../../components/FormulaPageSummaryTable';

import { DynamicFormulaEngine } from '../../utils/DynamicFormulaEngine';

const { Title, Text } = Typography;

export default function FormulaConfigPage() {
  const router = useRouter();

  // 公式编辑状态
  const [editingFormula, setEditingFormula] = useState(null);
  const [editModalVisible, setEditModalVisible] = useState(false);

  // 汇总表状态
  const [showSummaryTable, setShowSummaryTable] = useState(false);

  // 可编辑的公式表达式状态
  const [formulaExpressions, setFormulaExpressions] = useState({
    '公式1.1': '(data1+data2+data4)+data3',
    '公式1.2': '(data1+data2+data4)+data3',
    '公式1.31': 'data1+data2+data3',
    '公式1.32': 'data1+data2+data4',
    '公式1.41': 'data1+data2+data3+data4',
    '公式1.42': 'data1+data2+data3+data4+data5',
    '公式1.51': 'data1+data2+data3+data4',
    '公式1.52': 'data1+data2+data3+data4+data5',
    '公式2.1': 'data1*data2',
    '公式2.2': 'data2*data4',
    '公式2.31': 'data1*data2*data3',
    '公式2.32': 'data1*data2*data4',
    '公式2.41': 'data1*data2*data3*data4',
    '公式2.42': 'data1*data2*data3*data4*data5',
    '公式2.51': 'data1*data2*data3*data4',
    '公式2.52': 'data1*data2*data3*data4*data5',
    '公式3.1': 'data1',
    '公式3.2': 'data2*data4',
    '公式3.3': '',
    '公式3.4': '',
    '公式4.1': 'data1/data2',
    '公式4.2': 'data2/data4',
    '公式4.31': 'data1/data2/data3',
    '公式4.32': 'data1/data2/data4',
    '公式4.41': 'data1/data2/data3/data4',
    '公式4.42': 'data1/data2/data3/data4/data5',
    '公式4.51': 'data1/data2/data3/data4',
    '公式4.52': 'data1/data2/data3/data4/data5',
    '公式5.1': 'data1-data2',
    '公式5.2': 'data2-data4',
    '公式5.31': 'data1-data2-data3',
    '公式5.32': 'data1-data2-data4',
    '公式6.1': 'data1^2',
    '公式6.2': 'data2^2',
    '公式6.3': 'data3^2',
    '公式6.41': 'data1^data2',
    '公式6.42': 'data2^data3',
    '公式6.43': 'data3^data4',
    '公式7.1': 'data1',
    '公式7.2': 'data2*data4',
    '公式7.3': 'data3+data5'
  });

  // 客户数据管理 - 客户名称同步到股东名称
  const [customerData, setCustomerData] = useState({
    group1: [{
      key: 'customer1',
      customer: '张三公司',
      user: '用户A',
      data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3,
      result1: 200, result2: 150, result3: 100,
      shareholders: [{ name: '张三公司', ratio: 0.5, result1: 100, result2: 75 }]
    }],
    group2: [{
      key: 'customer2',
      customer: '张三公司',
      user: '用户A',
      data1: 150, data2: 1.5, data3: 30, data4: 2, data5: 15, data6: 60, data7: 2,
      result1: 300, result2: 120, result3: 80,
      shareholders: [{ name: '张三公司', ratio: 0.3, result1: 90, result2: 36 }]
    }],
    group3: [{
      key: 'customer3',
      customer: '李四公司',
      user: '用户B',
      data1: 200, data2: 3, data3: 40, data4: 1.5, data5: 20, data6: 90, data7: 2.5,
      result1: 450, result2: 225, result3: 120,
      shareholders: [{ name: '李四公司', ratio: 0.8, result1: 360, result2: 180 }]
    }],
    group4: [{
      key: 'customer4',
      customer: '王五公司',
      user: '用户B',
      data1: 80, data2: 2.5, data3: 35, data4: 1, data5: 18, data6: 70, data7: 4,
      result1: 200, result2: 280, result3: 90,
      shareholders: [{ name: '王五公司', ratio: 0.4, result1: 80, result2: 112 }]
    }],
    group5: [{
      key: 'customer5',
      customer: '客户E',
      user: '用户E',
      data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
      result1: 0, result2: 0, result3: 0,
      shareholders: []
    }],
    group6: [{
      key: 'customer6',
      customer: '客户F',
      user: '用户F',
      data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
      result1: 0, result2: 0, result3: 0,
      shareholders: []
    }],
    group7: [{
      key: 'customer7',
      customer: '客户G',
      user: '用户G',
      data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
      result1: 0, result2: 0, result3: 0,
      shareholders: []
    }]
  });

  // 动态公式计算函数 - 使用用户自定义的公式表达式
  const calculateFormula = (formulaName, data) => {
    return DynamicFormulaEngine.calculateByFormulaName(formulaName, formulaExpressions, data);
  };



  // 公式编辑处理函数
  const handleFormulaEdit = (formula) => {
    setEditingFormula(formula);
    setEditModalVisible(true);
  };

  const handleFormulaSave = (formula, expression) => {
    // 保存到数据库
    saveToDatabase('saveFormula', {
      name: formula,
      expression: expression
    });

    setFormulaExpressions(prev => ({
      ...prev,
      [formula]: expression
    }));
    setEditModalVisible(false);
    setEditingFormula(null);
  };

  const handleFormulaCancel = () => {
    setEditModalVisible(false);
    setEditingFormula(null);
  };

  // 公式网格配置 - 根据界面3文件的布局
  const formulaGrid = [
    ['公式1.1', '公式1.2', '公式1.31', '公式1.32', '公式1.41', '公式1.42', '公式1.51', '公式1.52'],
    ['公式2.1', '公式2.2', '公式2.31', '公式2.32', '公式2.41', '公式2.42', '公式2.51', '公式2.52'],
    ['公式3.1', '公式3.2', '公式3.3', '公式3.4'],
    ['公式4.1', '公式4.2', '公式4.31', '公式4.32', '公式4.41', '公式4.42', '公式4.51', '公式4.52'],
    ['公式5.1', '公式5.2', '公式5.31', '公式5.32'],
    ['公式6.1', '公式6.2', '公式6.3', '公式6.41', '公式6.42', '公式6.43'],
    ['公式7.1', '公式7.2', '公式7.3']
  ];

  // 渲染公式卡片
  const renderFormulaCard = (formula) => {
    const expression = formulaExpressions[formula] || '';
    const isEmpty = !expression;

    // 根据公式类型设置不同的颜色主题
    const getCardTheme = (formulaName) => {
      if (formulaName.includes('1.')) {
        return { borderColor: '#1890ff', bgColor: '#e6f7ff', titleColor: '#1890ff' };
      } else if (formulaName.includes('2.')) {
        return { borderColor: '#52c41a', bgColor: '#f6ffed', titleColor: '#52c41a' };
      } else if (formulaName.includes('3.')) {
        return { borderColor: '#faad14', bgColor: '#fffbe6', titleColor: '#faad14' };
      } else if (formulaName.includes('4.')) {
        return { borderColor: '#f5222d', bgColor: '#fff2f0', titleColor: '#f5222d' };
      } else if (formulaName.includes('5.')) {
        return { borderColor: '#722ed1', bgColor: '#f9f0ff', titleColor: '#722ed1' };
      } else if (formulaName.includes('6.')) {
        return { borderColor: '#13c2c2', bgColor: '#e6fffb', titleColor: '#13c2c2' };
      } else {
        return { borderColor: '#eb2f96', bgColor: '#fff0f6', titleColor: '#eb2f96' };
      }
    };

    const theme = getCardTheme(formula);

    return (
      <Card
        key={formula}
        size="small"
        hoverable
        onClick={() => handleFormulaEdit(formula)}
        className="formula-card fade-in-up"
        style={{
          minHeight: '90px',
          border: `2px solid ${theme.borderColor}`,
          borderRadius: '8px',
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          background: isEmpty ? '#fafafa' : theme.bgColor,
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        styles={{ body: { padding: '10px' } }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '6px', height: '100%' }}>
          <div style={{
            background: theme.titleColor,
            color: 'white',
            padding: '2px 8px',
            borderRadius: '12px',
            fontSize: '11px',
            fontWeight: 'bold',
            margin: '0 auto'
          }}>
            {formula}
          </div>

          {isEmpty ? (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              gap: '4px'
            }}>
              <Text type="secondary" style={{ fontSize: '10px' }}>
                点击编辑公式
              </Text>
              <div style={{ fontSize: '16px' }}>✏️</div>
            </div>
          ) : (
            <div style={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Text
                code
                style={{
                  fontSize: '10px',
                  backgroundColor: 'rgba(255,255,255,0.8)',
                  padding: '4px 6px',
                  borderRadius: '4px',
                  fontFamily: 'Courier New, monospace',
                  wordBreak: 'break-all',
                  lineHeight: '1.3',
                  border: '1px solid rgba(0,0,0,0.1)',
                  maxWidth: '100%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
                title={expression} // 显示完整表达式的提示
              >
                {expression.length > 20 ? expression.substring(0, 20) + '...' : expression}
              </Text>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // 计算股东结果 - 使用动态公式
  const calculateShareholderResult = (data, shareholderIndex) => {
    if (shareholderIndex === 0) {
      // 第一个股东：公式1.31和1.32
      return {
        result1: calculateFormula('公式1.31', data),
        result2: calculateFormula('公式1.32', data)
      };
    } else if (shareholderIndex === 1) {
      // 第二个股东：公式1.41和1.42
      return {
        result1: calculateFormula('公式1.41', data),
        result2: calculateFormula('公式1.42', data)
      };
    } else {
      // 第三个及以上股东：公式1.51和1.52
      return {
        result1: calculateFormula('公式1.51', data),
        result2: calculateFormula('公式1.52', data)
      };
    }
  };

  // 添加客户
  const addCustomer = (groupKey) => {
    const newKey = Date.now().toString();
    const newCustomer = {
      key: newKey,
      customer: '',
      user: '',
      data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
      result1: 0, result2: 0, result3: 0,
      shareholders: []
    };

    // 保存到数据库
    saveToDatabase('createCustomer', {
      groupKey,
      customerData: newCustomer
    });

    setCustomerData(prev => ({
      ...prev,
      [groupKey]: [...prev[groupKey], newCustomer]
    }));
  };

  // 添加股东到客户
  const addShareholder = (groupKey, customerKey) => {
    setCustomerData(prev => ({
      ...prev,
      [groupKey]: prev[groupKey].map(customer => {
        if (customer.key === customerKey) {
          const shareholderCount = customer.shareholders.length;
          const newShareholder = {
            key: Date.now().toString(),
            name: '', // 初始为空，让用户手动输入
            ratio: 0.1,
            result1: 0,
            result2: 0
          };

          // 先确保客户存在于数据库，然后保存新股东
          saveToDatabase('createCustomer', {
            groupKey: groupKey,
            customerData: customer
          }).then(() => {
            saveToDatabase('createShareholder', {
              customerKey: customerKey,
              shareholderData: newShareholder
            });
          }).catch(() => {
            // 如果客户已存在，直接创建股东
            saveToDatabase('createShareholder', {
              customerKey: customerKey,
              shareholderData: newShareholder
            });
          });

          return {
            ...customer,
            shareholders: [...customer.shareholders, newShareholder]
          };
        }
        return customer;
      })
    }));
  };



  // 删除客户
  const deleteCustomer = (groupKey, customerKey) => {
    setCustomerData(prev => ({
      ...prev,
      [groupKey]: prev[groupKey].filter(item => item.key !== customerKey)
    }));
  };

  // 删除股东
  const deleteShareholder = (groupKey, customerKey, shareholderKey) => {
    setCustomerData(prev => ({
      ...prev,
      [groupKey]: prev[groupKey].map(customer => {
        if (customer.key === customerKey) {
          return {
            ...customer,
            shareholders: customer.shareholders.filter(shareholder => shareholder.key !== shareholderKey)
          };
        }
        return customer;
      })
    }));
  };

  // 回车键确认处理函数
  const handleKeyPress = (e, updateFunction) => {
    if (e.key === 'Enter') {
      e.target.blur(); // 失去焦点，触发保存
      updateFunction();
    }
  };

  // 数字输入验证函数 - 支持正数和负数，根据字段设置不同的小数位限制
  const validateNumberInput = (value, field = 'default') => {
    // 移除所有非数字字符（除了小数点和负号）
    const cleanValue = value.replace(/[^0-9.-]/g, '');

    // 处理负号：确保负号只能在开头
    let processedValue = cleanValue;
    const hasNegative = cleanValue.includes('-');
    if (hasNegative) {
      // 移除所有负号
      const withoutNegative = cleanValue.replace(/-/g, '');
      // 如果原来有负号，在开头添加一个
      processedValue = cleanValue.startsWith('-') ? '-' + withoutNegative : withoutNegative;
    }

    // 确保只有一个小数点
    const parts = processedValue.split('.');
    if (parts.length > 2) {
      // 只保留第一个小数点，合并后面的数字部分
      const mergedDecimal = parts.slice(1).join('');
      processedValue = parts[0] + '.' + mergedDecimal;
    }

    // 根据字段设置不同的小数位限制
    let maxDecimalPlaces = 2; // 默认2位小数
    if (field === 'data3') {
      maxDecimalPlaces = 3; // 数据3允许3位小数
    } else if (field === 'data4') {
      maxDecimalPlaces = 8; // 数据4允许8位小数
    }

    // 重新分割处理后的值来限制小数位
    const finalParts = processedValue.split('.');
    if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
      processedValue = finalParts[0] + '.' + finalParts[1].substring(0, maxDecimalPlaces);
    }

    // 确保不以小数点开头（考虑负号）
    if (processedValue.startsWith('.')) {
      processedValue = '0' + processedValue;
    } else if (processedValue.startsWith('-.')) {
      processedValue = '-0' + processedValue.substring(1);
    }

    return processedValue;
  };

  // 处理数字输入变化
  const handleNumberChange = (groupKey, recordKey, field, value) => {
    const validatedValue = validateNumberInput(value, field);
    // 对于数字字段，我们需要保持字符串格式以正确显示小数位
    // 但在计算时DynamicFormulaEngine会自动转换为数字
    const displayValue = validatedValue === '' ? 0 : validatedValue;
    updateCustomerData(groupKey, recordKey, field, displayValue);
  };

  // 本地存储操作函数（临时解决方案）
  const saveToLocalStorage = (key, data) => {
    try {
      localStorage.setItem(key, JSON.stringify(data));
      console.log(`数据已保存到本地存储: ${key}`);
    } catch (error) {
      console.error('本地存储保存失败:', error);
    }
  };

  const loadFromLocalStorage = (key, defaultValue) => {
    try {
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : defaultValue;
    } catch (error) {
      console.error('本地存储加载失败:', error);
      return defaultValue;
    }
  };

  // 数据库操作函数（备用）
  const saveToDatabase = async (action, data) => {
    // 先保存到本地存储
    if (action === 'updateCustomer') {
      const currentData = loadFromLocalStorage('customerData', customerData);
      saveToLocalStorage('customerData', currentData);
    } else if (action === 'saveFormula') {
      const currentFormulas = loadFromLocalStorage('formulaExpressions', formulaExpressions);
      saveToLocalStorage('formulaExpressions', currentFormulas);
    }

    // 尝试保存到数据库（如果API可用）
    try {
      const response = await fetch('/api/formula-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, data }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('数据已保存到数据库');
        return result;
      }
    } catch (error) {
      console.log('数据库不可用，使用本地存储');
    }

    return { success: true };
  };

  // 从存储加载数据
  const loadFromStorage = () => {
    try {
      // 尝试从本地存储加载
      const storedCustomerData = loadFromLocalStorage('customerData', null);
      const storedFormulaExpressions = loadFromLocalStorage('formulaExpressions', null);

      if (storedCustomerData && Object.keys(storedCustomerData).length > 0) {
        // 检查是否有实际的客户记录
        const hasCustomers = Object.values(storedCustomerData).some(group => group && group.length > 0);
        if (hasCustomers) {
          setCustomerData(storedCustomerData);
          console.log('从本地存储加载客户数据');
        }
      }

      if (storedFormulaExpressions) {
        setFormulaExpressions(prev => ({
          ...prev,
          ...storedFormulaExpressions
        }));
        console.log('从本地存储加载公式数据');
      }
    } catch (error) {
      console.error('加载存储数据失败:', error);
    }
  };

  // 页面加载时从存储读取数据
  useEffect(() => {
    loadFromStorage();
  }, []);

  // 切换汇总表显示
  const handleToggleSummaryTable = () => {
    setShowSummaryTable(!showSummaryTable);
  };

  // 获取表格列配置 - 恢复客户表格 + 股东信息
  const getTableColumns = (groupKey) => {
    const baseColumns = [
      {
        title: '客户',
        dataIndex: 'customer',
        key: 'customer',
        width: 100,
        render: (text, record) => (
          <Input
            size="small"
            value={text}
            onChange={(e) => updateCustomerData(groupKey, record.key, 'customer', e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, () => {})}
            placeholder="输入客户名称"
          />
        )
      },
      {
        title: '用户',
        dataIndex: 'user',
        key: 'user',
        width: 100,
        render: (text, record) => (
          <Input
            size="small"
            value={text}
            onChange={(e) => updateCustomerData(groupKey, record.key, 'user', e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, () => {})}
            placeholder="输入用户名称"
          />
        )
      },
      {
        title: '数据1',
        dataIndex: 'data1',
        key: 'data1',
        width: 80,
        render: (text, record) => (
          <Input
            size="small"
            value={typeof text === 'number' ? text.toString() : (text || '')}
            onChange={(e) => handleNumberChange(groupKey, record.key, 'data1', e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, () => {})}
            placeholder="0"
            style={{ textAlign: 'right' }}
            min="0"
            step="0.01"
          />
        )
      },
      {
        title: '数据2',
        dataIndex: 'data2',
        key: 'data2',
        width: 80,
        render: (text, record) => (
          <Input
            size="small"
            value={typeof text === 'number' ? text.toString() : (text || '')}
            onChange={(e) => handleNumberChange(groupKey, record.key, 'data2', e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, () => {})}
            placeholder="0"
            style={{ textAlign: 'right' }}
            min="0"
            step="0.01"
          />
        )
      },
      {
        title: '数据3',
        dataIndex: 'data3',
        key: 'data3',
        width: 80,
        render: (text, record) => (
          <Input
            size="small"
            value={typeof text === 'number' ? text.toString() : (text || '')}
            onChange={(e) => handleNumberChange(groupKey, record.key, 'data3', e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, () => {})}
            placeholder="0.000"
            style={{ textAlign: 'right' }}
            min="0"
            step="0.001"
            title="支持3位小数"
          />
        )
      },
      {
        title: '数据4',
        dataIndex: 'data4',
        key: 'data4',
        width: 80,
        render: (text, record) => (
          <Input
            size="small"
            value={typeof text === 'number' ? text.toString() : (text || '')}
            onChange={(e) => handleNumberChange(groupKey, record.key, 'data4', e.target.value)}
            onKeyPress={(e) => handleKeyPress(e, () => {})}
            placeholder="0.00000000"
            style={{ textAlign: 'right' }}
            min="0"
            step="0.00000001"
            title="支持8位小数"
          />
        )
      }
    ];

    // 根据不同组添加不同的列
    if (groupKey !== 'group3') {
      baseColumns.push(
        {
          title: '数据5',
          dataIndex: 'data5',
          key: 'data5',
          width: 80,
          render: (text, record) => (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleNumberChange(groupKey, record.key, 'data5', e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, () => {})}
              placeholder="0"
              style={{ textAlign: 'right' }}
              min="0"
              step="0.01"
            />
          )
        },
        {
          title: '数据6',
          dataIndex: 'data6',
          key: 'data6',
          width: 80,
          render: (text, record) => (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleNumberChange(groupKey, record.key, 'data6', e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, () => {})}
              placeholder="0"
              style={{ textAlign: 'right' }}
              min="0"
              step="0.01"
            />
          )
        },
        {
          title: '数据7',
          dataIndex: 'data7',
          key: 'data7',
          width: 80,
          render: (text, record) => (
            <Input
              size="small"
              value={text}
              onChange={(e) => handleNumberChange(groupKey, record.key, 'data7', e.target.value)}
              onKeyPress={(e) => handleKeyPress(e, () => {})}
              placeholder="0"
              style={{ textAlign: 'right' }}
              min="0"
              step="0.01"
            />
          )
        }
      );
    }

    // 添加结果列
    baseColumns.push({ title: '结果1', dataIndex: 'result1', key: 'result1', width: 80, render: (text) => <Text strong>{text}</Text> });

    if (groupKey !== 'group3') {
      baseColumns.push({ title: '结果2', dataIndex: 'result2', key: 'result2', width: 80, render: (text) => <Text strong>{text}</Text> });
    }

    if (groupKey === 'group6' || groupKey === 'group7') {
      baseColumns.push({ title: '结果3', dataIndex: 'result3', key: 'result3', width: 80, render: (text) => <Text strong>{text}</Text> });
    }

    // 添加股东信息列 - 使用嵌套的股东表格
    baseColumns.push({
      title: '股东信息',
      key: 'shareholders',
      width: 400,
      render: (_, record) => (
        <ShareholderTable
          shareholders={record.shareholders}
          customerData={record}
          formulaExpressions={formulaExpressions}
          onAddShareholder={() => addShareholder(groupKey, record.key)}
          onUpdateShareholder={(shareholderKey, field, value) =>
            updateShareholderData(groupKey, record.key, shareholderKey, field, value)
          }
          onDeleteShareholder={(shareholderKey) =>
            deleteShareholder(groupKey, record.key, shareholderKey)
          }
        />
      )
    });

    // 添加操作列
    baseColumns.push({
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button size="small" danger onClick={() => deleteCustomer(groupKey, record.key)}>删除</Button>
      )
    });

    return baseColumns;
  };

  // 更新客户数据
  const updateCustomerData = (groupKey, customerKey, field, value) => {
    setCustomerData(prev => ({
      ...prev,
      [groupKey]: prev[groupKey].map(customer => {
        if (customer.key === customerKey) {
          const updatedCustomer = { ...customer, [field]: value };

          // 根据组别确定使用的公式
          const groupIndex = parseInt(groupKey.replace('group', ''));
          const formula1 = `公式${groupIndex}.1`;
          const formula2 = `公式${groupIndex}.2`;

          // 自动计算结果1和结果2 - 使用动态公式
          updatedCustomer.result1 = calculateFormula(formula1, updatedCustomer);
          updatedCustomer.result2 = calculateFormula(formula2, updatedCustomer);

          // 如果组6或组7，还需要计算result3
          if (groupIndex >= 6) {
            const formula3 = `公式${groupIndex}.3`;
            updatedCustomer.result3 = calculateFormula(formula3, updatedCustomer);
          }

          // 如果修改的是客户名称，只对空名称的股东进行同步
          if (field === 'customer') {
            updatedCustomer.shareholders = updatedCustomer.shareholders.map(shareholder => {
              // 只有当股东名称为空或者与旧客户名称相同时才同步
              const shouldSync = !shareholder.name ||
                                shareholder.name === '' ||
                                shareholder.name === customer.customer ||
                                shareholder.name === '未命名客户';

              if (shouldSync) {
                const updatedShareholder = {
                  ...shareholder,
                  name: value || '未命名客户'
                };

                // 更新数据库中的股东名称
                saveToDatabase('updateShareholder', {
                  key: shareholder.key,
                  shareholderData: updatedShareholder
                });

                return updatedShareholder;
              }

              return shareholder; // 保持用户手动输入的股东名称
            });
          }

          // 重新计算所有股东的结果
          updatedCustomer.shareholders = updatedCustomer.shareholders.map((shareholder, index) => {
            const shareholderResults = calculateShareholderResult(updatedCustomer, index);
            return {
              ...shareholder,
              result1: shareholderResults.result1,
              result2: shareholderResults.result2
            };
          });

          return updatedCustomer;
        }
        return customer;
      })
    }));

    // 保存到存储
    setTimeout(() => {
      const currentData = customerData;
      saveToLocalStorage('customerData', currentData);
      saveToDatabase('updateCustomer', {
        key: customerKey,
        customerData: currentData[groupKey]?.find(c => c.key === customerKey)
      });
    }, 100);
  };

  // 更新股东数据
  const updateShareholderData = (groupKey, customerKey, shareholderKey, field, value) => {
    setCustomerData(prev => ({
      ...prev,
      [groupKey]: prev[groupKey].map(customer => {
        if (customer.key === customerKey) {
          const updatedCustomer = {
            ...customer,
            shareholders: customer.shareholders.map(shareholder => {
              if (shareholder.key === shareholderKey) {
                const updatedShareholder = { ...shareholder, [field]: value };

                // 保存股东数据到数据库
                saveToDatabase('updateShareholder', {
                  key: shareholderKey,
                  shareholderData: updatedShareholder
                });

                return updatedShareholder;
              }
              return shareholder;
            })
          };

          // 保存整个客户数据到本地存储
          setTimeout(() => {
            const updatedData = {
              ...prev,
              [groupKey]: prev[groupKey].map(c =>
                c.key === customerKey ? updatedCustomer : c
              )
            };
            saveToLocalStorage('customerData', updatedData);
          }, 100);

          return updatedCustomer;
        }
        return customer;
      })
    }));
  };

  // 保存植入汇总表处理函数
  const handleSaveToSummaryTable = async () => {
    console.log('🚀 开始保存植入汇总表...');
    try {
      // 1. 数据验证
      const totalCustomers = Object.values(customerData).reduce((sum, group) => sum + group.length, 0);
      console.log('📊 客户数据统计:', { totalCustomers, customerData });

      if (totalCustomers === 0) {
        message.warning('没有客户数据可以保存！');
        return;
      }

      // 2. 数据收集和格式化
      const summaryData = {
        customerData: customerData,
        formulaExpressions: formulaExpressions,
        timestamp: new Date().toISOString(),
        totalCustomers: totalCustomers
      };

      console.log('💾 准备保存的数据:', summaryData);

      // 3. 保存到本地存储
      saveToLocalStorage('summaryTableData', summaryData);
      console.log('✅ 本地存储保存完成');

      // 4. 调用API保存到数据库
      console.log('📡 开始调用API...');
      const response = await fetch('/api/summary-layout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(summaryData),
      });

      console.log('📡 API响应状态:', response.status, response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('📡 API响应结果:', result);
        message.success(`数据保存成功！共保存 ${totalCustomers} 条客户数据`);

        // 5. 等待一下确保数据保存完成，然后跳转
        console.log('🔄 准备跳转到汇总表页面...');
        setTimeout(async () => {
          try {
            await router.push('/summary-layout');
            console.log('✅ 页面跳转完成');
          } catch (routerError) {
            console.error('❌ 页面跳转失败:', routerError);
            // 尝试使用window.location作为备选方案
            window.location.href = '/summary-layout';
          }
        }, 500);
      } else {
        // 即使API失败，也允许跳转（使用本地存储的数据）
        console.log('⚠️ API失败，使用本地数据跳转');
        message.warning('数据库保存失败，但本地数据已保存，将使用本地数据显示汇总表');
        setTimeout(async () => {
          try {
            await router.push('/summary-layout');
          } catch (routerError) {
            window.location.href = '/summary-layout';
          }
        }, 500);
      }
    } catch (error) {
      console.error('❌ 保存植入汇总表失败:', error);
      // 即使出错，也尝试跳转到汇总表页面
      message.warning('保存过程中出现问题，将使用当前数据显示汇总表');
      try {
        await router.push('/summary-layout');
        console.log('✅ 错误处理后页面跳转完成');
      } catch (routerError) {
        console.error('❌ 页面跳转也失败了:', routerError);
        message.error('页面跳转失败，请手动访问汇总表页面');
      }
    }
  };





  return (
    <>
      {/* 添加CSS动画 */}
      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .fade-in-up {
          animation: fadeInUp 0.6s ease-out;
        }

        .formula-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }
      `}</style>

      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
      }}>
      {/* 顶部导航 */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px 24px',
        borderBottom: '3px solid #5a67d8',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 2px 12px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => router.push('/dashboard')}
            style={{
              background: 'rgba(255,255,255,0.2)',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white',
              fontWeight: 'bold',
              backdropFilter: 'blur(10px)'
            }}
          >
            返回仪表盘
          </Button>
          <div>
            <Title level={2} style={{ margin: 0, color: 'white', textShadow: '0 2px 4px rgba(0,0,0,0.3)' }}>
              🧮 界面3 - 公式配置与数据展示
            </Title>
            <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
              动态公式编辑器 | 实时计算 | 股东信息管理
            </Text>
          </div>
        </div>

        {/* 状态指示器 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          background: 'rgba(255,255,255,0.1)',
          padding: '8px 16px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <div style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: '#52c41a',
              animation: 'pulse 2s infinite'
            }}></div>
            <Text style={{ color: 'white', fontSize: '12px' }}>系统运行中</Text>
          </div>
        </div>
      </div>

      <div style={{ padding: '24px' }}>
        {/* 公式网格区域 */}
        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '16px', fontWeight: 'bold' }}>📊 公式配置网格</span>
              <span style={{ fontSize: '12px', color: '#666', fontWeight: 'normal' }}>
                点击任意公式卡片进行编辑
              </span>
            </div>
          }
          style={{
            marginBottom: '24px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            borderRadius: '8px'
          }}
          styles={{ body: { padding: '20px' } }}
        >
          <div style={{
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            padding: '16px',
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            {formulaGrid.map((row, rowIndex) => (
              <Row key={rowIndex} gutter={[12, 12]} style={{ marginBottom: '12px' }}>
                {row.map((formula) => (
                  <Col key={formula} span={24 / row.length}>
                    {renderFormulaCard(formula)}
                  </Col>
                ))}
              </Row>
            ))}
          </div>

          {/* 公式统计信息 */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '12px 16px',
            background: '#f0f2f5',
            borderRadius: '6px',
            fontSize: '12px',
            color: '#666'
          }}>
            <span>
              📈 总公式数: {Object.keys(formulaExpressions).length}
            </span>
            <span>
              ✅ 已配置: {Object.values(formulaExpressions).filter(expr => expr.trim()).length}
            </span>
            <span>
              ⚪ 空白: {Object.values(formulaExpressions).filter(expr => !expr.trim()).length}
            </span>
          </div>
        </Card>

        {/* 操作按钮区域 */}
        <Card
          style={{
            marginBottom: '24px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            border: 'none',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
          }}
          styles={{ body: { padding: '20px' } }}
        >
          <div style={{ textAlign: 'center' }}>
            <Space size="large">
              <Button
                type="primary"
                icon={<SaveOutlined />}
                size="large"
                onClick={handleSaveToSummaryTable}
                style={{
                  background: '#52c41a',
                  borderColor: '#52c41a',
                  boxShadow: '0 2px 8px rgba(82, 196, 26, 0.3)',
                  fontWeight: 'bold',
                  height: '48px',
                  padding: '0 24px'
                }}
              >
                💾 保存值入汇总表
              </Button>
              <Button
                type={showSummaryTable ? "primary" : "default"}
                size="large"
                onClick={handleToggleSummaryTable}
                style={{
                  background: showSummaryTable ? '#1890ff' : 'white',
                  borderColor: showSummaryTable ? '#1890ff' : '#d9d9d9',
                  color: showSummaryTable ? 'white' : '#666',
                  fontWeight: 'bold',
                  height: '48px',
                  padding: '0 24px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                {showSummaryTable ? '📋 返回公式配置' : '📊 汇总表'}
              </Button>
            </Space>
          </div>
        </Card>

        {/* 客户数据表格区域 */}
        {showSummaryTable && (
          /* 汇总表视图 */
          <FormulaPageSummaryTable
            customerData={customerData}
            loading={false}
            onRefresh={() => {
              // 重新加载数据
              loadFromStorage();
            }}
            onExport={(summaryData) => {
              console.log('导出公式配置页面汇总表数据:', summaryData);
              message.success('汇总表数据已准备导出');
            }}
          />
        )}

        {!showSummaryTable && (
          /* 原始公式配置表格视图 */
          Object.entries(customerData).map(([groupKey, data], index) => {
          const groupColors = [
            '#1890ff', '#52c41a', '#faad14', '#f5222d',
            '#722ed1', '#13c2c2', '#eb2f96'
          ];
          const groupColor = groupColors[index % groupColors.length];

          return (
            <Card
              key={groupKey}
              title={
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      background: groupColor,
                      color: 'white',
                      padding: '4px 12px',
                      borderRadius: '16px',
                      fontSize: '14px',
                      fontWeight: 'bold'
                    }}>
                      📋 公式组 {index + 1}
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {data.length} 个客户记录
                    </Text>
                  </div>
                  <Button
                    type="primary"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => addCustomer(groupKey)}
                    style={{
                      background: groupColor,
                      borderColor: groupColor,
                      boxShadow: `0 2px 4px ${groupColor}40`
                    }}
                  >
                    添加客户
                  </Button>
                </div>
              }
              style={{
                marginBottom: '20px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                borderRadius: '8px',
                border: `1px solid ${groupColor}20`
              }}
              styles={{
                header: {
                  background: `${groupColor}08`,
                  borderBottom: `2px solid ${groupColor}20`
                }
              }}
            >
              <Table
                dataSource={data}
                columns={getTableColumns(groupKey)}
                pagination={false}
                size="small"
                scroll={{ x: 'max-content' }}
                bordered
                style={{
                  borderRadius: '6px',
                  overflow: 'hidden'
                }}
              />
            </Card>
          );
        })
        )}
      </div>

      {/* 公式编辑模态框 */}
      <FormulaEditModal
        open={editModalVisible}
        formula={editingFormula}
        expression={editingFormula ? formulaExpressions[editingFormula] : ''}
        onSave={handleFormulaSave}
        onCancel={handleFormulaCancel}
      />
      </div>
    </>
  );
}
