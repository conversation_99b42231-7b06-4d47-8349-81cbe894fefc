'use client';

// 新布局汇总表组件 - 根据截图要求实现
// 布局：右上角标签切换，左侧客户列表，中间备注，右侧详细表

import { useState, useEffect, useMemo } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Input,
  Typography,
  Row,
  Col,
  Tabs,
  List,
  Popconfirm,
  message,
  Divider
} from 'antd';
import { 
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { FormulaPageConsolidationCalculations } from '../lib/formulaPageConsolidationCalculations';

const { Title, Text } = Typography;
const { TextArea } = Input;

export default function NewLayoutSummaryTable({
  customerData = {},
  loading = false,
  onRefresh,
  onExport
}) {
  // 状态管理
  const [activeTab, setActiveTab] = useState('weekly'); // 'weekly' 或 'monthly'
  const [remarks, setRemarks] = useState('');
  const [isEditingRemarks, setIsEditingRemarks] = useState(false);
  const [consolidatedData, setConsolidatedData] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  // 计算归纳数据
  const calculatedData = useMemo(() => {
    console.log('🧮 NewLayoutSummaryTable: 开始计算归纳数据', customerData);
    try {
      const result = FormulaPageConsolidationCalculations.consolidateFormulaPageData(
        customerData,
        {}
      );
      console.log('✅ NewLayoutSummaryTable: 计算归纳数据成功', result);
      return result;
    } catch (error) {
      console.error('❌ NewLayoutSummaryTable: 计算归纳数据时出错', error);
      return FormulaPageConsolidationCalculations.getEmptyConsolidatedData();
    }
  }, [customerData]);

  // 更新计算结果数据
  useEffect(() => {
    setConsolidatedData(calculatedData.consolidatedCustomers);
  }, [calculatedData]);

  // 准备客户列表数据
  const customerList = useMemo(() => {
    const list = [];
    Object.entries(calculatedData.consolidatedByUser || {}).forEach(([customerName, records]) => {
      const totalSum = records.reduce((sum, record) => sum + (record.roundedSum || 0), 0);
      list.push({
        customerName,
        totalSum,
        recordCount: records.length,
        records
      });
    });
    return list.sort((a, b) => a.customerName.localeCompare(b.customerName));
  }, [calculatedData]);

  // 保存备注
  const handleSaveRemarks = () => {
    try {
      localStorage.setItem('summaryTableRemarks', remarks);
      setIsEditingRemarks(false);
      message.success('备注保存成功！');
    } catch (error) {
      message.error('备注保存失败！');
    }
  };

  // 加载备注
  useEffect(() => {
    try {
      const savedRemarks = localStorage.getItem('summaryTableRemarks') || '';
      setRemarks(savedRemarks);
    } catch (error) {
      console.error('加载备注失败:', error);
    }
  }, []);

  // 删除客户
  const handleDeleteCustomer = (customerName) => {
    // 这里应该调用删除API，暂时只显示消息
    message.success(`客户 ${customerName} 删除成功！`);
    if (onRefresh) {
      onRefresh();
    }
  };

  // 详细表列定义
  const getDetailColumns = () => {
    return [
      {
        title: '详细表',
        dataIndex: 'customerName',
        key: 'customerName',
        width: 120,
        render: (text, record) => (
          <Text strong style={{ color: '#1890ff' }}>
            {text}
          </Text>
        )
      },
      {
        title: '删除',
        key: 'action',
        width: 80,
        render: (_, record) => (
          <Popconfirm
            title="确定删除这个客户吗？"
            onConfirm={() => handleDeleteCustomer(record.customerName)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        )
      }
    ];
  };

  // 标签页配置
  const tabItems = [
    {
      key: 'weekly',
      label: (
        <span style={{
          backgroundColor: activeTab === 'weekly' ? '#faad14' : '#f0f0f0',
          color: activeTab === 'weekly' ? 'white' : '#666',
          padding: '4px 12px',
          borderRadius: '4px',
          fontWeight: 'bold'
        }}>
          周报表
        </span>
      ),
      children: null
    },
    {
      key: 'monthly',
      label: (
        <span style={{
          backgroundColor: activeTab === 'monthly' ? '#f5222d' : '#f0f0f0',
          color: activeTab === 'monthly' ? 'white' : '#666',
          padding: '4px 12px',
          borderRadius: '4px',
          fontWeight: 'bold'
        }}>
          月报表
        </span>
      ),
      children: null
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 右上角标签切换 */}
      <div style={{ 
        position: 'absolute', 
        top: '24px', 
        right: '24px', 
        zIndex: 1000 
      }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="small"
        />
      </div>

      {/* 主要内容区域 */}
      <Row gutter={16} style={{ marginTop: '60px' }}>
        {/* 左侧：客户名和总数列表 */}
        <Col span={6}>
          <Card 
            title="客户名" 
            size="small"
            style={{ height: '500px' }}
            bodyStyle={{ padding: '8px' }}
          >
            <List
              size="small"
              dataSource={customerList}
              renderItem={(item) => (
                <List.Item
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    backgroundColor: selectedCustomer === item.customerName ? '#e6f7ff' : 'transparent',
                    borderRadius: '4px',
                    marginBottom: '4px'
                  }}
                  onClick={() => setSelectedCustomer(item.customerName)}
                >
                  <div style={{ width: '100%' }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <Text strong>{item.customerName}</Text>
                      <Text style={{ color: '#666', fontSize: '12px' }}>
                        {FormulaPageConsolidationCalculations.formatNumber(item.totalSum, 0, true)}
                      </Text>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 中间：备注区域 */}
        <Col span={12}>
          <Card 
            title="备注" 
            size="small"
            style={{ height: '500px' }}
            extra={
              <Space>
                {isEditingRemarks ? (
                  <>
                    <Button 
                      type="primary" 
                      size="small" 
                      icon={<SaveOutlined />}
                      onClick={handleSaveRemarks}
                    >
                      保存
                    </Button>
                    <Button 
                      size="small" 
                      onClick={() => setIsEditingRemarks(false)}
                    >
                      取消
                    </Button>
                  </>
                ) : (
                  <Button 
                    type="dashed" 
                    size="small" 
                    icon={<EditOutlined />}
                    onClick={() => setIsEditingRemarks(true)}
                  >
                    编辑
                  </Button>
                )}
              </Space>
            }
          >
            {isEditingRemarks ? (
              <TextArea
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
                placeholder="请输入备注信息..."
                style={{ height: '400px', resize: 'none' }}
              />
            ) : (
              <div style={{ 
                height: '400px', 
                overflow: 'auto',
                padding: '8px',
                backgroundColor: '#fafafa',
                borderRadius: '4px',
                whiteSpace: 'pre-wrap'
              }}>
                {remarks || '暂无备注信息'}
              </div>
            )}
          </Card>
        </Col>

        {/* 右侧：详细表和删除功能 */}
        <Col span={6}>
          <Card 
            title="详细表" 
            size="small"
            style={{ height: '500px' }}
            bodyStyle={{ padding: '8px' }}
          >
            <Table
              columns={getDetailColumns()}
              dataSource={customerList}
              loading={loading}
              rowKey="customerName"
              pagination={false}
              size="small"
              scroll={{ y: 400 }}
              rowClassName={(record) => 
                selectedCustomer === record.customerName ? 'selected-row' : ''
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 底部操作区域 */}
      <div style={{ 
        marginTop: '24px', 
        textAlign: 'center',
        padding: '16px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Space size="large">
          <Button 
            icon={<ReloadOutlined />} 
            onClick={onRefresh}
            loading={loading}
          >
            刷新数据
          </Button>
          <Button 
            type="primary"
            onClick={onExport}
          >
            导出数据
          </Button>
        </Space>
      </div>

      {/* 自定义样式 */}
      <style jsx>{`
        .selected-row {
          background-color: #e6f7ff !important;
        }
        .selected-row:hover {
          background-color: #bae7ff !important;
        }
      `}</style>
    </div>
  );
}
