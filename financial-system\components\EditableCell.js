'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Input, InputNumber } from 'antd';

/**
 * 可编辑单元格组件
 * 支持直接在单元格内编辑，提供良好的用户体验
 */
const EditableCell = ({
  value,
  onSave,
  onCancel,
  type = 'text', // 'text', 'number', 'percentage'
  placeholder = '',
  style = {},
  className = '',
  disabled = false,
  maxLength,
  precision = 2,
  formatter,
  parser,
  cellKey = '', // 用于Tab键切换
  onTabNext,
  onTabPrev
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [originalValue, setOriginalValue] = useState(value);
  const inputRef = useRef(null);

  // 当外部value变化时，更新内部状态
  useEffect(() => {
    if (!isEditing) {
      setEditValue(value);
      setOriginalValue(value);
    }
  }, [value, isEditing]);

  // 进入编辑模式
  const startEdit = () => {
    if (disabled) return;
    
    setIsEditing(true);
    setEditValue(value);
    setOriginalValue(value);
  };

  // 保存编辑
  const saveEdit = () => {
    const finalValue = type === 'number' ? (parseFloat(editValue) || 0) : editValue;
    
    // 验证数值类型
    if (type === 'number' && isNaN(finalValue)) {
      // 如果是无效数字，恢复原值
      setEditValue(originalValue);
      setIsEditing(false);
      return;
    }

    setIsEditing(false);
    
    // 只有值真正改变时才调用onSave
    if (finalValue !== originalValue) {
      onSave && onSave(finalValue);
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditValue(originalValue);
    setIsEditing(false);
    onCancel && onCancel();
  };

  // 键盘事件处理
  const handleKeyDown = (e) => {
    switch (e.key) {
      case 'Enter':
        e.preventDefault();
        saveEdit();
        break;
      case 'Escape':
        e.preventDefault();
        cancelEdit();
        break;
      case 'Tab':
        e.preventDefault();
        saveEdit();
        // Tab键切换到下一个单元格
        if (e.shiftKey) {
          onTabPrev && onTabPrev(cellKey);
        } else {
          onTabNext && onTabNext(cellKey);
        }
        break;
      default:
        break;
    }
  };

  // 失去焦点时保存
  const handleBlur = () => {
    saveEdit();
  };

  // 格式化显示值
  const formatDisplayValue = (val) => {
    if (formatter && typeof formatter === 'function') {
      return formatter(val);
    }
    
    if (type === 'number') {
      if (typeof val === 'number' && !isNaN(val)) {
        return val.toString();
      }
      return val || '0';
    }
    
    if (type === 'percentage') {
      if (typeof val === 'number' && !isNaN(val)) {
        return `${val}%`;
      }
      return val || '0%';
    }
    
    return val || '';
  };

  // 编辑模式下的输入组件
  const renderEditInput = () => {
    const commonProps = {
      ref: inputRef,
      value: editValue,
      onKeyDown: handleKeyDown,
      onBlur: handleBlur,
      placeholder,
      maxLength,
      style: {
        width: '100%',
        border: '2px solid #1890ff',
        borderRadius: '6px',
        padding: '6px 10px',
        fontSize: '14px',
        backgroundColor: '#f0f8ff',
        boxShadow: '0 0 8px rgba(24, 144, 255, 0.3)',
        transition: 'all 0.2s ease'
      },
      autoFocus: true
    };

    if (type === 'number') {
      return (
        <InputNumber
          {...commonProps}
          onChange={setEditValue}
          precision={precision}
          parser={parser}
          style={{
            ...commonProps.style,
            textAlign: 'right'
          }}
        />
      );
    }

    return (
      <Input
        {...commonProps}
        onChange={(e) => setEditValue(e.target.value)}
        style={{
          ...commonProps.style,
          textAlign: type === 'number' ? 'right' : 'left'
        }}
      />
    );
  };

  // 显示模式下的内容
  const renderDisplayContent = () => {
    const displayValue = formatDisplayValue(value);
    
    return (
      <div
        className={`editable-cell-display ${className}`}
        style={{
          padding: '6px 10px',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          backgroundColor: disabled ? '#f5f5f5' : '#ffffff',
          cursor: disabled ? 'not-allowed' : 'pointer',
          textAlign: type === 'number' ? 'right' : 'left',
          minHeight: '36px',
          display: 'flex',
          alignItems: 'center',
          transition: 'all 0.3s ease',
          fontSize: '14px',
          fontWeight: '400',
          color: disabled ? '#999' : '#333',
          position: 'relative',
          ...style
        }}
        onClick={startEdit}
        onMouseEnter={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = '#f8f9fa';
            e.target.style.borderColor = '#40a9ff';
            e.target.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.15)';
            e.target.style.transform = 'translateY(-1px)';
          }
        }}
        onMouseLeave={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = '#ffffff';
            e.target.style.borderColor = '#d9d9d9';
            e.target.style.boxShadow = 'none';
            e.target.style.transform = 'translateY(0)';
          }
        }}
        title={disabled ? '此字段不可编辑' : '点击编辑'}
      >
        <span style={{ flex: 1 }}>
          {displayValue || placeholder}
        </span>
        {!disabled && (
          <span
            style={{
              opacity: 0,
              fontSize: '12px',
              color: '#999',
              marginLeft: '4px',
              transition: 'opacity 0.2s ease'
            }}
            className="edit-indicator"
          >
            ✏️
          </span>
        )}
      </div>
    );
  };

  // 编辑模式自动聚焦
  useEffect(() => {
    if (isEditing && inputRef.current) {
      // 延迟聚焦，确保DOM已更新
      setTimeout(() => {
        inputRef.current.focus();
        // 如果是文本输入，选中所有内容
        if (inputRef.current.select) {
          inputRef.current.select();
        }
      }, 0);
    }
  }, [isEditing]);

  return (
    <div className="editable-cell-wrapper" style={{ width: '100%' }}>
      {isEditing ? renderEditInput() : renderDisplayContent()}
      <style jsx>{`
        .editable-cell-wrapper:hover .edit-indicator {
          opacity: 1 !important;
        }
        .editable-cell-display:focus-within {
          border-color: #1890ff !important;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
        }
      `}</style>
    </div>
  );
};

export default EditableCell;
