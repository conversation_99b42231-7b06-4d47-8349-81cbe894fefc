# 财务管理系统 - 公式编辑与数据同步功能 PRD

## 1. 文档信息
- **项目名称**: 财务管理系统 - 公式编辑与数据同步功能
- **版本**: v1.0
- **创建日期**: 2025年7月30日
- **负责人**: Emma (产品经理)
- **状态**: 开发中

## 2. 背景与问题陈述

### 2.1 当前问题
用户在使用财务管理系统时，发现公式和数据字段的名称都是系统预设的固定标签（如"公式1.1"、"数据1"等），无法根据实际业务场景进行自定义命名，导致：

1. **业务语义不清晰**：固定标签无法体现实际业务含义
2. **用户体验差**：无法使用符合业务习惯的术语
3. **数据理解困难**：其他用户难以理解数据字段的实际含义
4. **跨报表不一致**：修改后无法在各报表间保持同步
5. **缺少汇总表植入**：保存时无法自动植入到汇总表

### 2.2 解决目标
实现公式名称和数据字段名称的可编辑功能，并确保修改后的名称在所有相关报表中保持同步，同时提供便捷的汇总表植入功能。

## 3. 目标与成功指标

### 3.1 项目目标
- **主要目标**: 实现公式和字段名称的完全自定义化
- **次要目标**: 建立跨模块数据同步机制
- **用户体验目标**: 提供直观的编辑界面和颜色标识

### 3.2 关键结果(KRs)
- 用户可以100%自定义公式名称和表达式
- 字段名称修改后，相关模块100%同步更新
- 汇总表数据植入成功率达到100%
- 界面颜色分组覆盖率达到100%

### 3.3 反向指标
- 系统响应时间不超过当前基准的120%
- 数据同步错误率控制在0.1%以下

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 财务管理员、数据分析师
- **使用场景**: 日常财务数据录入、报表生成、公式配置

### 4.2 用户故事
**作为** 财务管理员
**我希望** 能够自定义公式名称和计算表达式
**以便于** 根据不同业务场景配置个性化的计算规则

**作为** 数据录入员
**我希望** 能够修改数据字段的显示名称
**以便于** 更好地理解和识别不同的数据含义

**作为** 系统管理员
**我希望** 字段名称修改后能自动同步到相关模块
**以便于** 减少手动维护工作和避免数据不一致

## 5. 功能规格详述

### 5.1 公式编辑器功能

#### 5.1.1 功能描述
- 提供可视化公式编辑界面
- 支持自定义公式名称（如"公式1.1"改为"利润计算"）
- 支持手动输入公式表达式
- 提供公式语法验证和错误提示

#### 5.1.2 技术规格
```javascript
// 公式配置数据结构
{
  formulaId: "formula_1_1",
  customName: "利润计算", // 用户自定义名称
  expression: "(data1+data2*data4)*data3", // 用户输入表达式
  description: "计算净利润", // 公式描述
  isActive: true,
  lastModified: "2025-07-29T10:00:00Z"
}
```

#### 5.1.3 界面设计
- 公式名称输入框（可编辑）
- 公式表达式输入框（支持语法高亮）
- 实时预览计算结果
- 保存/重置按钮

### 5.2 字段名称编辑功能

#### 5.2.1 功能描述
- 所有数据字段名称可编辑
- 支持中文、英文、数字组合
- 字段名称长度限制：2-20个字符
- 提供默认名称和自定义名称切换

#### 5.2.2 数据结构
```javascript
// 字段名称配置
{
  fieldId: "data1_1",
  defaultName: "数据1",
  customName: "总收入", // 用户自定义名称
  fieldType: "currency", // 字段类型
  moduleId: "module1_1"
}
```

### 5.3 跨模块数据同步机制

#### 5.3.1 同步规则
- 数据1名称修改时，自动同步到：
  - 公式2-7中的相关引用
  - 周报表对应字段
  - 月报表对应字段
  - 汇总表显示名称

#### 5.3.2 同步流程
1. 用户修改字段名称
2. 系统检测关联模块
3. 批量更新相关引用
4. 实时刷新界面显示
5. 记录同步日志

### 5.4 汇总表数据植入功能

#### 5.4.1 植入时机
- 用户点击"保存"按钮时
- 数据发生变更时（可选）
- 定时同步（可配置）

#### 5.4.2 植入逻辑
```javascript
// 数据植入流程
const plantDataToSummary = (moduleData) => {
  // 1. 数据验证
  // 2. 格式转换
  // 3. 插入汇总表
  // 4. 更新统计信息
}
```

### 5.5 UI界面优化

#### 5.5.1 颜色分组标识
- **黄色区域**: 公式配置区域
- **蓝色区域**: 数据输入区域  
- **绿色区域**: 计算结果区域
- **橙色区域**: 统计汇总区域

#### 5.5.2 布局优化
- 模块化分组显示
- 响应式布局适配
- 操作按钮统一样式
- 加载状态提示

## 6. 范围定义

### 6.1 包含功能(In Scope)
✅ 公式名称和表达式自定义编辑
✅ 数据字段名称自定义编辑
✅ 跨模块字段名称同步
✅ 汇总表数据自动植入
✅ 界面颜色分组优化
✅ 数据验证和错误处理

### 6.2 排除功能(Out of Scope)
❌ 复杂数学函数支持（如三角函数）
❌ 历史版本回滚功能
❌ 批量导入导出功能
❌ 权限管理功能

## 7. 依赖与风险

### 7.1 内部依赖
- 现有模块配置系统(moduleConfigs.js)
- 计算引擎(calculations.js)
- 数据库模型(Prisma Schema)

### 7.2 外部依赖
- Antd组件库（表单编辑器）
- Zustand状态管理
- 无新增外部依赖

### 7.3 潜在风险
- **技术风险**: 公式解析可能存在安全漏洞
- **数据风险**: 跨模块同步可能导致数据不一致
- **性能风险**: 实时同步可能影响系统响应速度

### 7.4 风险缓解策略
- 实施公式表达式白名单验证
- 建立数据同步事务机制
- 添加性能监控和优化

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**: 公式编辑器开发 (2天)
- **阶段2**: 字段名称编辑功能 (1天)
- **阶段3**: 数据同步机制 (2天)
- **阶段4**: 汇总表植入功能 (1天)
- **阶段5**: UI界面优化 (1天)

### 8.2 测试计划
- 单元测试覆盖率 > 80%
- 集成测试验证数据同步
- 用户验收测试

### 8.3 上线计划
- 灰度发布：内部用户测试
- 全量发布：所有用户可用
- 数据监控：实时监控同步状态

---

**文档状态**: ✅ 已完成
**下一步**: 技术架构设计和开发任务分配
