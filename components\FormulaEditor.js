'use client';

// FormulaEditor 组件 - 公式编辑器
// 支持公式表达式编辑、语法验证和变量提示

import React, { useState, useRef, useEffect } from 'react';
import { Input, Button, message, Tooltip, Tag, Space, Popover } from 'antd';
import { 
  EditOutlined, 
  CheckOutlined, 
  CloseOutlined, 
  FunctionOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';

const { TextArea } = Input;

const FormulaEditor = ({
  formula = '',                // 当前公式表达式
  formulaName = '',           // 公式名称
  variables = [],             // 可用变量列表 [{ id: 'data1_1', name: '数据1', type: 'number' }]
  onFormulaChange,            // 公式变更回调 (newFormula) => void
  onNameChange,               // 名称变更回调 (newName) => void
  onSave,                     // 保存回调 (formulaData) => Promise<boolean>
  onCancel,                   // 取消回调
  readOnly = false,           // 是否只读
  showPreview = true,         // 是否显示预览
  style = {},                 // 自定义样式
  className = ''              // 自定义类名
}) => {
  // 状态管理
  const [isEditing, setIsEditing] = useState(false);
  const [tempFormula, setTempFormula] = useState(formula);
  const [tempName, setTempName] = useState(formulaName);
  const [isValid, setIsValid] = useState(true);
  const [validationError, setValidationError] = useState('');
  const [previewResult, setPreviewResult] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  
  // 引用
  const formulaInputRef = useRef(null);
  const nameInputRef = useRef(null);
  
  // 同步外部变化
  useEffect(() => {
    if (!isEditing) {
      setTempFormula(formula);
      setTempName(formulaName);
    }
  }, [formula, formulaName, isEditing]);
  
  // 验证公式表达式
  const validateFormula = (formulaText) => {
    try {
      // 基本语法检查
      if (!formulaText.trim()) {
        setIsValid(false);
        setValidationError('公式不能为空');
        return false;
      }
      
      // 检查括号匹配
      const openBrackets = (formulaText.match(/\(/g) || []).length;
      const closeBrackets = (formulaText.match(/\)/g) || []).length;
      if (openBrackets !== closeBrackets) {
        setIsValid(false);
        setValidationError('括号不匹配');
        return false;
      }
      
      // 检查变量是否存在
      const variablePattern = /data\d+_\d+/g;
      const usedVariables = formulaText.match(variablePattern) || [];
      const availableVariableIds = variables.map(v => v.id);
      
      for (const variable of usedVariables) {
        if (!availableVariableIds.includes(variable)) {
          setIsValid(false);
          setValidationError(`未知变量: ${variable}`);
          return false;
        }
      }
      
      // 检查操作符
      const invalidOperators = formulaText.match(/[^+\-*/().\d\s\w]/g);
      if (invalidOperators) {
        setIsValid(false);
        setValidationError(`不支持的操作符: ${invalidOperators.join(', ')}`);
        return false;
      }
      
      setIsValid(true);
      setValidationError('');
      return true;
    } catch (error) {
      setIsValid(false);
      setValidationError('公式语法错误');
      return false;
    }
  };
  
  // 计算预览结果
  const calculatePreview = (formulaText) => {
    if (!showPreview || !isValid) {
      setPreviewResult(null);
      return;
    }
    
    try {
      // 使用示例数据计算预览
      let previewFormula = formulaText;
      variables.forEach(variable => {
        const sampleValue = variable.type === 'percentage' ? 0.1 : 1000;
        previewFormula = previewFormula.replace(
          new RegExp(variable.id, 'g'), 
          sampleValue.toString()
        );
      });
      
      // 安全计算（仅支持基本数学运算）
      const result = Function(`"use strict"; return (${previewFormula})`)();
      setPreviewResult(result);
    } catch (error) {
      setPreviewResult('计算错误');
    }
  };
  
  // 开始编辑
  const startEdit = () => {
    if (readOnly) return;
    
    setIsEditing(true);
    setTempFormula(formula);
    setTempName(formulaName);
    setIsValid(true);
    setValidationError('');
    
    // 延迟聚焦，确保DOM已更新
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
      }
    }, 100);
  };
  
  // 取消编辑
  const cancelEdit = () => {
    setIsEditing(false);
    setTempFormula(formula);
    setTempName(formulaName);
    setIsValid(true);
    setValidationError('');
    setPreviewResult(null);
    
    if (onCancel) {
      onCancel();
    }
  };
  
  // 保存编辑
  const saveEdit = async () => {
    const trimmedFormula = tempFormula.trim();
    const trimmedName = tempName.trim();
    
    // 验证名称
    if (!trimmedName) {
      message.error('公式名称不能为空');
      return;
    }
    
    // 验证公式
    if (!validateFormula(trimmedFormula)) {
      return;
    }
    
    setIsSaving(true);
    
    try {
      const formulaData = {
        name: trimmedName,
        expression: trimmedFormula,
        variables: variables.filter(v => trimmedFormula.includes(v.id))
      };
      
      const success = await onSave(formulaData);
      
      if (success) {
        setIsEditing(false);
        message.success('公式保存成功');
        
        // 通知父组件
        if (onFormulaChange) onFormulaChange(trimmedFormula);
        if (onNameChange) onNameChange(trimmedName);
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败: ' + error.message);
    } finally {
      setIsSaving(false);
    }
  };
  
  // 公式输入变化处理
  const handleFormulaChange = (e) => {
    const newFormula = e.target.value;
    setTempFormula(newFormula);
    setCursorPosition(e.target.selectionStart);
    
    // 实时验证和预览
    if (validateFormula(newFormula)) {
      calculatePreview(newFormula);
    }
  };
  
  // 插入变量
  const insertVariable = (variable) => {
    const input = formulaInputRef.current;
    if (!input) return;
    
    const start = cursorPosition;
    const end = cursorPosition;
    const newFormula = tempFormula.slice(0, start) + variable.id + tempFormula.slice(end);
    
    setTempFormula(newFormula);
    
    // 更新光标位置
    setTimeout(() => {
      const newPosition = start + variable.id.length;
      input.setSelectionRange(newPosition, newPosition);
      setCursorPosition(newPosition);
    }, 0);
  };
  
  // 插入操作符
  const insertOperator = (operator) => {
    const input = formulaInputRef.current;
    if (!input) return;
    
    const start = cursorPosition;
    const end = cursorPosition;
    const newFormula = tempFormula.slice(0, start) + operator + tempFormula.slice(end);
    
    setTempFormula(newFormula);
    
    setTimeout(() => {
      const newPosition = start + operator.length;
      input.setSelectionRange(newPosition, newPosition);
      setCursorPosition(newPosition);
    }, 0);
  };
  
  // 渲染变量选择器
  const renderVariableSelector = () => (
    <div style={{ maxWidth: '300px' }}>
      <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>可用变量:</div>
      <Space wrap>
        {variables.map(variable => (
          <Tag
            key={variable.id}
            color="blue"
            style={{ cursor: 'pointer' }}
            onClick={() => insertVariable(variable)}
          >
            {variable.name} ({variable.id})
          </Tag>
        ))}
      </Space>
      <div style={{ marginTop: '8px', marginBottom: '8px', fontWeight: 'bold' }}>操作符:</div>
      <Space>
        {['+', '-', '*', '/', '(', ')'].map(op => (
          <Button
            key={op}
            size="small"
            onClick={() => insertOperator(op)}
          >
            {op}
          </Button>
        ))}
      </Space>
    </div>
  );
  
  // 渲染编辑模式
  if (isEditing) {
    return (
      <div className={`formula-editor-editing ${className}`} style={style}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 公式名称输入 */}
          <Input
            ref={nameInputRef}
            value={tempName}
            onChange={(e) => setTempName(e.target.value)}
            placeholder="输入公式名称"
            prefix={<FunctionOutlined />}
            style={{ marginBottom: '8px' }}
          />
          
          {/* 公式表达式输入 */}
          <div style={{ position: 'relative' }}>
            <TextArea
              ref={formulaInputRef}
              value={tempFormula}
              onChange={handleFormulaChange}
              onSelect={(e) => setCursorPosition(e.target.selectionStart)}
              placeholder="输入公式表达式，如: (data1_1+data1_2)*data1_3"
              rows={3}
              status={!isValid ? 'error' : ''}
              style={{
                fontFamily: 'monospace',
                border: isValid ? '2px solid #1890ff' : '2px solid #ff4d4f'
              }}
            />
            
            {/* 变量选择器 */}
            <Popover
              content={renderVariableSelector()}
              title="插入变量或操作符"
              trigger="click"
              placement="bottomRight"
            >
              <Button
                size="small"
                icon={<InfoCircleOutlined />}
                style={{
                  position: 'absolute',
                  top: '8px',
                  right: '8px',
                  zIndex: 10
                }}
              >
                变量
              </Button>
            </Popover>
          </div>
          
          {/* 错误提示 */}
          {!isValid && validationError && (
            <div style={{ 
              color: '#ff4d4f', 
              fontSize: '12px',
              padding: '4px 8px',
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: '4px'
            }}>
              {validationError}
            </div>
          )}
          
          {/* 预览结果 */}
          {showPreview && isValid && previewResult !== null && (
            <div style={{
              padding: '8px',
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              预览结果: {previewResult}
            </div>
          )}
          
          {/* 操作按钮 */}
          <Space>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={saveEdit}
              loading={isSaving}
              disabled={!isValid || !tempName.trim()}
            >
              保存
            </Button>
            <Button
              icon={<CloseOutlined />}
              onClick={cancelEdit}
            >
              取消
            </Button>
          </Space>
        </Space>
      </div>
    );
  }
  
  // 渲染显示模式
  return (
    <div className={`formula-editor-display ${className}`} style={style}>
      <Tooltip 
        title={readOnly ? '只读模式' : '点击编辑公式'}
        placement="top"
      >
        <div
          onClick={startEdit}
          style={{
            cursor: readOnly ? 'default' : 'pointer',
            padding: '8px 12px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            backgroundColor: readOnly ? '#f5f5f5' : '#fafafa',
            transition: 'all 0.2s',
            minHeight: '40px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
          className={readOnly ? '' : 'formula-editor-hover'}
        >
          <FunctionOutlined style={{ color: '#1890ff' }} />
          <div style={{ flex: 1 }}>
            <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
              {formulaName || '未命名公式'}
            </div>
            <div style={{ 
              fontSize: '12px', 
              color: '#666',
              fontFamily: 'monospace',
              wordBreak: 'break-all'
            }}>
              {formula || '点击编辑公式'}
            </div>
          </div>
          {!readOnly && (
            <EditOutlined style={{ color: '#8c8c8c', fontSize: '14px' }} />
          )}
        </div>
      </Tooltip>
      
      <style jsx>{`
        .formula-editor-hover:hover {
          border-color: #40a9ff !important;
          background-color: #e6f7ff !important;
        }
      `}</style>
    </div>
  );
};

export default FormulaEditor;
