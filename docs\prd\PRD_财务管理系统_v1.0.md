# 产品需求文档 (PRD) - 财务管理系统

## 1. 文档信息
- **项目名称**: 财务管理系统（代理商盈亏跟踪器）
- **版本**: v1.0
- **创建日期**: 2025年7月27日
- **负责人**: Emma (产品经理)
- **文档状态**: 初版完成

## 2. 背景与问题陈述

### 2.1 项目背景
当前代理商财务管理存在以下痛点：
- 代理商数据分散，缺乏统一管理平台
- 财务计算复杂，容易出错
- 周期性报表生成效率低下
- 缺乏实时的盈亏跟踪机制

### 2.2 解决方案
构建一个基于Web的财务管理系统，提供：
- 统一的代理商数据管理
- 自动化的财务计算引擎
- 多维度的报表生成
- 实时的盈亏跟踪和分析

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **提升效率**: 财务数据处理效率提升80%
2. **降低错误**: 计算错误率降低至0.1%以下
3. **增强可视化**: 提供直观的数据展示和分析

### 3.2 关键结果 (Key Results)
- 支持100+代理商同时在线操作
- 报表生成时间控制在5秒内
- 系统可用性达到99.9%
- 用户满意度达到4.5/5.0

### 3.3 反向指标 (Counter Metrics)
- 系统响应时间不超过3秒
- 数据丢失率为0
- 安全漏洞数量为0

## 4. 用户画像与用户故事

### 4.1 目标用户
**主要用户**: 管理员/代理商
- 年龄: 25-45岁
- 技能: 基础计算机操作能力
- 需求: 高效、准确的财务数据管理

### 4.2 用户故事
1. **作为管理员**，我希望能够安全登录系统，以便管理代理商数据
2. **作为代理商**，我希望能够录入客户数据，以便跟踪业务情况
3. **作为管理员**，我希望能够查看周/年利润报表，以便做出业务决策
4. **作为代理商**，我希望系统能够自动计算复杂公式，以便减少人工错误

## 5. 功能规格详述

### 5.1 登录模块
**功能描述**: 用户身份验证和会话管理

**业务流程**:
1. 用户输入账号和密码
2. 系统验证凭据
3. 验证成功后跳转到主仪表盘
4. 验证失败显示错误提示

**技术要求**:
- 密码加密存储
- 会话管理
- 支持键盘回车登录
- 错误处理机制

### 5.2 主仪表盘模块
**功能描述**: 系统核心导航和数据录入界面

**核心功能**:
- 日期选择器
- 备注输入（最大30字符）
- 代理商数据表格
- 报表导航链接
- 数据操作按钮

**数据表格字段**:
- 代理名/客户名/用户名（可编辑）
- 日期编辑1（1-3期）
- 日期编辑2（4-7期）
- 周期选择
- 代理商数据录入表格链接
- 删除代理按钮

### 5.3 公司周利润表
**功能描述**: 展示公司层面的周利润数据概览

**数据结构**:
- 日期范围显示（如：7.22-7.28）
- 代理名/下客户列
- 总额列（自动计算）
- 成本列（自动计算）
- 多期数据列（第1期至第12期）
- 合计列（汇总所有期间）
- 总计行（所有代理商汇总）

### 5.4 公司年利润表
**功能描述**: 展示公司层面的年利润数据概览

**数据结构**:
- 日期范围显示
- 代理名/下客户列
- 余额列
- 减免列
- 本周利润列
- 合计列
- 总计行

### 5.5 汇总表/客户数据编辑表
**功能描述**: 特定代理商的详细客户数据编辑界面

**核心特性**:
1. **全局公式系统**: 支持复杂公式定义和计算
2. **多周期管理**: 支持多个独立的日期周期
3. **客户数据表格**: 包含8种不同类型的数据字段
4. **实时计算**: 自动计算和更新相关结果
5. **数据汇总**: 右侧总计区域和底部汇总

**数据字段详述**:
- 数据1-2: 整数输入（千万/十亿级别）
- 数据3,5: 百分比输入（保留2位小数）
- 数据4: 数字输入（保留8位小数）
- 数据6-8: 只读计算结果

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 用户登录认证
- 代理商数据管理
- 复杂公式计算引擎
- 多维度报表生成
- 数据导入导出
- 自动排序功能
- 实时数据计算

### 6.2 排除功能 (Out of Scope)
- 移动端适配（v1.0版本）
- 多语言支持
- 高级权限管理
- 数据备份恢复
- API接口开放

## 7. 依赖与风险

### 7.1 技术依赖
- 前端框架: Next.js + React
- 后端服务: Node.js + Express
- 数据库: PostgreSQL
- 认证: JWT Token
- 部署: Context7环境

### 7.2 潜在风险
- **高风险**: 复杂公式计算引擎的实现难度
- **中风险**: 大量数据的性能优化
- **低风险**: 用户界面的易用性

### 7.3 风险缓解策略
- 分阶段实现公式引擎功能
- 采用数据分页和缓存策略
- 进行用户体验测试和优化

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**: 基础框架搭建（1周）
- **阶段2**: 核心功能开发（3周）
- **阶段3**: 测试和优化（1周）

### 8.2 发布策略
- **内部测试**: 开发团队测试
- **用户验收**: 关键用户试用
- **正式发布**: 全量上线

### 8.3 数据跟踪
- 用户登录成功率
- 页面加载时间
- 计算准确性
- 用户操作路径分析

## 9. 详细技术规格

### 9.1 公式计算引擎规格
**核心需求**: 实现强大的公式解析和计算引擎

**技术要求**:
- 支持引用特定数据列（数据1-8）
- 支持全局公式和局部公式
- 支持嵌套公式和数学运算符
- 精确处理不同数据类型（整数、百分比、小数）
- 支持四舍五入和格式化显示
- 前端实时计算或后端批量计算

**公式示例**:
- 简单运算: `(数据1 + 数据2) * 数据3`
- 引用公式: `公式1.1 * 公式1.2`
- 复杂表达式: `(c+d*f)*e*g`

### 9.2 数据字段规格详述

**数据1 (千万整数)**:
- 输入类型: 整数
- 显示格式: 小数点后0位，四舍五入
- 支持自动生成

**数据2 (十亿整数)**:
- 输入类型: 整数
- 显示格式: 小数点后0位，四舍五入
- 支持自动生成

**数据3 (百分比)**:
- 输入类型: 百分比数字
- 显示格式: 百分比格式（如10.01%）
- 精度: 小数点后保留2位

**数据4 (高精度数字)**:
- 输入类型: 数字
- 精度: 小数点后保留8位
- 用于精确计算

**数据5 (百分比)**:
- 输入类型: 百分比数字
- 显示格式: 百分比格式（如4.50%）
- 精度: 小数点后保留2位

**数据6 (计算结果)**:
- 类型: 只读计算结果
- 计算逻辑: 数据4 - 数据5
- 显示格式: 小数点后0位，四舍五入

**数据7 (计算结果)**:
- 类型: 只读计算结果
- 计算逻辑: 与百分比或公式相关
- 显示格式: 小数点后0位，四舍五入

**数据8 (计算结果)**:
- 类型: 只读计算结果
- 计算逻辑: 待定义
- 显示格式: 根据计算结果确定

### 9.3 周期管理规格
**多周期支持**:
- 每个大周期包含多个子期
- 一期: 4个子期
- 二期: 3个子期
- 支持自定义日期范围
- 独立的计算上下文

**日期选择器**:
- 每个分组顶部配置
- 支持日期范围选择
- 与计算结果关联

### 9.4 用户界面规格
**表格布局**:
- 蓝色背景区块分隔
- 可编辑字段明确标识
- 只读字段灰色显示
- 计算结果实时更新

**操作按钮**:
- "自动生成": 填充默认数据
- "点击生成结果": 刷新所有计算
- "点击出来汇总表": 生成报告
- "添加客户行": 新增数据行

**排序功能**:
- 用户名、客户名支持字母排序
- 自动排序或手动触发
- 排序状态保持

### 9.5 数据验证规格
**输入验证**:
- 数字字段范围检查
- 百分比格式验证
- 必填字段检查
- 数据类型验证

**计算验证**:
- 公式语法检查
- 循环引用检测
- 除零错误处理
- 溢出保护

**数据一致性**:
- 主仪表盘与汇总表数据同步
- 计算结果与源数据关联
- 总计数据准确性验证

## 10. 非功能性需求

### 10.1 性能要求
- 页面加载时间 < 3秒
- 计算响应时间 < 1秒
- 支持100+并发用户
- 数据库查询优化

### 10.2 安全要求
- JWT Token认证
- 密码加密存储
- 会话超时管理
- 输入数据过滤

### 10.3 可用性要求
- 系统可用性 99.9%
- 错误恢复机制
- 数据备份策略
- 监控告警系统

### 10.4 兼容性要求
- 支持主流浏览器
- 响应式设计
- Context7环境兼容
- 数据导入导出格式
