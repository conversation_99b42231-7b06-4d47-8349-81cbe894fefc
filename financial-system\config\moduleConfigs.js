// 7模块财务管理系统配置文件
// 定义每个模块的字段配置、公式配置和数据结构

export const MODULE_CONFIGS = {
  'module1_1': {
    id: 'module1_1',
    name: 'Module 1.1',
    displayName: '模块 1.1',
    description: '标准财务模块 - 7输入+5结果',
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,
    
    // 字段定义
    fields: {
      // 基础字段
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      
      // 数据输入字段
      data1: { title: '数据1 (千万整数)', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2 (十亿整数)', type: 'number', format: 'integer', editable: true },
      data3: { title: '数据3 (小数格式)', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '数据4 (高精度)', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '数据5 (百分比)', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '数据6 (通用数值)', type: 'number', format: 'decimal', editable: true },
      data7: { title: '数据7 (通用数值)', type: 'number', format: 'decimal', editable: true },
      
      // 计算结果字段
      result1: { title: '结果1 (计算结果)', type: 'number', format: 'result', editable: false },
      result2: { title: '结果2 (计算结果)', type: 'number', format: 'result', editable: false },
      result3: { title: '结果3 (计算结果)', type: 'number', format: 'result', editable: false },
      result4: { title: '结果4 (计算结果)', type: 'number', format: 'result', editable: false },
      result5: { title: '结果5 (计算结果)', type: 'number', format: 'result', editable: false }
    },
    
    // 固定公式配置 - 基于真实业务需求
    fixedFormulas: {
      'income': {
        id: 'income',
        name: '收入计算',
        structure: '(quantityField) * (priceField)',
        description: '收入 = 数量 × 单价',
        parameters: {
          quantityField: 'data1',  // 默认使用data1作为数量字段
          priceField: 'data2'      // 默认使用data2作为单价字段
        },
        resultField: 'result1'
      },
      'expense': {
        id: 'expense',
        name: '付出计算',
        structure: '(quantityField) * (priceField) * (-1)',
        description: '付出 = 数量 × 单价 × (-1)',
        parameters: {
          quantityField: 'data6',  // 默认使用data6作为付出数量
          priceField: 'data7'      // 默认使用data7作为付出单价
        },
        resultField: 'result2'
      },
      'balance': {
        id: 'balance',
        name: '余额计算',
        structure: '(previousBalance) + (income) - (expense) - (deduction)',
        description: '余额 = 上期余额 + 收入 - 付出 - 减免',
        parameters: {
          previousBalance: 500,    // 固定值：上周余额
          deduction: 1000         // 固定值：减免金额
        },
        resultField: 'result3'
      },
      'finalSummary': {
        id: 'finalSummary',
        name: '最终汇总',
        structure: '(income) + (expense) + (balance) + (additionalCalculations)',
        description: '最终汇总 = 所有计算结果的加权求和',
        parameters: {
          // 这个公式会根据所有其他计算结果动态计算
        },
        resultField: 'finalSummaryValue'
      }
    },

    // 修正后的公式配置 - 确保股东公式映射正确
    defaultFormulas: {
      // 主要公式
      '公式1.1': '(data1+data2+data4)+data3',
      '公式1.2': '(data1+data2+data4)+data3',

      // 第1个股东公式 - 修正表达式
      '公式1.31': 'data1*data3+data5',  // 修正：data1 × data3 + data5
      '公式1.32': 'data2*data4+data6',  // 修正：data2 × data4 + data6

      // 第2个股东公式 - 修正表达式
      '公式1.41': 'data1+data2+data3',  // 修正：简单加法
      '公式1.42': 'data4+data5+data6',  // 修正：简单加法

      // 第3个及以上股东公式 - 修正表达式
      '公式1.51': 'data1*data5',        // 修正：data1 × data5
      '公式1.52': 'data2*data6',        // 修正：data2 × data6

      '公式1.5': 'data1+data2+data3+data4+data5',
      '公式2.1': 'data1*data2',
      '公式2.2': 'data2*data4',
      '公式2.31': 'data1*data2*data3',
      '公式2.32': 'data1*data2*data4',
      '公式2.5': 'data1*data2*data3*data4*data5',
      '公式2.41': 'data1*data2*data3*data4',
      '公式2.42': 'data1*data2*data3*data4*data5',
      '公式2.51': 'data1*data2*data3*data4',
      '公式2.52': 'data1*data2*data3*data4*data5',
      '公式3.1': 'data1',
      '公式3.2': 'data2*data4',
      '公式3.3': '',
      '公式3.4': '',
      '公式4.1': 'data1/data2',
      '公式4.2': 'data2/data4',
      '公式4.31': 'data1/data2/data3',
      '公式4.32': 'data1/data2/data4',
      '公式4.5': 'data1/data2/data3/data4/data5',
      '公式4.41': 'data1/data2/data3/data4',
      '公式4.42': 'data1/data2/data3/data4/data5',
      '公式4.51': 'data1/data2/data3/data4',
      '公式4.52': 'data1/data2/data3/data4/data5',
      '公式5.1': 'data1-data2',
      '公式5.2': 'data2-data4',
      '公式5.31': 'data1-data2-data3',
      '公式5.32': 'data1-data2-data4',
      '公式6.1': 'data1^2',
      '公式6.2': 'data2^2',
      '公式6.3': 'data3^2',
      '公式6.41': 'data1^data2',
      '公式6.42': 'data2^data3',
      '公式6.43': 'data3^data4',
      '公式7.1': 'data1',
      '公式7.2': 'data2*data4',
      '公式7.3': 'data3+data5'
    },

    // 新增：自定义名称配置
    customNames: {
      // 公式自定义名称 - 根据截图更新
      formulas: {
        'formula1_1': {
          defaultName: '公式1.1',
          customName: null,
          expression: '(data1+data2+data4)+data3',
          lastModified: null
        },
        'formula1_2': {
          defaultName: '公式1.2',
          customName: null,
          expression: '(data1+data2+data4)+data3',
          lastModified: null
        },
        'formula1_31': {
          defaultName: '公式1.31',
          customName: null,
          expression: 'data1+data2+data3',
          lastModified: null
        },
        'formula1_32': {
          defaultName: '公式1.32',
          customName: null,
          expression: 'data1+data2+data4',
          lastModified: null
        },
        'formula1_5': {
          defaultName: '公式1.5',
          customName: null,
          expression: 'data1+data2+data3+data4+data5',
          lastModified: null
        },
        'formula1_41': {
          defaultName: '公式1.41',
          customName: null,
          expression: 'data1+data2+data3+data4',
          lastModified: null
        },
        'formula1_42': {
          defaultName: '公式1.42',
          customName: null,
          expression: 'data1+data2+data3+data4+data5',
          lastModified: null
        },
        'formula1_51': {
          defaultName: '公式1.51',
          customName: null,
          expression: 'data1+data2+data3+data4',
          lastModified: null
        },
        'formula1_52': {
          defaultName: '公式1.52',
          customName: null,
          expression: 'data1+data2+data3+data4+data5',
          lastModified: null
        },
        'formula2_1': {
          defaultName: '公式2.1',
          customName: null,
          expression: 'data1*data2',
          lastModified: null
        },
        'formula2_2': {
          defaultName: '公式2.2',
          customName: null,
          expression: 'data2*data4',
          lastModified: null
        },
        'formula2_31': {
          defaultName: '公式2.31',
          customName: null,
          expression: 'data1*data2*data3',
          lastModified: null
        },
        'formula2_32': {
          defaultName: '公式2.32',
          customName: null,
          expression: 'data1*data2*data4',
          lastModified: null
        },
        'formula2_5': {
          defaultName: '公式2.5',
          customName: null,
          expression: 'data1*data2*data3*data4*data5',
          lastModified: null
        },
        'formula2_41': {
          defaultName: '公式2.41',
          customName: null,
          expression: 'data1*data2*data3*data4',
          lastModified: null
        },
        'formula2_42': {
          defaultName: '公式2.42',
          customName: null,
          expression: 'data1*data2*data3*data4*data5',
          lastModified: null
        },
        'formula2_51': {
          defaultName: '公式2.51',
          customName: null,
          expression: 'data1*data2*data3*data4',
          lastModified: null
        },
        'formula2_52': {
          defaultName: '公式2.52',
          customName: null,
          expression: 'data1*data2*data3*data4*data5',
          lastModified: null
        },
        'formula3_1': {
          defaultName: '公式3.1',
          customName: null,
          expression: 'data1',
          lastModified: null
        },
        'formula3_2': {
          defaultName: '公式3.2',
          customName: null,
          expression: 'data2*data4',
          lastModified: null
        },
        'formula3_3': {
          defaultName: '公式3.3',
          customName: null,
          expression: '',
          lastModified: null
        },
        'formula3_4': {
          defaultName: '公式3.4',
          customName: null,
          expression: '',
          lastModified: null
        },
        'formula4_1': {
          defaultName: '公式4.1',
          customName: null,
          expression: 'data1/data2',
          lastModified: null
        },
        'formula4_2': {
          defaultName: '公式4.2',
          customName: null,
          expression: 'data2/data4',
          lastModified: null
        },
        'formula4_31': {
          defaultName: '公式4.31',
          customName: null,
          expression: 'data1/data2/data3',
          lastModified: null
        },
        'formula4_32': {
          defaultName: '公式4.32',
          customName: null,
          expression: 'data1/data2/data4',
          lastModified: null
        },
        'formula4_5': {
          defaultName: '公式4.5',
          customName: null,
          expression: 'data1/data2/data3/data4/data5',
          lastModified: null
        },
        'formula4_41': {
          defaultName: '公式4.41',
          customName: null,
          expression: 'data1/data2/data3/data4',
          lastModified: null
        },
        'formula4_42': {
          defaultName: '公式4.42',
          customName: null,
          expression: 'data1/data2/data3/data4/data5',
          lastModified: null
        },
        'formula4_51': {
          defaultName: '公式4.51',
          customName: null,
          expression: 'data1/data2/data3/data4',
          lastModified: null
        },
        'formula4_52': {
          defaultName: '公式4.52',
          customName: null,
          expression: 'data1/data2/data3/data4/data5',
          lastModified: null
        },
        'formula5_1': {
          defaultName: '公式5.1',
          customName: null,
          expression: 'data1-data2',
          lastModified: null
        },
        'formula5_2': {
          defaultName: '公式5.2',
          customName: null,
          expression: 'data2-data4',
          lastModified: null
        },
        'formula5_31': {
          defaultName: '公式5.31',
          customName: null,
          expression: 'data1-data2-data3',
          lastModified: null
        },
        'formula5_32': {
          defaultName: '公式5.32',
          customName: null,
          expression: 'data1-data2-data4',
          lastModified: null
        },
        'formula6_1': {
          defaultName: '公式6.1',
          customName: null,
          expression: 'data1^2',
          lastModified: null
        },
        'formula6_2': {
          defaultName: '公式6.2',
          customName: null,
          expression: 'data2^2',
          lastModified: null
        },
        'formula6_3': {
          defaultName: '公式6.3',
          customName: null,
          expression: 'data3^2',
          lastModified: null
        },
        'formula6_41': {
          defaultName: '公式6.41',
          customName: null,
          expression: 'data1^data2',
          lastModified: null
        },
        'formula6_42': {
          defaultName: '公式6.42',
          customName: null,
          expression: 'data2^data3',
          lastModified: null
        },
        'formula6_43': {
          defaultName: '公式6.43',
          customName: null,
          expression: 'data3^data4',
          lastModified: null
        },
        'formula7_1': {
          defaultName: '公式7.1',
          customName: null,
          expression: 'data1',
          lastModified: null
        },
        'formula7_2': {
          defaultName: '公式7.2',
          customName: null,
          expression: 'data2*data4',
          lastModified: null
        },
        'formula7_3': {
          defaultName: '公式7.3',
          customName: null,
          expression: 'data3+data5',
          lastModified: null
        }
      },

      // 数据字段自定义名称
      dataFields: {
        'data1': {
          defaultName: '数据1',
          customName: null, // 用户自定义名称
          fieldType: 'integer',
          lastModified: null
        },
        'data2': {
          defaultName: '数据2',
          customName: null,
          fieldType: 'integer',
          lastModified: null
        },
        'data3': {
          defaultName: '数据3',
          customName: null,
          fieldType: 'decimal',
          lastModified: null
        },
        'data4': {
          defaultName: '数据4',
          customName: null,
          fieldType: 'precision',
          lastModified: null
        },
        'data5': {
          defaultName: '数据5',
          customName: null,
          fieldType: 'percentage',
          lastModified: null
        },
        'data6': {
          defaultName: '数据6',
          customName: null,
          fieldType: 'decimal',
          lastModified: null
        },
        'data7': {
          defaultName: '数据7',
          customName: null,
          fieldType: 'decimal',
          lastModified: null
        }
      }
    },

    // 主汇总表更新配置
    mainSummaryConfig: {
      // 是否启用自动更新主汇总表
      autoUpdateEnabled: true,

      // 最终汇总值的计算方式
      summaryCalculation: {
        // 基于固定公式的计算结果
        baseFormula: 'income + expense + balance',

        // 额外的计算项
        additionalItems: [
          { name: '上周余额', value: 500, type: 'fixed' },
          { name: '减免', value: -1000, type: 'fixed' }
        ],

        // 最终汇总的目标字段
        targetField: 'finalSummaryValue'
      },

      // 主汇总表更新的API配置
      updateApi: {
        endpoint: '/api/main-summary/update',
        method: 'PUT',
        dataMapping: {
          customerId: 'name',           // 客户名称字段
          summaryValue: 'finalSummaryValue', // 汇总值字段
          sourceModule: 'module1_1'     // 数据来源模块
        }
      }
    },

    // 新增：同步配置
    syncConfig: {
      // 当前模块字段变更时，需要同步的目标模块
      syncTargets: {
        'data1': ['module2_1', 'module3_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'data2': ['module2_1', 'module4_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'data3': ['module2_1', 'module5_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'data4': ['module3_1', 'module6_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'data5': ['module4_1', 'module7_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'data6': ['module5_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'data7': ['module6_1', 'module7_1', 'main_summary', 'weekly_report', 'monthly_report'],
        'income': ['main_summary', 'weekly_report', 'monthly_report'],
        'expense': ['main_summary', 'weekly_report', 'monthly_report'],
        'balance': ['main_summary', 'weekly_report', 'monthly_report'],
        'finalSummary': ['main_summary', 'weekly_report', 'monthly_report']
      }
    },
    
    // 结果字段映射
    resultMapping: {
      result1: '公式1.1',
      result2: '公式1.2', 
      result3: '公式1.3',
      result4: '公式1.4',
      result5: '公式1.5'
    }
  },

  'module1_2': {
    id: 'module1_2',
    name: 'Module 1.2',
    displayName: '模块 1.2',
    description: '标准财务模块 - 7输入+5结果',
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,

    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '数据1', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2', type: 'number', format: 'integer', editable: true },
      data3: { title: '数据3', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '数据4', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '数据5', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '数据6', type: 'number', format: 'decimal', editable: true },
      data7: { title: '数据7', type: 'number', format: 'decimal', editable: true },
      result1: { title: '结果1', type: 'number', format: 'result', editable: false },
      result2: { title: '结果2', type: 'number', format: 'result', editable: false },
      result3: { title: '结果3', type: 'number', format: 'result', editable: false },
      result4: { title: '结果4', type: 'number', format: 'result', editable: false },
      result5: { title: '结果5', type: 'number', format: 'result', editable: false }
    },

    defaultFormulas: {
      '公式1.2.1': 'data1 * data2',
      '公式1.2.2': 'data3 + data4',
      '公式1.2.3': 'data5 * data6',
      '公式1.2.4': 'data7 - data1',
      '公式1.2.5': '(data1 + data2) / 2'
    },

    resultMapping: {
      result1: '公式1.2.1',
      result2: '公式1.2.2',
      result3: '公式1.2.3',
      result4: '公式1.2.4',
      result5: '公式1.2.5'
    }
  },

  'module1_3': {
    id: 'module1_3',
    name: 'Module 1.3',
    displayName: '模块 1.3',
    description: '简化模块 - 2输入+1结果',
    inputFields: 2,
    resultFields: 1,
    totalFields: 3,

    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '数据1', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2', type: 'number', format: 'integer', editable: true },
      result1: { title: '结果1', type: 'number', format: 'result', editable: false }
    },

    defaultFormulas: {
      '公式1.3.1': 'data1 + data2'
    },

    resultMapping: {
      result1: '公式1.3.1'
    }
  },

  'module1_4': {
    id: 'module1_4',
    name: 'Module 1.4',
    displayName: '模块 1.4',
    description: '标准财务模块 - 7输入+5结果',
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,

    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '数据1', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2', type: 'number', format: 'integer', editable: true },
      data3: { title: '数据3', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '数据4', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '数据5', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '数据6', type: 'number', format: 'decimal', editable: true },
      data7: { title: '数据7', type: 'number', format: 'decimal', editable: true },
      result1: { title: '结果1', type: 'number', format: 'result', editable: false },
      result2: { title: '结果2', type: 'number', format: 'result', editable: false },
      result3: { title: '结果3', type: 'number', format: 'result', editable: false },
      result4: { title: '结果4', type: 'number', format: 'result', editable: false },
      result5: { title: '结果5', type: 'number', format: 'result', editable: false }
    },

    defaultFormulas: {
      '公式1.4.1': 'data1 * data3',
      '公式1.4.2': 'data2 + data4',
      '公式1.4.3': 'data5 * data6',
      '公式1.4.4': 'data7 / 2',
      '公式1.4.5': 'data1 + data2 + data3'
    },

    resultMapping: {
      result1: '公式1.4.1',
      result2: '公式1.4.2',
      result3: '公式1.4.3',
      result4: '公式1.4.4',
      result5: '公式1.4.5'
    }
  },

  'module1_5': {
    id: 'module1_5',
    name: 'Module 1.5',
    displayName: '模块 1.5',
    description: '标准财务模块 - 7输入+5结果',
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,

    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '数据1', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2', type: 'number', format: 'integer', editable: true },
      data3: { title: '数据3', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '数据4', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '数据5', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '数据6', type: 'number', format: 'decimal', editable: true },
      data7: { title: '数据7', type: 'number', format: 'decimal', editable: true },
      result1: { title: '结果1', type: 'number', format: 'result', editable: false },
      result2: { title: '结果2', type: 'number', format: 'result', editable: false },
      result3: { title: '结果3', type: 'number', format: 'result', editable: false },
      result4: { title: '结果4', type: 'number', format: 'result', editable: false },
      result5: { title: '结果5', type: 'number', format: 'result', editable: false }
    },

    defaultFormulas: {
      '公式1.5.1': 'data1 - data2',
      '公式1.5.2': 'data3 * data4',
      '公式1.5.3': 'data5 + data6',
      '公式1.5.4': 'data7 * 1.5',
      '公式1.5.5': '(data1 * data2) / data3'
    },

    resultMapping: {
      result1: '公式1.5.1',
      result2: '公式1.5.2',
      result3: '公式1.5.3',
      result4: '公式1.5.4',
      result5: '公式1.5.5'
    }
  },

  'module1_6': {
    id: 'module1_6',
    name: 'Module 1.6',
    displayName: '模块 1.6',
    description: '紧凑模块 - 3输入+4结果',
    inputFields: 3,
    resultFields: 4,
    totalFields: 7,

    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '数据1', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2', type: 'number', format: 'integer', editable: true },
      data3: { title: '数据3', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      result1: { title: '结果1', type: 'number', format: 'result', editable: false },
      result2: { title: '结果2', type: 'number', format: 'result', editable: false },
      result3: { title: '结果3', type: 'number', format: 'result', editable: false },
      result4: { title: '结果4', type: 'number', format: 'result', editable: false }
    },

    defaultFormulas: {
      '公式1.6.1': 'data1 * data2',
      '公式1.6.2': 'data2 + data3',
      '公式1.6.3': 'data1 - data3',
      '公式1.6.4': 'data1 + data2 + data3'
    },

    resultMapping: {
      result1: '公式1.6.1',
      result2: '公式1.6.2',
      result3: '公式1.6.3',
      result4: '公式1.6.4'
    }
  },

  'module1_7': {
    id: 'module1_7',
    name: 'Module 1.7',
    displayName: '模块 1.7',
    description: '紧凑模块 - 3输入+4结果',
    inputFields: 3,
    resultFields: 4,
    totalFields: 7,

    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '数据1', type: 'number', format: 'integer', editable: true },
      data2: { title: '数据2', type: 'number', format: 'integer', editable: true },
      data3: { title: '数据3', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      result1: { title: '结果1', type: 'number', format: 'result', editable: false },
      result2: { title: '结果2', type: 'number', format: 'result', editable: false },
      result3: { title: '结果3', type: 'number', format: 'result', editable: false },
      result4: { title: '结果4', type: 'number', format: 'result', editable: false }
    },

    defaultFormulas: {
      '公式1.7.1': 'data1 / data2',
      '公式1.7.2': 'data2 * data3',
      '公式1.7.3': 'data1 + data3',
      '公式1.7.4': '(data1 + data2) * data3'
    },

    resultMapping: {
      result1: '公式1.7.1',
      result2: '公式1.7.2',
      result3: '公式1.7.3',
      result4: '公式1.7.4'
    }
  },

  'module2_1': {
    id: 'module2_1',
    name: 'Module 2.1',
    displayName: '模块 2.1',
    description: '标准财务模块 - 7输入+5结果',
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,
    
    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '收入数据', type: 'number', format: 'integer', editable: true },
      data2: { title: '支出数据', type: 'number', format: 'integer', editable: true },
      data3: { title: '利润率', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '增长率', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '税率', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '折扣率', type: 'number', format: 'decimal', editable: true },
      data7: { title: '佣金率', type: 'number', format: 'decimal', editable: true },
      result1: { title: '净利润', type: 'number', format: 'result', editable: false },
      result2: { title: '税后收入', type: 'number', format: 'result', editable: false },
      result3: { title: '总佣金', type: 'number', format: 'result', editable: false },
      result4: { title: '实际增长', type: 'number', format: 'result', editable: false },
      result5: { title: '综合评分', type: 'number', format: 'result', editable: false }
    },
    
    defaultFormulas: {
      '公式2.1': '(data1 - data2) * data3',
      '公式2.2': 'data1 * (1 - data5)',
      '公式2.3': 'data1 * data7',
      '公式2.4': 'data1 * data4',
      '公式2.5': '(data1 - data2) * data3 * data4'
    },
    
    resultMapping: {
      result1: '公式2.1',
      result2: '公式2.2',
      result3: '公式2.3', 
      result4: '公式2.4',
      result5: '公式2.5'
    }
  },

  'module3_1': {
    id: 'module3_1',
    name: 'Module 3.1', 
    displayName: '模块 3.1',
    description: '简化模块 - 4输入+1结果',
    inputFields: 4,
    resultFields: 1,
    totalFields: 5,
    
    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '基础数据', type: 'number', format: 'integer', editable: true },
      data2: { title: '系数数据', type: 'number', format: 'decimal', editable: true },
      data3: { title: '比率数据', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '权重数据', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      result1: { title: '综合结果', type: 'number', format: 'result', editable: false }
    },
    
    defaultFormulas: {
      '公式3.1': 'data1 * data2 * data3 * data4'
    },
    
    resultMapping: {
      result1: '公式3.1'
    }
  },

  'module4_1': {
    id: 'module4_1',
    name: 'Module 4.1',
    displayName: '模块 4.1', 
    description: '标准财务模块 - 7输入+5结果',
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,
    
    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '销售额', type: 'number', format: 'integer', editable: true },
      data2: { title: '成本', type: 'number', format: 'integer', editable: true },
      data3: { title: '毛利率', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '运营费率', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '税率', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '折旧率', type: 'number', format: 'decimal', editable: true },
      data7: { title: '投资回报率', type: 'number', format: 'decimal', editable: true },
      result1: { title: '毛利润', type: 'number', format: 'result', editable: false },
      result2: { title: '运营利润', type: 'number', format: 'result', editable: false },
      result3: { title: '净利润', type: 'number', format: 'result', editable: false },
      result4: { title: '投资收益', type: 'number', format: 'result', editable: false },
      result5: { title: '综合收益率', type: 'number', format: 'result', editable: false }
    },
    
    defaultFormulas: {
      '公式4.1': '(data1 - data2) * data3',
      '公式4.2': 'data1 * (1 - data4)',
      '公式4.3': 'data1 * data3 * (1 - data5)',
      '公式4.4': 'data1 * data7',
      '公式4.5': '(data1 - data2) * data3 * data7'
    },
    
    resultMapping: {
      result1: '公式4.1',
      result2: '公式4.2',
      result3: '公式4.3',
      result4: '公式4.4', 
      result5: '公式4.5'
    }
  },

  'module5_1': {
    id: 'module5_1',
    name: 'Module 5.1',
    displayName: '模块 5.1',
    description: '标准财务模块 - 7输入+5结果', 
    inputFields: 7,
    resultFields: 5,
    totalFields: 12,
    
    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '资产总额', type: 'number', format: 'integer', editable: true },
      data2: { title: '负债总额', type: 'number', format: 'integer', editable: true },
      data3: { title: '资产负债率', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '流动比率', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '速动比率', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '现金比率', type: 'number', format: 'decimal', editable: true },
      data7: { title: '权益乘数', type: 'number', format: 'decimal', editable: true },
      result1: { title: '净资产', type: 'number', format: 'result', editable: false },
      result2: { title: '流动性指标', type: 'number', format: 'result', editable: false },
      result3: { title: '偿债能力', type: 'number', format: 'result', editable: false },
      result4: { title: '财务杠杆', type: 'number', format: 'result', editable: false },
      result5: { title: '综合评级', type: 'number', format: 'result', editable: false }
    },
    
    defaultFormulas: {
      '公式5.1': 'data1 - data2',
      '公式5.2': '(data4 + data5 + data6) / 3',
      '公式5.3': 'data1 / data2',
      '公式5.4': 'data1 * data7',
      '公式5.5': '(data1 - data2) * data3 * data4'
    },
    
    resultMapping: {
      result1: '公式5.1',
      result2: '公式5.2',
      result3: '公式5.3',
      result4: '公式5.4',
      result5: '公式5.5'
    }
  },

  'module6_1': {
    id: 'module6_1',
    name: 'Module 6.1',
    displayName: '模块 6.1',
    description: '财务模块 - 7输入+4结果',
    inputFields: 7,
    resultFields: 4,
    totalFields: 11,
    
    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '营业收入', type: 'number', format: 'integer', editable: true },
      data2: { title: '营业成本', type: 'number', format: 'integer', editable: true },
      data3: { title: '费用率', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '税率', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '分红率', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '留存率', type: 'number', format: 'decimal', editable: true },
      data7: { title: '增长预期', type: 'number', format: 'decimal', editable: true },
      result1: { title: '营业利润', type: 'number', format: 'result', editable: false },
      result2: { title: '净利润', type: 'number', format: 'result', editable: false },
      result3: { title: '分红金额', type: 'number', format: 'result', editable: false },
      result4: { title: '留存收益', type: 'number', format: 'result', editable: false }
    },
    
    defaultFormulas: {
      '公式6.1': '(data1 - data2) * (1 - data3)',
      '公式6.2': 'data1 * (1 - data4)',
      '公式6.3': 'data1 * data5',
      '公式6.4': 'data1 * data6'
    },
    
    resultMapping: {
      result1: '公式6.1',
      result2: '公式6.2',
      result3: '公式6.3',
      result4: '公式6.4'
    }
  },

  'module7_1': {
    id: 'module7_1',
    name: 'Module 7.1',
    displayName: '模块 7.1',
    description: '财务模块 - 7输入+4结果',
    inputFields: 7,
    resultFields: 4,
    totalFields: 11,
    
    fields: {
      name: { title: '客户名', type: 'text', editable: true },
      userName: { title: '用户名', type: 'text', editable: true },
      data1: { title: '项目投资', type: 'number', format: 'integer', editable: true },
      data2: { title: '年收益', type: 'number', format: 'integer', editable: true },
      data3: { title: '风险系数', type: 'number', format: 'decimal', editable: true, step: 0.01 },
      data4: { title: '贴现率', type: 'number', format: 'precision', editable: true, step: 0.00000001 },
      data5: { title: '项目期限', type: 'number', format: 'percentage', editable: true, step: 0.01 },
      data6: { title: '残值率', type: 'number', format: 'decimal', editable: true },
      data7: { title: '通胀率', type: 'number', format: 'decimal', editable: true },
      result1: { title: '净现值', type: 'number', format: 'result', editable: false },
      result2: { title: '投资回报率', type: 'number', format: 'result', editable: false },
      result3: { title: '风险调整收益', type: 'number', format: 'result', editable: false },
      result4: { title: '实际收益率', type: 'number', format: 'result', editable: false }
    },
    
    defaultFormulas: {
      '公式7.1': 'data2 / (1 + data4) - data1',
      '公式7.2': '(data2 - data1) / data1',
      '公式7.3': 'data2 * (1 - data3)',
      '公式7.4': 'data2 * (1 - data7)'
    },
    
    resultMapping: {
      result1: '公式7.1',
      result2: '公式7.2',
      result3: '公式7.3',
      result4: '公式7.4'
    }
  }
};

// 获取模块配置
export const getModuleConfig = (moduleId) => {
  return MODULE_CONFIGS[moduleId] || null;
};

// 获取所有模块列表
export const getAllModules = () => {
  return Object.values(MODULE_CONFIGS);
};

// 获取模块的字段列表
export const getModuleFields = (moduleId) => {
  const config = getModuleConfig(moduleId);
  return config ? config.fields : {};
};

// 获取模块的公式配置
export const getModuleFormulas = (moduleId) => {
  const config = getModuleConfig(moduleId);
  return config ? config.defaultFormulas : {};
};

// 验证模块ID是否有效
export const isValidModuleId = (moduleId) => {
  return moduleId in MODULE_CONFIGS;
};
