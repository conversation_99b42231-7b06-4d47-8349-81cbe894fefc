/* 界面3 - 公式配置页面样式 */

.formulaConfigurationPage {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.formulaGridSection {
  margin-bottom: 24px;
}

.formulaGrid {
  margin-bottom: 24px;
}

.formulaRow {
  margin-bottom: 16px;
}

.formulaCard {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
  cursor: pointer;
}

.formulaCard:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.formulaContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formulaNameTag {
  background-color: #FFD700 !important;
  color: #000 !important;
  font-weight: bold;
  margin: 0;
  align-self: flex-start;
}

.formulaExpression {
  background-color: #FFF8DC;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  word-break: break-all;
}

.saveSection {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #d9d9d9;
}

.savePlantButton {
  background-color: #FFD700 !important;
  border-color: #FFD700 !important;
  color: #000 !important;
  font-weight: bold;
  min-width: 120px;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.savePlantButton:hover {
  background-color: #FFC700 !important;
  border-color: #FFC700 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 215, 0, 0.4);
}

.detailedDataSection {
  background: #fff;
  padding: 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.editableCell {
  cursor: pointer;
  transition: background-color 0.2s;
}

.editableCell:hover {
  background-color: #f0f8ff !important;
}

/* 颜色编码样式 */
.colorCodedCell {
  border: 1px solid #D3D3D3;
  padding: 4px 8px;
  min-height: 32px;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.colorCodedCell.customer {
  background-color: #FFFFFF;
  justify-content: flex-start;
}

.colorCodedCell.input {
  background-color: #FFFFFF;
  justify-content: flex-end;
}

.colorCodedCell.calculation {
  background-color: #87CEEB;
  justify-content: flex-end;
}

.colorCodedCell.result {
  background-color: #FFA500;
  justify-content: flex-end;
  font-weight: bold;
}

.colorCodedCell.summary {
  background-color: #90EE90;
  justify-content: flex-end;
  font-weight: bold;
}

.colorCodedCell.formula {
  background-color: #FFD700;
  justify-content: center;
  font-weight: bold;
}

/* 表格样式优化 */
.detailTable {
  border: 1px solid #f0f0f0;
}

.detailTable .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: bold;
  text-align: center;
  padding: 8px 4px;
}

.detailTable .ant-table-tbody > tr > td {
  padding: 4px;
  border-right: 1px solid #f0f0f0;
}

.detailTable .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 卡片标题样式 */
.detailCardTitle {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #000;
  font-weight: bold;
  text-align: center;
  padding: 8px;
  border-radius: 4px 4px 0 0;
  margin: -16px -16px 16px -16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .formulaRow .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .formulaConfigurationPage {
    padding: 16px;
  }
  
  .formulaCard {
    margin-bottom: 12px;
  }
  
  .detailedDataSection {
    padding: 12px;
  }
  
  .formulaExpression {
    font-size: 10px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.formulaCard {
  animation: fadeIn 0.3s ease-out;
}

.detailedDataSection {
  animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
.detailedDataSection ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.detailedDataSection ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.detailedDataSection ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.detailedDataSection ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
