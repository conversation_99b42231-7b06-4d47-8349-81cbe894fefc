// 调试DynamicFormulaEngine的表达式转换过程

// 导入实际的DynamicFormulaEngine
import { DynamicFormulaEngine } from './utils/DynamicFormulaEngine.js';

function debugExpression(expression, data) {
  console.log(`\n🔍 调试表达式: "${expression}"`);
  console.log(`📊 输入数据: ${JSON.stringify(data)}`);
  
  // 模拟DynamicFormulaEngine的处理过程
  let cleanExpression = expression.trim();
  console.log(`1️⃣ 清理后表达式: "${cleanExpression}"`);
  
  // 替换数据变量
  const dataValues = {
    data1: data.data1 !== undefined ? data.data1 : 0,
    data2: data.data2 !== undefined ? data.data2 : 0,
    data3: data.data3 !== undefined ? data.data3 : 0,
    data4: data.data4 !== undefined ? data.data4 : 0,
    data5: data.data5 !== undefined ? data.data5 : 0,
    data6: data.data6 !== undefined ? data.data6 : 0,
    data7: data.data7 !== undefined ? data.data7 : 0
  };
  
  console.log(`2️⃣ 数据值映射: ${JSON.stringify(dataValues)}`);
  
  // 按照变量名长度排序，避免替换冲突
  const sortedKeys = Object.keys(dataValues).sort((a, b) => b.length - a.length);
  
  for (const key of sortedKeys) {
    const regex = new RegExp(`\\b${key}\\b`, 'g');
    const oldExpression = cleanExpression;
    cleanExpression = cleanExpression.replace(regex, `(${dataValues[key]})`);
    if (oldExpression !== cleanExpression) {
      console.log(`   替换 ${key} -> (${dataValues[key]}): "${cleanExpression}"`);
    }
  }
  
  console.log(`3️⃣ 变量替换后: "${cleanExpression}"`);
  
  // 处理幂运算
  const beforePower = cleanExpression;
  cleanExpression = cleanExpression.replace(/(-?\d+(?:\.\d+)?|\([^)]+\))\s*\^\s*(-?\d+(?:\.\d+)?|\([^)]+\))/g, 
    (match, base, exponent) => {
      return `Math.pow((${base}), (${exponent}))`;
    }
  );
  
  if (beforePower !== cleanExpression) {
    console.log(`4️⃣ 幂运算处理后: "${cleanExpression}"`);
  }
  
  // 验证表达式安全性
  const safePattern = /^[\d+\-*/().\s,Math.pow]+$/;
  const isSafe = safePattern.test(cleanExpression);
  console.log(`5️⃣ 安全性检查: ${isSafe ? '✅ 通过' : '❌ 失败'}`);
  
  if (!isSafe) {
    console.log(`   不安全字符: "${cleanExpression}"`);
    return 0;
  }
  
  // 尝试计算
  try {
    console.log(`6️⃣ 准备计算: "${cleanExpression}"`);
    const result = eval(cleanExpression);
    console.log(`7️⃣ 计算结果: ${result}`);
    const rounded = Math.round(result);
    console.log(`8️⃣ 四舍五入: ${rounded}`);
    return rounded;
  } catch (error) {
    console.log(`❌ 计算错误: ${error.message}`);
    return 0;
  }
}

// 测试用例
const testCases = [
  {
    expression: 'data1-data2',
    data: { data1: -100, data2: -2 }
  },
  {
    expression: 'data1^2',
    data: { data1: -10 }
  },
  {
    expression: '(data1+data2+data4)+data3',
    data: { data1: -100, data2: -2, data3: -50, data4: -1 }
  }
];

console.log('🧪 开始调试DynamicFormulaEngine...\n');

for (const testCase of testCases) {
  const result = debugExpression(testCase.expression, testCase.data);
  
  // 与实际引擎对比
  const engineResult = DynamicFormulaEngine.calculateExpression(testCase.expression, testCase.data);
  console.log(`🔄 引擎结果: ${engineResult}`);
  console.log(`📊 结果对比: ${result === engineResult ? '✅ 一致' : '❌ 不一致'}`);
  console.log('-'.repeat(50));
}
