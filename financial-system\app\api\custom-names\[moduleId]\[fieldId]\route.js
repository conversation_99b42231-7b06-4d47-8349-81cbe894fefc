// API路由 - 单个字段自定义名称管理
// GET /api/custom-names/[moduleId]/[fieldId] - 获取单个字段的自定义名称
// PUT /api/custom-names/[moduleId]/[fieldId] - 更新单个字段的自定义名称

import { NextResponse } from 'next/server';
import { MODULE_CONFIGS } from '../../../../../config/moduleConfigs';

/**
 * 获取单个字段的自定义名称
 * GET /api/custom-names/[moduleId]/[fieldId]
 */
export async function GET(request, { params }) {
  try {
    const { moduleId, fieldId } = params;
    
    // 验证模块ID
    if (!MODULE_CONFIGS[moduleId]) {
      return NextResponse.json(
        { success: false, error: '模块不存在' },
        { status: 404 }
      );
    }
    
    const moduleConfig = MODULE_CONFIGS[moduleId];
    const customNames = moduleConfig.customNames || { formulas: {}, dataFields: {} };
    
    // 确定字段类型
    const isFormula = fieldId.startsWith('formula') || fieldId.startsWith('result');
    const fieldSection = isFormula ? 'formulas' : 'dataFields';
    
    // 获取字段配置
    const fieldConfig = customNames[fieldSection]?.[fieldId];
    
    if (!fieldConfig) {
      return NextResponse.json(
        { success: false, error: '字段不存在' },
        { status: 404 }
      );
    }
    
    const responseData = {
      moduleId,
      fieldId,
      fieldType: isFormula ? 'formula' : 'data',
      defaultName: fieldConfig.defaultName,
      customName: fieldConfig.customName || fieldConfig.defaultName,
      isCustomized: !!fieldConfig.customName,
      lastModified: fieldConfig.lastModified
    };
    
    // 如果是公式字段，添加表达式信息
    if (isFormula) {
      responseData.expression = fieldConfig.expression;
    } else {
      responseData.dataType = fieldConfig.fieldType;
    }
    
    return NextResponse.json({
      success: true,
      data: responseData
    });
    
  } catch (error) {
    console.error('获取字段自定义名称失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 更新单个字段的自定义名称
 * PUT /api/custom-names/[moduleId]/[fieldId]
 * Body: { customName: string, expression?: string }
 */
export async function PUT(request, { params }) {
  try {
    const { moduleId, fieldId } = params;
    const body = await request.json();
    
    // 验证模块ID
    if (!MODULE_CONFIGS[moduleId]) {
      return NextResponse.json(
        { success: false, error: '模块不存在' },
        { status: 404 }
      );
    }
    
    const { customName, expression } = body;
    
    // 验证输入
    if (!customName || customName.trim().length === 0) {
      return NextResponse.json(
        { success: false, error: '自定义名称不能为空' },
        { status: 400 }
      );
    }
    
    if (customName.trim().length > 50) {
      return NextResponse.json(
        { success: false, error: '自定义名称长度不能超过50个字符' },
        { status: 400 }
      );
    }
    
    const moduleConfig = MODULE_CONFIGS[moduleId];
    
    // 确保customNames结构存在
    if (!moduleConfig.customNames) {
      moduleConfig.customNames = { formulas: {}, dataFields: {} };
    }
    
    // 确定字段类型
    const isFormula = fieldId.startsWith('formula') || fieldId.startsWith('result');
    const fieldSection = isFormula ? 'formulas' : 'dataFields';
    
    // 确保字段配置存在
    if (!moduleConfig.customNames[fieldSection][fieldId]) {
      // 创建默认配置
      if (isFormula) {
        moduleConfig.customNames[fieldSection][fieldId] = {
          defaultName: `公式${fieldId.replace('formula', '').replace('_', '.')}`,
          customName: null,
          expression: '',
          lastModified: null
        };
      } else {
        moduleConfig.customNames[fieldSection][fieldId] = {
          defaultName: `数据${fieldId.replace('data', '')}`,
          customName: null,
          fieldType: 'decimal',
          lastModified: null
        };
      }
    }
    
    const fieldConfig = moduleConfig.customNames[fieldSection][fieldId];
    const oldName = fieldConfig.customName || fieldConfig.defaultName;
    const newName = customName.trim();
    
    // 检查名称是否发生变化
    if (oldName === newName && (!isFormula || fieldConfig.expression === expression)) {
      return NextResponse.json({
        success: true,
        data: {
          fieldId,
          message: '名称未发生变化',
          changed: false
        }
      });
    }
    
    // 更新配置
    fieldConfig.customName = newName;
    fieldConfig.lastModified = new Date().toISOString();
    
    // 如果是公式字段，更新表达式
    if (isFormula && expression !== undefined) {
      if (!expression || expression.trim().length === 0) {
        return NextResponse.json(
          { success: false, error: '公式表达式不能为空' },
          { status: 400 }
        );
      }
      fieldConfig.expression = expression.trim();
    }
    
    const responseData = {
      fieldId,
      moduleId,
      oldName,
      newName,
      changed: true,
      lastModified: fieldConfig.lastModified,
      sync: {
        success: true,
        affectedModules: ['summary_table']
      }
    };
    
    // 如果是公式字段，添加表达式信息
    if (isFormula) {
      responseData.expression = fieldConfig.expression;
    }
    
    return NextResponse.json({
      success: true,
      data: responseData
    });
    
  } catch (error) {
    console.error('更新字段自定义名称失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 重置字段名称为默认值
 * DELETE /api/custom-names/[moduleId]/[fieldId]
 */
export async function DELETE(request, { params }) {
  try {
    const { moduleId, fieldId } = params;
    
    // 验证模块ID
    if (!MODULE_CONFIGS[moduleId]) {
      return NextResponse.json(
        { success: false, error: '模块不存在' },
        { status: 404 }
      );
    }
    
    const moduleConfig = MODULE_CONFIGS[moduleId];
    const customNames = moduleConfig.customNames || { formulas: {}, dataFields: {} };
    
    // 确定字段类型
    const isFormula = fieldId.startsWith('formula') || fieldId.startsWith('result');
    const fieldSection = isFormula ? 'formulas' : 'dataFields';
    
    // 获取字段配置
    const fieldConfig = customNames[fieldSection]?.[fieldId];
    
    if (!fieldConfig) {
      return NextResponse.json(
        { success: false, error: '字段不存在' },
        { status: 404 }
      );
    }
    
    const oldName = fieldConfig.customName || fieldConfig.defaultName;
    const defaultName = fieldConfig.defaultName;
    
    // 重置为默认名称
    fieldConfig.customName = null;
    fieldConfig.lastModified = new Date().toISOString();
    
    return NextResponse.json({
      success: true,
      data: {
        fieldId,
        moduleId,
        oldName,
        newName: defaultName,
        reset: true,
        lastModified: fieldConfig.lastModified,
        sync: {
          success: true,
          affectedModules: ['summary_table']
        }
      }
    });
    
  } catch (error) {
    console.error('重置字段名称失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
