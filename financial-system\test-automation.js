// 简化的测试脚本 - 不使用puppeteer，直接检查数据处理逻辑

// 模拟FormulaPageConsolidationCalculations的测试
function testDataProcessing() {
  console.log('🚀 开始数据处理测试...');

  // 模拟测试数据
  const testCustomerData = {
    group1: [{
      key: 'customer1',
      customer: '张三公司',
      user: '用户A',
      data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3,
      result1: 200, result2: 150, result3: 100,
      shareholders: [{ name: '张三公司', ratio: 0.5, result1: 100, result2: 75 }]
    }],
    group2: [{
      key: 'customer2',
      customer: '张三公司',
      user: '用户A',
      data1: 150, data2: 1.5, data3: 30, data4: 2, data5: 15, data6: 60, data7: 2,
      result1: 300, result2: 120, result3: 80,
      shareholders: [{ name: '张三公司', ratio: 0.3, result1: 90, result2: 36 }]
    }],
    group3: [{
      key: 'customer3',
      customer: '李四公司',
      user: '用户B',
      data1: 200, data2: 3, data3: 40, data4: 1.5, data5: 20, data6: 90, data7: 2.5,
      result1: 450, result2: 225, result3: 120,
      shareholders: [{ name: '李四公司', ratio: 0.8, result1: 360, result2: 180 }]
    }],
    group4: [{
      key: 'customer4',
      customer: '王五公司',
      user: '用户B',
      data1: 80, data2: 2.5, data3: 35, data4: 1, data5: 18, data6: 70, data7: 4,
      result1: 200, result2: 280, result3: 90,
      shareholders: [{ name: '王五公司', ratio: 0.4, result1: 80, result2: 112 }]
    }]
  };

  console.log('📊 测试数据:', testCustomerData);

  // 模拟数据转换过程
  console.log('🔄 开始数据转换...');

  // 1. 转换为统一格式
  const allCustomers = [];
  Object.entries(testCustomerData).forEach(([groupKey, customers]) => {
    customers.forEach(customer => {
      const normalizedCustomer = {
        name: customer.customer,
        userName: customer.user,
        groupKey: groupKey,
        data1: customer.data1, data2: customer.data2, data3: customer.data3,
        data4: customer.data4, data5: customer.data5, data6: customer.data6, data7: customer.data7,
        result1: customer.result1, result2: customer.result2, result3: customer.result3,
        shareholders: customer.shareholders
      };

      // 检查是否有数据
      const hasData = normalizedCustomer.data1 !== 0 ||
                     normalizedCustomer.data2 !== 0 ||
                     normalizedCustomer.result1 !== 0 ||
                     normalizedCustomer.result2 !== 0 ||
                     normalizedCustomer.result3 !== 0 ||
                     (normalizedCustomer.shareholders && normalizedCustomer.shareholders.length > 0);

      if (hasData) {
        allCustomers.push(normalizedCustomer);
      }
    });
  });

  console.log('✅ 转换后的客户数组:', allCustomers);
  console.log(`📊 有效客户数量: ${allCustomers.length}`);

  // 2. 按客户名称归纳
  const customerGroups = {};
  allCustomers.forEach(customer => {
    const customerName = customer.name;
    if (!customerGroups[customerName]) {
      customerGroups[customerName] = [];
    }
    customerGroups[customerName].push(customer);
  });

  console.log('✅ 按客户名分组:', customerGroups);

  // 3. 归纳相同客户名的数据
  const consolidatedCustomers = [];
  Object.entries(customerGroups).forEach(([customerName, customers]) => {
    const consolidated = {
      customerName: customerName,
      userName: customers[0].userName,
      originalCount: customers.length,
      data1: 0, data2: 0, data3: 0, data4: 0, data5: 0, data6: 0, data7: 0,
      result1: 0, result2: 0, result3: 0,
      consolidatedShareholders: []
    };

    customers.forEach(customer => {
      consolidated.data1 += customer.data1;
      consolidated.data2 += customer.data2;
      consolidated.data3 += customer.data3;
      consolidated.data4 += customer.data4;
      consolidated.data5 += customer.data5;
      consolidated.data6 += customer.data6;
      consolidated.data7 += customer.data7;
      consolidated.result1 += customer.result1;
      consolidated.result2 += customer.result2;
      consolidated.result3 += customer.result3;

      if (customer.shareholders) {
        consolidated.consolidatedShareholders.push(...customer.shareholders);
      }
    });

    consolidatedCustomers.push(consolidated);
  });

  console.log('✅ 归纳后的客户数据:', consolidatedCustomers);

  // 4. 按用户名分组显示
  const groupedByUser = {};
  consolidatedCustomers.forEach(customer => {
    const userName = customer.userName;
    if (!groupedByUser[userName]) {
      groupedByUser[userName] = [];
    }
    groupedByUser[userName].push(customer);
  });

  console.log('✅ 按用户分组的最终数据:', groupedByUser);

  // 5. 验证预期结果
  console.log('🧪 验证测试结果:');
  console.log(`- 用户A应该有1个客户(张三公司合并): ${groupedByUser['用户A'] ? groupedByUser['用户A'].length : 0}`);
  console.log(`- 用户B应该有2个客户(李四公司+王五公司): ${groupedByUser['用户B'] ? groupedByUser['用户B'].length : 0}`);

  if (groupedByUser['用户A'] && groupedByUser['用户A'][0]) {
    const zhangsan = groupedByUser['用户A'][0];
    console.log(`- 张三公司合并后data1应该是250: ${zhangsan.data1}`);
    console.log(`- 张三公司合并后result1应该是500: ${zhangsan.result1}`);
  }

  return groupedByUser;
}

// 运行测试
console.log('🚀 开始测试数据处理逻辑...');
const result = testDataProcessing();
console.log('✅ 测试完成，结果:', result);
