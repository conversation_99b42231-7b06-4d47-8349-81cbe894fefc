// 健康检查API - JavaScript版本
// 避免TypeScript版本冲突

import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';

export async function GET() {
  try {
    const startTime = Date.now();
    
    // 检查数据库连接
    let dbStatus = 'healthy';
    let dbResponseTime = 0;
    
    try {
      const dbStartTime = Date.now();
      await prisma.$queryRaw`SELECT 1`;
      dbResponseTime = Date.now() - dbStartTime;
    } catch (error) {
      dbStatus = 'unhealthy';
      console.error('数据库健康检查失败:', error);
    }
    
    // 检查内存使用情况
    const memoryUsage = process.memoryUsage();
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };
    
    // 检查系统负载
    const cpuUsage = process.cpuUsage();
    
    // 计算总响应时间
    const totalResponseTime = Date.now() - startTime;
    
    // 确定整体健康状态
    const isHealthy = dbStatus === 'healthy' && 
                     memoryUsageMB.heapUsed < 500 && // 内存使用小于500MB
                     totalResponseTime < 1000; // 响应时间小于1秒
    
    const healthData = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      
      // 数据库状态
      database: {
        status: dbStatus,
        responseTime: `${dbResponseTime}ms`,
      },
      
      // 内存状态
      memory: {
        usage: memoryUsageMB,
        limit: '1GB',
        status: memoryUsageMB.heapUsed < 500 ? 'healthy' : 'warning',
      },
      
      // CPU状态
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      
      // 性能指标
      performance: {
        responseTime: `${totalResponseTime}ms`,
        status: totalResponseTime < 1000 ? 'healthy' : 'slow',
      },
      
      // 服务状态
      services: {
        api: 'healthy',
        auth: 'healthy',
        calculations: 'healthy',
      },
      
      // 系统信息
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
      },
    };
    
    // 根据健康状态返回相应的HTTP状态码
    const statusCode = isHealthy ? 200 : 503;
    
    return NextResponse.json(healthData, { status: statusCode });
    
  } catch (error) {
    console.error('健康检查失败:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: '健康检查执行失败',
      message: error.message,
    }, { status: 503 });
  }
}

// 支持HEAD请求用于简单的存活检查
export async function HEAD() {
  try {
    // 简单的数据库连接检查
    await prisma.$queryRaw`SELECT 1`;
    return new Response(null, { status: 200 });
  } catch (error) {
    return new Response(null, { status: 503 });
  }
}
