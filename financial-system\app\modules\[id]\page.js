'use client';

import { useState, useEffect } from 'react';
import { Layout, Card, Button, Space, Typography, message, Statistic, Row, Col } from 'antd';
import { useRouter, useParams } from 'next/navigation';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import EnhancedDynamicTable from '../../../components/EnhancedDynamicTable';
import { MODULE_CONFIGS } from '../../../config/moduleConfigs';

const { Content } = Layout;
const { Title, Text } = Typography;

export default function ModulePage() {
  const router = useRouter();
  const params = useParams();
  const moduleId = params.id; // 保持字符串格式，如 'module1_1'

  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([
    // 示例数据
    {
      name: '客户A',
      userName: '用户A',
      data1: 1000,
      data2: 800,
      data3: 0.15,
      data4: 0.00000123,
      data5: 0.08,
      data6: 500,
      data7: 300,
      result1: 0,
      result2: 0,
      result3: 0,
      result4: 0,
      result5: 0
    },
    {
      name: '客户B',
      userName: '用户B',
      data1: 1200,
      data2: 900,
      data3: 0.18,
      data4: 0.00000156,
      data5: 0.10,
      data6: 600,
      data7: 350,
      result1: 0,
      result2: 0,
      result3: 0,
      result4: 0,
      result5: 0
    }
  ]);

  // 获取模块配置
  const moduleConfig = MODULE_CONFIGS[moduleId];

  if (!moduleConfig) {
    return (
      <Layout>
        <Content style={{ padding: '24px' }}>
          <Card>
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Title level={3}>模块不存在</Title>
              <Text>模块ID: {moduleId}</Text>
              <br />
              <Button type="primary" onClick={() => router.push('/')}>
                返回首页
              </Button>
            </div>
          </Card>
        </Content>
      </Layout>
    );
  }

  // 添加新客户
  const addCustomer = () => {
    const newCustomer = {
      name: `客户${customers.length + 1}`,
      userName: `用户${customers.length + 1}`,
      data1: 0,
      data2: 0,
      data3: 0,
      data4: 0,
      data5: 0,
      data6: 0,
      data7: 0,
      result1: 0,
      result2: 0,
      result3: 0,
      result4: 0,
      result5: 0
    };
    setCustomers([...customers, newCustomer]);
  };

  // 删除客户
  const deleteCustomer = (index) => {
    const newCustomers = customers.filter((_, i) => i !== index);
    setCustomers(newCustomers);
  };

  // 处理客户数据变更
  const handleCustomersChange = (newCustomers) => {
    setCustomers(newCustomers);
  };

  return (
    <Layout>
      <Content style={{ padding: '24px' }}>
        {/* 页面标题和统计信息 */}
        <Card style={{ marginBottom: '16px' }}>
          <Row gutter={16}>
            <Col span={12}>
              <Title level={2}>{moduleConfig.displayName}</Title>
              <Text type="secondary">{moduleConfig.description}</Text>
            </Col>
            <Col span={12}>
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic title="客户数量" value={customers.length} />
                </Col>
                <Col span={8}>
                  <Statistic title="输入字段" value={moduleConfig.inputFields} />
                </Col>
                <Col span={8}>
                  <Statistic title="结果字段" value={moduleConfig.resultFields} />
                </Col>
              </Row>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <Card style={{ marginBottom: '16px' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={addCustomer}
            >
              添加客户
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                if (customers.length > 0) {
                  deleteCustomer(customers.length - 1);
                }
              }}
              disabled={customers.length === 0}
            >
              删除最后一个客户
            </Button>
          </Space>
        </Card>

        {/* 增强版动态表格 */}
        <EnhancedDynamicTable
          agentId={1} // 假设代理商ID为1
          moduleId={moduleId}
          customers={customers}
          onCustomersChange={handleCustomersChange}
          editable={true}
          showFormulas={true}
          showPlantButton={true}
        />
      </Content>
    </Layout>
  );
}
