// 调试登录测试 - 详细分析登录过程
const { chromium } = require('playwright');

async function debugLogin() {
  console.log('🔍 启动登录调试测试...');
  console.log('🌐 网址: http://dlsykgzq.w1.luyouxia.net');
  console.log('👤 用户名: admin');
  console.log('🔑 密码: 123456\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 3000
  });
  
  const page = await browser.newPage();
  
  // 监听页面响应
  page.on('response', response => {
    if (response.url().includes('login') || response.url().includes('auth')) {
      console.log(`📡 登录相关响应: ${response.status()} - ${response.url()}`);
    }
  });
  
  // 监听页面错误
  page.on('pageerror', error => {
    console.log(`🔴 页面错误: ${error.message}`);
  });
  
  try {
    // 1. 访问网站
    console.log('📊 步骤1: 访问网站...');
    await page.goto('http://dlsykgzq.w1.luyouxia.net');
    await page.waitForTimeout(3000);
    
    // 2. 分析登录表单
    console.log('\n📊 步骤2: 分析登录表单...');
    
    // 截图保存当前页面状态
    await page.screenshot({ path: 'login-page.png' });
    console.log('📸 已保存登录页面截图: login-page.png');
    
    // 查找所有输入框
    const allInputs = await page.$$('input');
    console.log(`📊 找到 ${allInputs.length} 个输入框:`);
    
    for (let i = 0; i < allInputs.length; i++) {
      const input = allInputs[i];
      const type = await input.getAttribute('type') || 'text';
      const placeholder = await input.getAttribute('placeholder') || '';
      const name = await input.getAttribute('name') || '';
      const id = await input.getAttribute('id') || '';
      
      console.log(`   输入框${i+1}: type="${type}", placeholder="${placeholder}", name="${name}", id="${id}"`);
    }
    
    // 查找所有按钮
    const allButtons = await page.$$('button');
    console.log(`\n📊 找到 ${allButtons.length} 个按钮:`);
    
    for (let i = 0; i < allButtons.length; i++) {
      const button = allButtons[i];
      const text = await button.textContent();
      const type = await button.getAttribute('type') || '';
      
      console.log(`   按钮${i+1}: text="${text}", type="${type}"`);
    }
    
    // 3. 尝试多种登录方式
    console.log('\n📊 步骤3: 尝试登录...');
    
    // 方式1: 使用placeholder查找
    console.log('🔐 方式1: 使用placeholder查找输入框...');
    const usernameInput1 = page.locator('input[placeholder*="用户名"], input[placeholder*="账号"]').first();
    const passwordInput1 = page.locator('input[type="password"]').first();
    
    if (await usernameInput1.isVisible() && await passwordInput1.isVisible()) {
      console.log('✅ 找到用户名和密码输入框');
      
      await usernameInput1.clear();
      await usernameInput1.fill('admin');
      await page.waitForTimeout(1000);
      
      await passwordInput1.clear();
      await passwordInput1.fill('123456');
      await page.waitForTimeout(1000);
      
      // 检查输入值
      const usernameValue = await usernameInput1.inputValue();
      const passwordValue = await passwordInput1.inputValue();
      console.log(`📝 输入验证: 用户名="${usernameValue}", 密码="${passwordValue}"`);
      
      // 查找并点击登录按钮
      const loginButton = page.locator('button[type="submit"], button:has-text("登录")').first();
      if (await loginButton.isVisible()) {
        console.log('🔘 点击登录按钮...');
        
        // 等待响应
        const responsePromise = page.waitForResponse(response => 
          response.url().includes('login') || response.url().includes('auth'), 
          { timeout: 10000 }
        ).catch(() => null);
        
        await loginButton.click();
        
        const response = await responsePromise;
        if (response) {
          console.log(`📡 登录响应: ${response.status()}`);
          const responseText = await response.text().catch(() => '无法读取响应内容');
          console.log(`📄 响应内容: ${responseText.substring(0, 200)}...`);
        }
        
        await page.waitForTimeout(5000);
        
        // 检查登录结果
        const currentUrl = page.url();
        console.log(`📍 登录后URL: ${currentUrl}`);
        
        // 检查是否有错误消息
        const errorMessages = await page.$$('text=错误, text=失败, .error, .ant-message-error');
        if (errorMessages.length > 0) {
          console.log('🔴 发现错误消息:');
          for (const error of errorMessages) {
            const text = await error.textContent();
            console.log(`   - ${text}`);
          }
        }
        
        // 检查是否登录成功
        const hasLoggedIn = currentUrl !== 'http://dlsykgzq.w1.luyouxia.net/login/' && 
                           !currentUrl.includes('login');
        
        console.log(`${hasLoggedIn ? '✅' : '❌'} 登录${hasLoggedIn ? '成功' : '失败'}`);
        
        if (hasLoggedIn) {
          console.log('\n🎉 登录成功！开始测试主要功能...');
          await testMainFeatures(page);
        } else {
          console.log('\n❌ 登录失败，可能的原因:');
          console.log('   1. 用户名或密码不正确');
          console.log('   2. 需要验证码');
          console.log('   3. 账户被锁定');
          console.log('   4. 服务器端验证问题');
          
          // 尝试无登录访问主要页面
          console.log('\n🔄 尝试直接访问主要功能页面...');
          await testWithoutLogin(page);
        }
      } else {
        console.log('❌ 未找到登录按钮');
      }
    } else {
      console.log('❌ 未找到用户名或密码输入框');
    }
    
    console.log('\n🔍 浏览器保持打开状态，请手动检查登录过程...');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 测试主要功能（登录成功后）
async function testMainFeatures(page) {
  console.log('⚙️ 测试主要功能...');
  
  try {
    // 测试仪表盘
    await page.goto('http://dlsykgzq.w1.luyouxia.net/dashboard');
    await page.waitForTimeout(3000);
    console.log(`✅ 仪表盘访问: ${page.url()}`);
    
    // 测试公式配置
    await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config');
    await page.waitForTimeout(3000);
    console.log(`✅ 公式配置访问: ${page.url()}`);
    
  } catch (error) {
    console.log(`❌ 主要功能测试异常: ${error.message}`);
  }
}

// 测试无登录访问
async function testWithoutLogin(page) {
  console.log('🔓 测试无登录直接访问...');
  
  const testUrls = [
    'http://dlsykgzq.w1.luyouxia.net/',
    'http://dlsykgzq.w1.luyouxia.net/dashboard',
    'http://dlsykgzq.w1.luyouxia.net/formula-config'
  ];
  
  for (const url of testUrls) {
    try {
      await page.goto(url);
      await page.waitForTimeout(3000);
      
      const currentUrl = page.url();
      const isAccessible = !currentUrl.includes('login') && !currentUrl.includes('error');
      
      console.log(`${isAccessible ? '✅' : '❌'} ${url}: ${isAccessible ? '可访问' : '需要登录'}`);
      
      if (isAccessible) {
        // 测试基本功能
        const hasButtons = await page.locator('button').count() > 0;
        const hasInputs = await page.locator('input').count() > 0;
        
        console.log(`   📊 按钮: ${hasButtons ? '存在' : '不存在'}`);
        console.log(`   📝 输入框: ${hasInputs ? '存在' : '不存在'}`);
        
        if (hasButtons && hasInputs) {
          console.log('   🎉 页面功能可用，无需登录！');
          break;
        }
      }
    } catch (error) {
      console.log(`❌ 访问 ${url} 失败: ${error.message}`);
    }
  }
}

// 执行调试测试
debugLogin().catch(console.error);
