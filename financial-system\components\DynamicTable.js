'use client';

// 动态表格组件
// 根据模块配置动态生成表格列和数据处理

import { Table, Input, Button, Popconfirm } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { FormulaEngine } from '../lib/calculations';
import { getModuleConfig } from '../config/moduleConfigs';

export default function DynamicTable({
  moduleId,
  customers = [],
  loading = false,
  editingState = {},
  onCellClick,
  onCellEditCancel,
  onDataChange,
  onKeyDown,
  onDeleteCustomer
}) {
  const moduleConfig = getModuleConfig(moduleId);
  
  if (!moduleConfig) {
    return <div>模块配置未找到</div>;
  }

  // 检查单元格是否在编辑状态
  const isCellEditing = (record, field) => {
    return editingState[record.id] === field;
  };

  // 生成动态列配置
  const generateColumns = () => {
    const columns = [];
    const fields = moduleConfig.fields;

    // 遍历字段配置生成列
    Object.entries(fields).forEach(([fieldKey, fieldConfig]) => {
      // 跳过操作相关的内部字段
      if (['id', 'agentId', 'moduleId', 'createdAt', 'updatedAt'].includes(fieldKey)) {
        return;
      }

      const column = {
        title: fieldConfig.title,
        dataIndex: fieldKey,
        key: fieldKey,
        width: fieldConfig.type === 'text' ? 120 : 150,
        render: (value, record) => {
          const cellEditing = isCellEditing(record, fieldKey);

          // 如果字段不可编辑，直接显示格式化后的值
          if (!fieldConfig.editable) {
            return (
              <div style={{ 
                color: '#1890ff', 
                fontWeight: 'bold',
                textAlign: 'right'
              }}>
                {FormulaEngine.formatData.formatByType(value, fieldConfig.format)}
              </div>
            );
          }

          // 可编辑字段的渲染逻辑
          if (cellEditing) {
            return (
              <Input
                type={fieldConfig.type === 'text' ? 'text' : 'number'}
                step={fieldConfig.step || 0.01}
                value={value || ''}
                onChange={(e) => onDataChange(record.id, fieldKey, e.target.value)}
                onBlur={() => onCellEditCancel()}
                onKeyDown={(e) => onKeyDown(e, record, fieldKey)}
                placeholder={getPlaceholderText(fieldConfig)}
                autoFocus
                style={{ border: '2px solid #1890ff' }}
              />
            );
          }

          return (
            <div
              onClick={() => onCellClick(record, fieldKey)}
              style={{
                cursor: 'pointer',
                minHeight: '22px',
                padding: '4px 8px',
                borderRadius: '4px',
                transition: 'background-color 0.2s',
                textAlign: fieldConfig.type === 'text' ? 'left' : 'right'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
            >
              {fieldConfig.type === 'text' 
                ? (value || '') 
                : FormulaEngine.formatData.formatByType(value, fieldConfig.format)
              }
            </div>
          );
        }
      };

      columns.push(column);
    });

    // 添加操作列
    columns.push({
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_, record) => (
        <Popconfirm
          title="确定删除这个客户吗？"
          onConfirm={() => onDeleteCustomer(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />}
            size="small"
          >
            删除
          </Button>
        </Popconfirm>
      )
    });

    return columns;
  };

  // 获取输入框占位符文本
  const getPlaceholderText = (fieldConfig) => {
    switch (fieldConfig.format) {
      case 'integer':
        return '输入整数';
      case 'decimal':
        return '小数格式(如0.15代表15%)';
      case 'precision':
        return '高精度数字';
      case 'percentage':
        return '百分比';
      default:
        return `输入${fieldConfig.title}`;
    }
  };

  // 表格配置
  const tableProps = {
    columns: generateColumns(),
    dataSource: customers,
    loading,
    rowKey: 'id',
    pagination: {
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total) => `共 ${total} 条记录`,
      pageSize: 10,
      pageSizeOptions: ['10', '20', '50', '100']
    },
    scroll: { 
      x: 'max-content',
      y: 400 
    },
    size: 'middle',
    bordered: true,
    locale: {
      emptyText: (
        <div style={{ 
          padding: '40px', 
          textAlign: 'center',
          color: '#999'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
          <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无数据</div>
          <div style={{ fontSize: '14px' }}>
            点击"添加客户行"开始添加 {moduleConfig.displayName} 的客户数据
          </div>
        </div>
      )
    }
  };

  return (
    <div className="dynamic-table">
      <div style={{ 
        marginBottom: '16px', 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
          📋 {moduleConfig.displayName} - 客户数据表格
        </div>
        <div style={{ fontSize: '14px', color: '#666' }}>
          {customers.length} 条记录 | {moduleConfig.inputFields}个输入字段 + {moduleConfig.resultFields}个结果字段
        </div>
      </div>
      
      <Table {...tableProps} />
    </div>
  );
}
