# 界面3技术架构设计 v3.0

## 1. 架构概述

### 1.1 设计目标
基于老板展示的界面3效果，重新设计财务管理系统的界面架构：
- **公式配置网格布局** - 顶部显示所有公式表达式
- **详细数据表格** - 底部显示每个公式的详细数据
- **颜色编码系统** - 不同类型数据用不同颜色区分
- **保存植入功能** - 统一的数据保存和计算

### 1.2 核心技术栈
- **前端框架**: React 18 + Next.js 14
- **UI组件库**: Ant Design 5.x
- **状态管理**: React Context + useReducer
- **样式方案**: CSS Modules + Ant Design主题定制
- **数据处理**: 自定义公式计算引擎

## 2. 组件架构设计

### 2.1 组件层次结构
```
FormulaConfigurationPage (界面3主页面)
├── FormulaGridSection (公式配置网格区域)
│   ├── FormulaCard (公式卡片组件)
│   │   ├── FormulaNameTag (公式名称标签)
│   │   ├── FormulaExpressionDisplay (公式表达式显示)
│   │   └── FormulaEditModal (公式编辑弹窗)
│   └── SavePlantButton (保存植入按钮)
├── DetailedDataSection (详细数据区域)
│   ├── FormulaDataTable (公式数据表格)
│   │   ├── ColorCodedCell (颜色编码单元格)
│   │   ├── EditableCell (可编辑单元格)
│   │   └── CalculatedCell (计算结果单元格)
│   └── DataSummaryPanel (数据汇总面板)
└── NavigationTabs (导航标签页)
```

### 2.2 核心组件设计

#### 2.2.1 FormulaConfigurationPage (主页面组件)
```javascript
// components/pages/FormulaConfigurationPage.jsx
import React, { useState, useEffect, useContext } from 'react';
import { Layout, Row, Col, Card, Button, message } from 'antd';
import FormulaGridSection from '../sections/FormulaGridSection';
import DetailedDataSection from '../sections/DetailedDataSection';
import { FormulaContext } from '../../contexts/FormulaContext';

const FormulaConfigurationPage = () => {
  const { formulaConfig, updateFormula, saveAllFormulas } = useContext(FormulaContext);
  const [selectedFormula, setSelectedFormula] = useState(null);
  const [calculationResults, setCalculationResults] = useState({});

  return (
    <Layout className="formula-configuration-page">
      <Layout.Content>
        {/* 公式配置网格区域 */}
        <FormulaGridSection 
          formulaConfig={formulaConfig}
          onFormulaSelect={setSelectedFormula}
          onSave={handleSaveAll}
        />
        
        {/* 详细数据表格区域 */}
        <DetailedDataSection 
          selectedFormula={selectedFormula}
          calculationResults={calculationResults}
          onDataChange={handleDataChange}
        />
      </Layout.Content>
    </Layout>
  );
};
```

#### 2.2.2 FormulaGridSection (公式网格区域)
```javascript
// components/sections/FormulaGridSection.jsx
import React from 'react';
import { Row, Col, Card, Button, Space } from 'antd';
import FormulaCard from '../cards/FormulaCard';

const FormulaGridSection = ({ formulaConfig, onFormulaSelect, onSave }) => {
  const formulaGroups = [
    { id: 'formula1', name: '公式1', formulas: ['1.1', '1.2'] },
    { id: 'formula2', name: '公式2', formulas: ['2.1', '2.2'] },
    { id: 'formula3', name: '公式3', formulas: ['3.1', '3.2'] },
    { id: 'formula4', name: '公式4', formulas: ['4.1', '4.2'] },
    { id: 'formula5', name: '公式5', formulas: ['5.1', '5.2'] },
    { id: 'formula6', name: '公式6', formulas: ['6.1', '6.2'] },
    { id: 'formula7', name: '公式7', formulas: ['7.1', '7.2'] }
  ];

  return (
    <Card className="formula-grid-section" title="公式配置">
      <div className="formula-grid">
        {formulaGroups.map(group => (
          <Row key={group.id} gutter={[16, 16]} className="formula-row">
            {group.formulas.map(formulaNum => (
              <Col key={formulaNum} span={12}>
                <FormulaCard 
                  formulaId={`${group.id}_${formulaNum.replace('.', '_')}`}
                  formulaName={`${group.name}.${formulaNum}`}
                  expression={getFormulaExpression(group.id, formulaNum)}
                  onClick={() => onFormulaSelect(`${group.id}_${formulaNum}`)}
                />
              </Col>
            ))}
          </Row>
        ))}
      </div>
      
      <div className="save-section">
        <Button 
          type="primary" 
          size="large"
          className="save-plant-button"
          onClick={onSave}
        >
          保存植入
        </Button>
      </div>
    </Card>
  );
};
```

#### 2.2.3 FormulaCard (公式卡片组件)
```javascript
// components/cards/FormulaCard.jsx
import React from 'react';
import { Card, Tag, Typography } from 'antd';
import { EditOutlined } from '@ant-design/icons';

const { Text } = Typography;

const FormulaCard = ({ formulaId, formulaName, expression, onClick }) => {
  return (
    <Card 
      className="formula-card"
      size="small"
      hoverable
      onClick={onClick}
      actions={[<EditOutlined key="edit" />]}
    >
      <div className="formula-content">
        <Tag color="gold" className="formula-name-tag">
          {formulaName}
        </Tag>
        <Text className="formula-expression" code>
          {expression}
        </Text>
      </div>
    </Card>
  );
};
```

#### 2.2.4 DetailedDataSection (详细数据区域)
```javascript
// components/sections/DetailedDataSection.jsx
import React, { useState, useEffect } from 'react';
import { Card, Table, Row, Col } from 'antd';
import ColorCodedCell from '../cells/ColorCodedCell';

const DetailedDataSection = ({ selectedFormula, calculationResults, onDataChange }) => {
  const [dataSource, setDataSource] = useState([]);

  const columns = [
    {
      title: '客户',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 100,
      render: (text) => <ColorCodedCell value={text} type="customer" />
    },
    {
      title: '数据1',
      dataIndex: 'data1',
      key: 'data1',
      width: 100,
      render: (value, record) => (
        <ColorCodedCell 
          value={value} 
          type="input" 
          editable={true}
          onChange={(newValue) => handleCellChange(record.key, 'data1', newValue)}
        />
      )
    },
    {
      title: '数据2',
      dataIndex: 'data2',
      key: 'data2',
      width: 100,
      render: (value, record) => (
        <ColorCodedCell 
          value={value} 
          type="input" 
          editable={true}
          onChange={(newValue) => handleCellChange(record.key, 'data2', newValue)}
        />
      )
    },
    {
      title: '计算值',
      dataIndex: 'calculatedValue',
      key: 'calculatedValue',
      width: 120,
      render: (value) => <ColorCodedCell value={value} type="calculation" />
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 120,
      render: (value) => <ColorCodedCell value={value} type="result" />
    }
  ];

  return (
    <div className="detailed-data-section">
      <Row gutter={[16, 16]}>
        {/* 公式1详细数据 */}
        <Col span={8}>
          <Card title="公式1.1详细数据" size="small">
            <Table 
              columns={columns}
              dataSource={getFormulaData('formula1_1')}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="公式1.2详细数据" size="small">
            <Table 
              columns={columns}
              dataSource={getFormulaData('formula1_2')}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="公式1.3详细数据" size="small">
            <Table 
              columns={columns}
              dataSource={getFormulaData('formula1_3')}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
      
      {/* 更多公式数据行... */}
    </div>
  );
};
```

#### 2.2.5 ColorCodedCell (颜色编码单元格)
```javascript
// components/cells/ColorCodedCell.jsx
import React, { useState } from 'react';
import { Input, InputNumber } from 'antd';

const ColorCodedCell = ({ value, type, editable = false, onChange }) => {
  const [editing, setEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);

  const getBackgroundColor = (type) => {
    const colorMap = {
      'customer': '#FFFFFF',      // 白色 - 客户名称
      'input': '#FFFFFF',         // 白色 - 输入数据
      'calculation': '#87CEEB',   // 蓝色 - 计算数据
      'result': '#FFA500',        // 橙色 - 结果数据
      'summary': '#90EE90',       // 绿色 - 汇总数据
      'formula': '#FFD700'        // 黄色 - 公式
    };
    return colorMap[type] || '#FFFFFF';
  };

  const cellStyle = {
    backgroundColor: getBackgroundColor(type),
    border: '1px solid #D3D3D3',
    padding: '4px 8px',
    textAlign: type === 'customer' ? 'left' : 'right'
  };

  if (editable && editing) {
    return (
      <InputNumber
        value={currentValue}
        onChange={setCurrentValue}
        onBlur={() => {
          setEditing(false);
          onChange && onChange(currentValue);
        }}
        onPressEnter={() => {
          setEditing(false);
          onChange && onChange(currentValue);
        }}
        style={cellStyle}
        size="small"
      />
    );
  }

  return (
    <div 
      style={cellStyle}
      onClick={() => editable && setEditing(true)}
      className={editable ? 'editable-cell' : ''}
    >
      {typeof value === 'number' ? value.toLocaleString() : value}
    </div>
  );
};
```

## 3. 状态管理设计

### 3.1 FormulaContext (公式上下文)
```javascript
// contexts/FormulaContext.js
import React, { createContext, useReducer, useContext } from 'react';

const FormulaContext = createContext();

const initialState = {
  formulaConfig: {
    formula1: {
      '1_1': { name: '公式1.1', expression: '(data1+data2*data4)*data3' },
      '1_2': { name: '公式1.2', expression: '(data1+data2*data4)' }
    },
    formula2: {
      '2_1': { name: '公式2.1', expression: '(data1+data2*data4)*data3' },
      '2_2': { name: '公式2.2', expression: '(data1+data2*data4)*data3*data5' }
    },
    // ... 更多公式配置
  },
  detailedData: {},
  calculationResults: {},
  isDirty: false
};

const formulaReducer = (state, action) => {
  switch (action.type) {
    case 'UPDATE_FORMULA':
      return {
        ...state,
        formulaConfig: {
          ...state.formulaConfig,
          [action.formulaGroup]: {
            ...state.formulaConfig[action.formulaGroup],
            [action.formulaId]: action.formula
          }
        },
        isDirty: true
      };
    
    case 'UPDATE_DATA':
      return {
        ...state,
        detailedData: {
          ...state.detailedData,
          [action.formulaId]: action.data
        },
        isDirty: true
      };
    
    case 'SET_CALCULATION_RESULTS':
      return {
        ...state,
        calculationResults: action.results
      };
    
    case 'SAVE_SUCCESS':
      return {
        ...state,
        isDirty: false
      };
    
    default:
      return state;
  }
};

export const FormulaProvider = ({ children }) => {
  const [state, dispatch] = useReducer(formulaReducer, initialState);

  const updateFormula = (formulaGroup, formulaId, formula) => {
    dispatch({ type: 'UPDATE_FORMULA', formulaGroup, formulaId, formula });
  };

  const updateData = (formulaId, data) => {
    dispatch({ type: 'UPDATE_DATA', formulaId, data });
  };

  const calculateResults = () => {
    // 执行所有公式计算
    const results = {};
    // ... 计算逻辑
    dispatch({ type: 'SET_CALCULATION_RESULTS', results });
  };

  const saveAllFormulas = async () => {
    try {
      // 保存到服务端
      await saveFormulasToServer(state.formulaConfig, state.detailedData);
      dispatch({ type: 'SAVE_SUCCESS' });
      return true;
    } catch (error) {
      console.error('保存失败:', error);
      return false;
    }
  };

  return (
    <FormulaContext.Provider value={{
      ...state,
      updateFormula,
      updateData,
      calculateResults,
      saveAllFormulas
    }}>
      {children}
    </FormulaContext.Provider>
  );
};

export const useFormula = () => {
  const context = useContext(FormulaContext);
  if (!context) {
    throw new Error('useFormula must be used within a FormulaProvider');
  }
  return context;
};
```

## 4. 样式设计

### 4.1 CSS Modules样式
```css
/* styles/FormulaConfiguration.module.css */
.formulaConfigurationPage {
  padding: 24px;
  background: #f5f5f5;
}

.formulaGridSection {
  margin-bottom: 24px;
}

.formulaGrid {
  margin-bottom: 24px;
}

.formulaRow {
  margin-bottom: 16px;
}

.formulaCard {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s;
}

.formulaCard:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.formulaContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formulaNameTag {
  background-color: #FFD700;
  color: #000;
  font-weight: bold;
}

.formulaExpression {
  background-color: #FFF8DC;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.saveSection {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #d9d9d9;
}

.savePlantButton {
  background-color: #FFD700;
  border-color: #FFD700;
  color: #000;
  font-weight: bold;
  min-width: 120px;
}

.detailedDataSection {
  background: #fff;
  padding: 16px;
  border-radius: 6px;
}

.editableCell {
  cursor: pointer;
}

.editableCell:hover {
  background-color: #f0f8ff;
}
```

## 5. 数据流设计

### 5.1 数据流图
```
用户交互 → 组件状态更新 → Context状态管理 → 计算引擎 → 结果更新 → 界面渲染
    ↑                                                                    ↓
    ←─────────── 用户反馈 ←─────────── 界面更新 ←─────────── 状态同步 ←─────
```

### 5.2 计算引擎集成
```javascript
// utils/FormulaCalculationEngine.js
class FormulaCalculationEngine {
  constructor() {
    this.formulaDefinitions = {
      'formula1_1': '(data1+data2*data4)*data3',
      'formula1_2': '(data1+data2*data4)',
      'formula2_1': '(data1+data2*data4)*data3',
      // ... 更多公式定义
    };
  }

  calculateFormula(formulaId, inputData) {
    const expression = this.formulaDefinitions[formulaId];
    if (!expression) {
      throw new Error(`未找到公式: ${formulaId}`);
    }

    // 替换变量并计算
    let calculationExpression = expression;
    Object.keys(inputData).forEach(key => {
      const regex = new RegExp(key, 'g');
      calculationExpression = calculationExpression.replace(regex, inputData[key]);
    });

    try {
      // 安全的表达式计算
      const result = this.safeEval(calculationExpression);
      return {
        formulaId,
        expression,
        inputData,
        calculationExpression,
        result,
        success: true
      };
    } catch (error) {
      return {
        formulaId,
        expression,
        inputData,
        error: error.message,
        success: false
      };
    }
  }

  safeEval(expression) {
    // 安全的数学表达式计算
    const allowedChars = /^[0-9+\-*/.() ]+$/;
    if (!allowedChars.test(expression)) {
      throw new Error('表达式包含非法字符');
    }
    return Function(`"use strict"; return (${expression})`)();
  }
}
```

---

**Bob完成时间**: 2025年7月30日  
**下一步**: 提交给Alex进行具体实现  
**关键目标**: 实现界面3的完整布局和交互效果
