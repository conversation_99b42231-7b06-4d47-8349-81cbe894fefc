// 新汇总表计算引擎 v2.0
// 按模块汇总显示，支持右侧总和计算

import { MODULE_CONFIGS } from '../config/moduleConfigs';

export class NewSummaryCalculations {
  
  /**
   * 按模块汇总计算主函数
   * @param {Array} customers - 所有客户数据
   * @returns {Object} 按模块分组的汇总数据
   */
  static calculateModuleSummary(customers = []) {
    if (!customers || customers.length === 0) {
      return this.getEmptyModuleSummary();
    }

    // 按模块分组客户数据
    const moduleGroups = this.groupCustomersByModule(customers);
    
    // 计算各模块汇总数据
    const moduleSummaries = this.calculateEachModuleSummary(moduleGroups);
    
    // 计算右侧总和数据
    const rightSummary = this.calculateRightSummary(moduleSummaries);
    
    // 组织最终数据结构
    return {
      moduleGroups: this.organizeModuleGroups(moduleSummaries),
      rightSummary: rightSummary,
      metadata: {
        totalCustomers: customers.length,
        totalModules: Object.keys(moduleSummaries).length,
        lastUpdated: new Date()
      }
    };
  }

  /**
   * 按模块分组客户数据
   * @param {Array} customers - 客户数据
   * @returns {Object} 按模块分组的客户数据
   */
  static groupCustomersByModule(customers) {
    const groups = {};
    
    customers.forEach(customer => {
      const moduleId = customer.moduleId || 'module1_1';
      
      if (!groups[moduleId]) {
        groups[moduleId] = [];
      }
      
      groups[moduleId].push(customer);
    });
    
    return groups;
  }

  /**
   * 计算每个模块的汇总数据
   * @param {Object} moduleGroups - 按模块分组的客户数据
   * @returns {Object} 各模块汇总数据
   */
  static calculateEachModuleSummary(moduleGroups) {
    const summaries = {};
    
    Object.entries(moduleGroups).forEach(([moduleId, customers]) => {
      const moduleConfig = MODULE_CONFIGS[moduleId];
      if (!moduleConfig) return;
      
      const summary = {
        moduleId,
        moduleName: moduleConfig.displayName,
        customerCount: customers.length,
        dataFields: {},
        resultFields: {},
        totalSum: 0
      };
      
      // 计算数据字段汇总
      for (let i = 1; i <= 7; i++) {
        const fieldName = `data${i}`;
        summary.dataFields[fieldName] = customers.reduce((sum, customer) => 
          sum + (parseFloat(customer[fieldName]) || 0), 0);
      }
      
      // 计算结果字段汇总（重要：这是总和计算的基础）
      for (let i = 1; i <= 5; i++) {
        const fieldName = `result${i}`;
        summary.resultFields[fieldName] = customers.reduce((sum, customer) => 
          sum + (parseFloat(customer[fieldName]) || 0), 0);
      }
      
      // 计算模块总和（结果字段的总和）
      summary.totalSum = Object.values(summary.resultFields).reduce((sum, value) => sum + value, 0);
      
      summaries[moduleId] = summary;
    });
    
    return summaries;
  }

  /**
   * 组织模块分组数据
   * @param {Object} moduleSummaries - 各模块汇总数据
   * @returns {Array} 按要求分组的模块数据
   */
  static organizeModuleGroups(moduleSummaries) {
    return [
      // 第一组：模块1.1和1.2
      {
        groupName: "第一组",
        groupType: "normal",
        modules: [
          this.formatModuleForDisplay(moduleSummaries['module1_1'], {
            displayName: "客户1.1",
            dataFields: ["数据1", "数据2", "数据6", "数据7"],
            resultFields: ["结果1", "结果2", "结果3", "结果4", "结果5"]
          }),
          this.formatModuleForDisplay(moduleSummaries['module1_2'], {
            displayName: "客户1.2", 
            dataFields: ["数据1", "数据2", "数据6", "数据7"],
            resultFields: ["结果1", "结果2", "结果3", "结果4", "结果5"]
          })
        ]
      },
      
      // 第二组：模块1.3（特殊处理）
      {
        groupName: "第二组",
        groupType: "special",
        hasSeparator: true,
        modules: [
          this.formatModuleForDisplay(moduleSummaries['module1_3'], {
            displayName: "客户1.3",
            dataFields: ["数据1", "数据2"],
            resultFields: ["结果1"],
            specialConnection: {
              fromModule: "module1_2",
              fromField: "结果1",
              description: "接入1.2结果1数据",
              value: moduleSummaries['module1_2']?.resultFields?.result1 || 0
            }
          })
        ]
      },
      
      // 第三组：模块1.4和1.5
      {
        groupName: "第三组", 
        groupType: "normal",
        modules: [
          this.formatModuleForDisplay(moduleSummaries['module1_4'], {
            displayName: "客户1.4",
            dataFields: ["数据1", "数据2", "数据6", "数据7"],
            resultFields: ["结果1", "结果2", "结果3", "结果4", "结果5"]
          }),
          this.formatModuleForDisplay(moduleSummaries['module1_5'], {
            displayName: "客户1.5",
            dataFields: ["数据1", "数据2", "数据6", "数据7"], 
            resultFields: ["结果1", "结果2", "结果3", "结果4", "结果5"]
          })
        ]
      },
      
      // 第四组：模块1.6和1.7
      {
        groupName: "第四组",
        groupType: "compact", // 紧凑布局
        modules: [
          this.formatModuleForDisplay(moduleSummaries['module1_6'], {
            displayName: "客户1.6",
            dataFields: ["数据1", "数据2", "数据3"],
            resultFields: ["结果1", "结果2", "结果3", "结果4"]
          }),
          this.formatModuleForDisplay(moduleSummaries['module1_7'], {
            displayName: "客户1.7",
            dataFields: ["数据1", "数据2", "数据3"],
            resultFields: ["结果1", "结果2", "结果3", "结果4"]
          })
        ]
      }
    ];
  }

  /**
   * 格式化模块数据用于显示
   * @param {Object} moduleSummary - 模块汇总数据
   * @param {Object} displayConfig - 显示配置
   * @returns {Object} 格式化后的模块数据
   */
  static formatModuleForDisplay(moduleSummary, displayConfig) {
    if (!moduleSummary) {
      return {
        moduleId: displayConfig.moduleId || 'unknown',
        displayName: displayConfig.displayName,
        customerCount: 0,
        dataFields: {},
        resultFields: {},
        totalSum: 0,
        isEmpty: true
      };
    }
    
    const formatted = {
      moduleId: moduleSummary.moduleId,
      displayName: displayConfig.displayName,
      customerCount: moduleSummary.customerCount,
      dataFields: {},
      resultFields: {},
      totalSum: moduleSummary.totalSum,
      isEmpty: false
    };
    
    // 只显示配置中指定的数据字段
    displayConfig.dataFields.forEach((fieldName, index) => {
      const dataKey = `data${index + 1}`;
      formatted.dataFields[fieldName] = moduleSummary.dataFields[dataKey] || 0;
    });
    
    // 只显示配置中指定的结果字段
    displayConfig.resultFields.forEach((fieldName, index) => {
      const resultKey = `result${index + 1}`;
      formatted.resultFields[fieldName] = moduleSummary.resultFields[resultKey] || 0;
    });
    
    // 特殊连接处理
    if (displayConfig.specialConnection) {
      formatted.specialConnection = displayConfig.specialConnection;
    }
    
    return formatted;
  }

  /**
   * 计算右侧总和数据
   * @param {Object} moduleSummaries - 各模块汇总数据
   * @returns {Object} 右侧总和数据
   */
  static calculateRightSummary(moduleSummaries) {
    // 计算所有模块结果字段的总和
    const allModulesTotalSum = Object.values(moduleSummaries).reduce((sum, module) => 
      sum + (module.totalSum || 0), 0);
    
    // 基础计算数据
    const baseCalculation = {
      收入: allModulesTotalSum, // 所有结果字段的总和
      付出: -allModulesTotalSum, // 负数
    };
    
    // 手动输入数据（从localStorage或数据库获取）
    const manualInputs = this.getManualInputs();
    
    // 上周余额（从历史数据获取）
    const previousWeekBalance = this.getPreviousWeekBalance();
    
    // 计算6项指标
    const indicators = {
      收入: baseCalculation.收入,
      付出: baseCalculation.付出,
      收付现金: manualInputs.cashFlow || 0,
      上周余额: previousWeekBalance,
      减免: manualInputs.discount || 0,
      所有总和合计: 0 // 将在下面计算
    };
    
    // 计算总和合计
    indicators.所有总和合计 = indicators.收入 + indicators.付出 + 
                          indicators.收付现金 + indicators.上周余额 + indicators.减免;
    
    // 返回包含精确值和四舍五入值的数据
    return {
      precise: indicators, // 精确数字
      rounded: this.roundToHundred(indicators), // 四舍五入到百位
      metadata: {
        calculationTime: new Date(),
        totalModules: Object.keys(moduleSummaries).length,
        baseSum: allModulesTotalSum
      }
    };
  }

  /**
   * 获取手动输入数据
   * @returns {Object} 手动输入的数据
   */
  static getManualInputs() {
    // 从localStorage获取手动输入的数据
    try {
      const stored = localStorage.getItem('summaryManualInputs');
      return stored ? JSON.parse(stored) : { cashFlow: 0, discount: 0 };
    } catch (error) {
      console.warn('获取手动输入数据失败:', error);
      return { cashFlow: 0, discount: 0 };
    }
  }

  /**
   * 保存手动输入数据
   * @param {Object} inputs - 手动输入的数据
   */
  static saveManualInputs(inputs) {
    try {
      localStorage.setItem('summaryManualInputs', JSON.stringify(inputs));
    } catch (error) {
      console.warn('保存手动输入数据失败:', error);
    }
  }

  /**
   * 获取上周余额
   * @returns {number} 上周余额
   */
  static getPreviousWeekBalance() {
    // 这里应该从历史数据或数据库获取上周的余额
    // 暂时返回模拟数据
    try {
      const stored = localStorage.getItem('previousWeekBalance');
      return stored ? parseFloat(stored) : 0;
    } catch (error) {
      console.warn('获取上周余额失败:', error);
      return 0;
    }
  }

  /**
   * 四舍五入到百位
   * @param {Object} indicators - 指标数据
   * @returns {Object} 四舍五入后的数据
   */
  static roundToHundred(indicators) {
    const rounded = {};
    Object.entries(indicators).forEach(([key, value]) => {
      rounded[key] = Math.round(value / 100) * 100;
    });
    return rounded;
  }

  /**
   * 获取空的模块汇总数据
   * @returns {Object} 空数据结构
   */
  static getEmptyModuleSummary() {
    return {
      moduleGroups: [],
      rightSummary: {
        precise: {
          收入: 0, 付出: 0, 收付现金: 0, 上周余额: 0, 减免: 0, 所有总和合计: 0
        },
        rounded: {
          收入: 0, 付出: 0, 收付现金: 0, 上周余额: 0, 减免: 0, 所有总和合计: 0
        }
      },
      metadata: {
        totalCustomers: 0,
        totalModules: 0,
        lastUpdated: new Date()
      }
    };
  }

  /**
   * 格式化显示数值
   * @param {number} value - 数值
   * @param {string} type - 格式类型
   * @returns {string} 格式化后的字符串
   */
  static formatDisplayValue(value, type = 'number') {
    if (value === null || value === undefined || isNaN(value)) return '-';
    
    switch (type) {
      case 'currency':
        return new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY'
        }).format(value);
      case 'percentage':
        return `${value.toFixed(2)}%`;
      case 'integer':
        return Math.round(value).toLocaleString();
      case 'decimal':
        return value.toFixed(2);
      default:
        return value.toLocaleString();
    }
  }
}
