// API路由 - 汇总表数据植入
// POST /api/summary/plant - 将模块数据植入汇总表

import { NextResponse } from 'next/server';
import { MODULE_CONFIGS } from '../../../../config/moduleConfigs';

/**
 * 将模块数据植入汇总表
 * POST /api/summary/plant
 * Body: { agentId: number, moduleId: string, customerData: object[] }
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { agentId, moduleId, customerData } = body;
    
    // 验证输入参数
    if (!agentId || !moduleId || !customerData) {
      return NextResponse.json(
        { success: false, error: '缺少必要参数' },
        { status: 400 }
      );
    }
    
    if (!Array.isArray(customerData) || customerData.length === 0) {
      return NextResponse.json(
        { success: false, error: '客户数据不能为空' },
        { status: 400 }
      );
    }
    
    // 验证模块配置
    const moduleConfig = MODULE_CONFIGS[moduleId];
    if (!moduleConfig) {
      return NextResponse.json(
        { success: false, error: '模块配置不存在' },
        { status: 404 }
      );
    }
    
    const plantResults = [];
    const errors = [];
    
    // 处理每个客户数据
    for (const customer of customerData) {
      try {
        const plantResult = await plantCustomerData(agentId, moduleId, customer, moduleConfig);
        plantResults.push(plantResult);
      } catch (error) {
        console.error(`植入客户数据失败:`, error);
        errors.push({
          customerName: customer.name || '未知客户',
          error: error.message
        });
      }
    }
    
    // 统计结果
    const successCount = plantResults.filter(r => r.success).length;
    const failedCount = errors.length;
    
    return NextResponse.json({
      success: failedCount === 0,
      data: {
        agentId: parseInt(agentId),
        moduleId,
        summary: {
          total: customerData.length,
          success: successCount,
          failed: failedCount
        },
        results: plantResults,
        errors: errors
      }
    });
    
  } catch (error) {
    console.error('汇总表植入失败:', error);
    return NextResponse.json(
      { success: false, error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

/**
 * 植入单个客户数据到汇总表
 * @param {number} agentId - 代理商ID
 * @param {string} moduleId - 模块ID
 * @param {object} customerData - 客户数据
 * @param {object} moduleConfig - 模块配置
 * @returns {Promise<object>} 植入结果
 */
async function plantCustomerData(agentId, moduleId, customerData, moduleConfig) {
  const startTime = Date.now();
  
  try {
    // 1. 数据验证
    const validation = validateCustomerData(customerData, moduleConfig);
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.error}`);
    }
    
    // 2. 计算公式结果
    const calculatedResults = calculateModuleResults(customerData, moduleConfig);
    
    // 3. 准备汇总表数据
    const summaryData = prepareSummaryData(customerData, calculatedResults, moduleConfig);
    
    // 4. 模拟植入到汇总表（实际项目中这里会调用数据库操作）
    console.log(`植入客户 ${customerData.name} 的数据到汇总表:`, summaryData);
    
    const duration = Date.now() - startTime;
    
    return {
      success: true,
      customerName: customerData.name,
      moduleId,
      summaryData,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    return {
      success: false,
      customerName: customerData.name || '未知客户',
      moduleId,
      error: error.message,
      duration
    };
  }
}

/**
 * 验证客户数据
 * @param {object} customerData - 客户数据
 * @param {object} moduleConfig - 模块配置
 * @returns {object} 验证结果
 */
function validateCustomerData(customerData, moduleConfig) {
  // 检查必要字段
  if (!customerData.name || customerData.name.trim().length === 0) {
    return { isValid: false, error: '客户名称不能为空' };
  }
  
  // 检查数据字段
  const requiredDataFields = ['data1', 'data2', 'data3', 'data4', 'data5'];
  
  for (const fieldKey of requiredDataFields) {
    const value = customerData[fieldKey];
    if (value === undefined || value === null || value === '') {
      return { isValid: false, error: `字段 ${fieldKey} 不能为空` };
    }
    
    if (typeof value !== 'number' || isNaN(value)) {
      return { isValid: false, error: `字段 ${fieldKey} 必须是有效数字` };
    }
  }
  
  return { isValid: true };
}

/**
 * 计算模块结果
 * @param {object} customerData - 客户数据
 * @param {object} moduleConfig - 模块配置
 * @returns {object} 计算结果
 */
function calculateModuleResults(customerData, moduleConfig) {
  const results = {};
  
  try {
    // 获取自定义公式或使用默认公式
    const formulas = moduleConfig.customNames?.formulas || {};
    const defaultFormulas = moduleConfig.defaultFormulas || {};
    
    // 计算每个结果字段
    Object.keys(moduleConfig.resultMapping || {}).forEach(resultKey => {
      const formulaName = moduleConfig.resultMapping[resultKey];
      
      // 查找对应的公式
      let formulaExpression = null;
      
      // 先查找自定义公式
      const customFormula = Object.values(formulas).find(f => 
        f.customName === formulaName || f.defaultName === formulaName
      );
      
      if (customFormula && customFormula.expression) {
        formulaExpression = customFormula.expression;
      } else if (defaultFormulas[formulaName]) {
        formulaExpression = defaultFormulas[formulaName];
      }
      
      if (formulaExpression) {
        try {
          // 简单的公式计算（实际项目中应该使用更安全的计算引擎）
          let calcExpression = formulaExpression;
          Object.keys(customerData).forEach(key => {
            if (key.startsWith('data')) {
              calcExpression = calcExpression.replace(new RegExp(key, 'g'), customerData[key]);
            }
          });
          
          const result = Function(`"use strict"; return (${calcExpression})`)();
          results[resultKey] = result;
        } catch (calcError) {
          console.warn(`计算公式 ${formulaName} 失败:`, calcError);
          results[resultKey] = 0;
        }
      } else {
        results[resultKey] = 0;
      }
    });
    
  } catch (error) {
    console.error('计算模块结果失败:', error);
  }
  
  return results;
}

/**
 * 准备汇总表数据
 * @param {object} customerData - 客户数据
 * @param {object} calculatedResults - 计算结果
 * @param {object} moduleConfig - 模块配置
 * @returns {object} 汇总表数据
 */
function prepareSummaryData(customerData, calculatedResults, moduleConfig) {
  const summaryData = {
    // 基础信息
    customerName: customerData.name,
    userName: customerData.userName || customerData.name,
    
    // 数据字段（使用自定义名称）
    ...customerData,
    
    // 计算结果
    ...calculatedResults,
    
    // 汇总统计
    totalSum: Object.values(calculatedResults).reduce((sum, val) => sum + (val || 0), 0),
    
    // 元数据
    moduleId: moduleConfig.id,
    moduleName: moduleConfig.displayName,
    lastUpdated: new Date().toISOString()
  };
  
  return summaryData;
}
