// 主汇总表更新API
// 处理客户详细表计算结果到主汇总表的自动更新

import { NextResponse } from 'next/server';

// 模拟主汇总表数据存储
let mainSummaryData = {
  '2': { customerId: '2', customerName: '2', summaryValue: 300, lastUpdated: new Date().toISOString() },
  '55': { customerId: '55', customerName: '55', summaryValue: 17400, lastUpdated: new Date().toISOString() },
  'cc': { customerId: 'cc', customerName: 'cc', summaryValue: 39200, lastUpdated: new Date().toISOString() },
  '大妈粉': { customerId: '大妈粉', customerName: '大妈粉', summaryValue: -3400, lastUpdated: new Date().toISOString() },
  '果子': { customerId: '果子', customerName: '果子', summaryValue: 53300, lastUpdated: new Date().toISOString() },
  '太空': { customerId: '太空', customerName: '太空', summaryValue: 73800, lastUpdated: new Date().toISOString() },
  '大大': { customerId: '大大', customerName: '大大', summaryValue: 48500, lastUpdated: new Date().toISOString() },
  '嘻嘻嘻': { customerId: '嘻嘻嘻', customerName: '嘻嘻嘻', summaryValue: 158700, lastUpdated: new Date().toISOString() },
  '小李': { customerId: '小李', customerName: '小李', summaryValue: 327000, lastUpdated: new Date().toISOString() },
  '张': { customerId: '张', customerName: '张', summaryValue: 3300, lastUpdated: new Date().toISOString() }
};

// PUT /api/main-summary/update - 更新主汇总表数据
export async function PUT(request) {
  try {
    const body = await request.json();
    const { customerId, customerName, summaryValue, sourceModule, calculationDetails } = body;

    // 验证必需参数
    if (!customerId || summaryValue === undefined) {
      return NextResponse.json(
        { 
          success: false, 
          error: '缺少必需参数：customerId 和 summaryValue' 
        },
        { status: 400 }
      );
    }

    // 验证数值类型
    const numericValue = parseFloat(summaryValue);
    if (isNaN(numericValue)) {
      return NextResponse.json(
        { 
          success: false, 
          error: '汇总值必须是有效数字' 
        },
        { status: 400 }
      );
    }

    // 更新主汇总表数据
    const previousValue = mainSummaryData[customerId]?.summaryValue || 0;
    
    mainSummaryData[customerId] = {
      customerId,
      customerName: customerName || customerId,
      summaryValue: numericValue,
      previousValue,
      sourceModule: sourceModule || 'unknown',
      calculationDetails: calculationDetails || {},
      lastUpdated: new Date().toISOString(),
      updateCount: (mainSummaryData[customerId]?.updateCount || 0) + 1
    };

    // 记录更新日志
    console.log(`主汇总表更新: ${customerId} = ${numericValue} (之前: ${previousValue})`);

    // 触发周报表和月报表重新计算
    await triggerReportRecalculation(customerId, numericValue, sourceModule);

    return NextResponse.json({
      success: true,
      message: '主汇总表更新成功',
      data: {
        customerId,
        customerName: customerName || customerId,
        summaryValue: numericValue,
        previousValue,
        changeAmount: numericValue - previousValue,
        lastUpdated: mainSummaryData[customerId].lastUpdated,
        updateCount: mainSummaryData[customerId].updateCount
      }
    });

  } catch (error) {
    console.error('主汇总表更新失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// GET /api/main-summary/update - 获取主汇总表数据
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customerId');

    if (customerId) {
      // 获取特定客户的汇总数据
      const customerData = mainSummaryData[customerId];
      
      if (!customerData) {
        return NextResponse.json(
          { 
            success: false, 
            error: '客户不存在' 
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: customerData
      });
    } else {
      // 获取所有客户的汇总数据
      return NextResponse.json({
        success: true,
        data: Object.values(mainSummaryData),
        totalCustomers: Object.keys(mainSummaryData).length,
        lastUpdated: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('获取主汇总表数据失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// 触发周报表和月报表重新计算
async function triggerReportRecalculation(customerId, summaryValue, sourceModule) {
  try {
    // 这里可以调用周报表和月报表的重新计算逻辑
    console.log(`触发报表重新计算: 客户${customerId}, 值${summaryValue}, 来源${sourceModule}`);
    
    // 模拟异步处理
    setTimeout(() => {
      console.log(`报表重新计算完成: ${customerId}`);
    }, 1000);

    return true;
  } catch (error) {
    console.error('触发报表重新计算失败:', error);
    return false;
  }
}

// POST /api/main-summary/update - 批量更新主汇总表
export async function POST(request) {
  try {
    const body = await request.json();
    const { updates } = body;

    if (!Array.isArray(updates) || updates.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: '批量更新数据格式错误' 
        },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // 处理每个更新请求
    for (const update of updates) {
      try {
        const { customerId, customerName, summaryValue, sourceModule } = update;
        
        if (!customerId || summaryValue === undefined) {
          errors.push({ customerId, error: '缺少必需参数' });
          continue;
        }

        const numericValue = parseFloat(summaryValue);
        if (isNaN(numericValue)) {
          errors.push({ customerId, error: '汇总值必须是有效数字' });
          continue;
        }

        const previousValue = mainSummaryData[customerId]?.summaryValue || 0;
        
        mainSummaryData[customerId] = {
          customerId,
          customerName: customerName || customerId,
          summaryValue: numericValue,
          previousValue,
          sourceModule: sourceModule || 'batch_update',
          lastUpdated: new Date().toISOString(),
          updateCount: (mainSummaryData[customerId]?.updateCount || 0) + 1
        };

        results.push({
          customerId,
          summaryValue: numericValue,
          previousValue,
          changeAmount: numericValue - previousValue,
          success: true
        });

      } catch (error) {
        errors.push({ customerId: update.customerId, error: error.message });
      }
    }

    return NextResponse.json({
      success: true,
      message: `批量更新完成: ${results.length}成功, ${errors.length}失败`,
      results,
      errors,
      totalProcessed: updates.length
    });

  } catch (error) {
    console.error('批量更新主汇总表失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '服务器内部错误',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
