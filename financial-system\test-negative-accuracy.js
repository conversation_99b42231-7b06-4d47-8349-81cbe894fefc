// 专门测试负数和公式准确性
const { chromium } = require('playwright');

async function testNegativeAndAccuracy() {
  console.log('🎯 专门测试负数和公式准确性');
  console.log('🌐 URL: http://dlsykgzq.w1.luyouxia.net/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const page = await browser.newPage();
  
  // 监听页面日志
  page.on('console', msg => {
    if (msg.type() === 'log') {
      console.log(`🌐 页面日志: ${msg.text()}`);
    }
  });
  
  try {
    // 1. 访问页面
    console.log('📊 步骤1: 访问公式配置页面...');
    await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config/', {
      timeout: 0
    });
    
    await page.waitForTimeout(5000);
    console.log('✅ 页面加载完成');
    
    // 2. 查找添加客户按钮
    console.log('\n📊 步骤2: 查找添加客户功能...');
    const addButtons = await page.$$('button:has-text("添加客户"), button:has-text("添加"), button:has-text("新增")');
    console.log(`🔘 找到 ${addButtons.length} 个添加相关按钮`);
    
    if (addButtons.length > 0) {
      // 尝试每个按钮，找到真正的添加客户按钮
      for (let i = 0; i < addButtons.length; i++) {
        const buttonText = await addButtons[i].textContent();
        console.log(`   按钮${i+1}: "${buttonText}"`);
        
        if (buttonText.includes('添加客户') || buttonText.includes('添加') || buttonText.includes('新增')) {
          console.log(`🔘 点击按钮: "${buttonText}"`);
          await addButtons[i].click();
          await page.waitForTimeout(3000);
          break;
        }
      }
    }
    
    // 3. 获取所有输入框
    console.log('\n📊 步骤3: 获取输入框...');
    const inputs = await page.$$('input[type="text"], input[type="number"], table input');
    console.log(`📝 找到 ${inputs.length} 个输入框`);
    
    if (inputs.length < 4) {
      console.log('❌ 输入框数量不足，无法进行完整测试');
      return;
    }
    
    // 4. 执行多个测试用例
    const testCases = [
      {
        name: '基础正数测试',
        data: { data1: 100, data2: 2, data3: 50, data4: 1 },
        expected: {
          formula1_1: 153, // (100+2+1)+50 = 153
          formula2_1: 200, // 100*2 = 200
          formula2_2: 2,   // 2*1 = 2
          formula3_1: 100  // 100
        }
      },
      {
        name: '基础负数测试',
        data: { data1: -100, data2: -2, data3: -50, data4: -1 },
        expected: {
          formula1_1: -153, // (-100+-2+-1)+(-50) = -153
          formula2_1: 200,  // (-100)*(-2) = 200
          formula2_2: 2,    // (-2)*(-1) = 2
          formula3_1: -100  // -100
        }
      },
      {
        name: '正负混合测试',
        data: { data1: 100, data2: -2, data3: 50, data4: -1 },
        expected: {
          formula1_1: 147, // (100+-2+-1)+50 = 147
          formula2_1: -200, // 100*(-2) = -200
          formula2_2: 2,    // (-2)*(-1) = 2
          formula3_1: 100   // 100
        }
      },
      {
        name: '小负数测试',
        data: { data1: -10, data2: -0.5, data3: -5, data4: -0.1 },
        expected: {
          formula1_1: -15.6, // (-10+-0.5+-0.1)+(-5) = -15.6
          formula2_1: 5,     // (-10)*(-0.5) = 5
          formula2_2: 0.05,  // (-0.5)*(-0.1) = 0.05
          formula3_1: -10    // -10
        }
      },
      {
        name: '零值边界测试',
        data: { data1: 0, data2: -5, data3: 0, data4: 2 },
        expected: {
          formula1_1: -3,  // (0+-5+2)+0 = -3
          formula2_1: 0,   // 0*(-5) = 0
          formula2_2: -10, // (-5)*2 = -10
          formula3_1: 0    // 0
        }
      }
    ];
    
    // 执行每个测试用例
    for (const testCase of testCases) {
      await executeTestCase(page, inputs, testCase);
      await page.waitForTimeout(2000);
    }
    
    // 5. 生成综合报告
    generateComprehensiveReport();
    
    console.log('\n🎉 所有测试完成！');
    console.log('🔍 浏览器保持打开状态，请手动验证结果...');
    console.log('💡 特别关注：');
    console.log('   1. 负数输入是否被正确接受');
    console.log('   2. 负数计算结果是否准确');
    console.log('   3. 正负数混合运算是否正确');
    console.log('   4. 边界值（如0）的处理是否正确');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 全局测试结果存储
let allTestResults = [];

// 执行单个测试用例
async function executeTestCase(page, inputs, testCase) {
  console.log(`\n🧪 执行测试: ${testCase.name}`);
  console.log(`📊 测试数据: data1=${testCase.data.data1}, data2=${testCase.data.data2}, data3=${testCase.data.data3}, data4=${testCase.data.data4}`);
  
  try {
    // 清空所有输入框
    for (let i = 0; i < Math.min(4, inputs.length); i++) {
      await inputs[i].clear();
      await page.waitForTimeout(200);
    }
    
    // 输入测试数据
    const dataArray = [testCase.data.data1, testCase.data.data2, testCase.data.data3, testCase.data.data4];
    
    for (let i = 0; i < 4; i++) {
      if (i < inputs.length && dataArray[i] !== undefined) {
        await inputs[i].fill(dataArray[i].toString());
        await page.waitForTimeout(300);
        
        // 验证输入值
        const actualValue = await inputs[i].inputValue();
        const expectedValue = dataArray[i].toString();
        const inputCorrect = actualValue === expectedValue;
        
        console.log(`   📝 输入data${i+1}: 期望${expectedValue}, 实际${actualValue} ${inputCorrect ? '✅' : '❌'}`);
        
        if (!inputCorrect && dataArray[i] < 0) {
          console.log(`   ⚠️ 负数输入可能有问题: data${i+1}`);
        }
      }
    }
    
    // 触发计算
    console.log('   ⚡ 触发计算...');
    await page.keyboard.press('Tab');
    await page.waitForTimeout(3000);
    
    // 获取计算结果
    const results = await page.$$('strong');
    console.log(`   📊 找到 ${results.length} 个结果元素`);
    
    const calculatedResults = [];
    for (let i = 0; i < results.length; i++) {
      try {
        const text = await results[i].textContent();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          calculatedResults.push(num);
        }
      } catch (e) {}
    }
    
    console.log('   📈 计算结果验证:');
    
    // 验证每个公式
    const verificationResults = {};
    
    // 公式1.1: (data1+data2+data4)+data3
    const expected1_1 = testCase.expected.formula1_1;
    const found1_1 = calculatedResults.find(r => Math.abs(r - expected1_1) < 0.01);
    verificationResults.formula1_1 = !!found1_1;
    console.log(`      公式1.1 (data1+data2+data4)+data3: 预期${expected1_1}, ${found1_1 ? `✅找到${found1_1}` : '❌未找到'}`);
    
    // 公式2.1: data1*data2
    const expected2_1 = testCase.expected.formula2_1;
    const found2_1 = calculatedResults.find(r => Math.abs(r - expected2_1) < 0.01);
    verificationResults.formula2_1 = !!found2_1;
    console.log(`      公式2.1 data1*data2: 预期${expected2_1}, ${found2_1 ? `✅找到${found2_1}` : '❌未找到'}`);
    
    // 公式2.2: data2*data4
    const expected2_2 = testCase.expected.formula2_2;
    const found2_2 = calculatedResults.find(r => Math.abs(r - expected2_2) < 0.01);
    verificationResults.formula2_2 = !!found2_2;
    console.log(`      公式2.2 data2*data4: 预期${expected2_2}, ${found2_2 ? `✅找到${found2_2}` : '❌未找到'}`);
    
    // 公式3.1: data1
    const expected3_1 = testCase.expected.formula3_1;
    const found3_1 = calculatedResults.find(r => Math.abs(r - expected3_1) < 0.01);
    verificationResults.formula3_1 = !!found3_1;
    console.log(`      公式3.1 data1: 预期${expected3_1}, ${found3_1 ? `✅找到${found3_1}` : '❌未找到'}`);
    
    // 计算成功率
    const totalFormulas = Object.keys(verificationResults).length;
    const passedFormulas = Object.values(verificationResults).filter(v => v).length;
    const successRate = (passedFormulas / totalFormulas * 100).toFixed(1);
    
    console.log(`   📊 ${testCase.name}验证结果: ${passedFormulas}/${totalFormulas} 正确 (${successRate}%)`);
    
    // 存储测试结果
    allTestResults.push({
      name: testCase.name,
      data: testCase.data,
      expected: testCase.expected,
      results: calculatedResults,
      verifications: verificationResults,
      successRate: parseFloat(successRate),
      passedFormulas,
      totalFormulas
    });
    
    // 特别关注负数处理
    if (testCase.name.includes('负数')) {
      console.log('   🔍 负数处理分析:');
      const hasNegativeInputs = Object.values(testCase.data).some(v => v < 0);
      const hasNegativeResults = calculatedResults.some(r => r < 0);
      
      console.log(`      输入包含负数: ${hasNegativeInputs ? '✅是' : '❌否'}`);
      console.log(`      结果包含负数: ${hasNegativeResults ? '✅是' : '❌否'}`);
      
      if (hasNegativeInputs && !hasNegativeResults) {
        console.log('      ⚠️ 警告: 输入了负数但结果中没有负数，可能存在问题');
      }
    }
    
  } catch (error) {
    console.log(`   ❌ ${testCase.name}执行异常: ${error.message}`);
  }
}

// 生成综合报告
function generateComprehensiveReport() {
  console.log('\n' + '='.repeat(80));
  console.log('🎯 负数和公式准确性综合测试报告');
  console.log('='.repeat(80));
  
  const totalTests = allTestResults.length;
  const avgSuccessRate = allTestResults.reduce((sum, result) => sum + result.successRate, 0) / totalTests;
  
  console.log(`📊 测试统计:`);
  console.log(`   总测试用例: ${totalTests}`);
  console.log(`   平均成功率: ${avgSuccessRate.toFixed(1)}%`);
  
  console.log('\n📈 各测试用例详细结果:');
  allTestResults.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.name}`);
    console.log(`   成功率: ${result.successRate}%`);
    console.log(`   通过公式: ${result.passedFormulas}/${result.totalFormulas}`);
    
    if (result.successRate < 100) {
      console.log(`   ⚠️ 需要关注的公式:`);
      Object.entries(result.verifications).forEach(([formula, passed]) => {
        if (!passed) {
          console.log(`      - ${formula}: 验证失败`);
        }
      });
    }
  });
  
  // 负数处理专项分析
  console.log('\n🔍 负数处理专项分析:');
  const negativeTests = allTestResults.filter(r => r.name.includes('负数') || Object.values(r.data).some(v => v < 0));
  
  if (negativeTests.length > 0) {
    const negativeAvgRate = negativeTests.reduce((sum, result) => sum + result.successRate, 0) / negativeTests.length;
    console.log(`   负数相关测试平均成功率: ${negativeAvgRate.toFixed(1)}%`);
    
    if (negativeAvgRate >= 80) {
      console.log('   ✅ 负数处理良好');
    } else {
      console.log('   ⚠️ 负数处理需要改进');
    }
  }
  
  // 总结
  console.log('\n🎯 测试总结:');
  if (avgSuccessRate >= 90) {
    console.log('🎉 公式计算功能优秀！负数处理和计算准确性都很好。');
  } else if (avgSuccessRate >= 70) {
    console.log('⚠️ 公式计算功能良好，但有一些问题需要关注。');
  } else {
    console.log('❌ 公式计算功能需要改进，特别是负数处理方面。');
  }
  
  console.log('='.repeat(80));
}

// 执行测试
testNegativeAndAccuracy().catch(console.error);
