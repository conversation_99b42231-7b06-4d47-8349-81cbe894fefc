// 财务管理系统公式计算浏览器测试脚本
// 使用Playwright在真实浏览器中测试公式计算准确性

const { chromium } = require('playwright');

class BrowserFormulaCalculationTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
    this.baseUrl = 'http://localhost:3000';
    this.formulaResults = new Map(); // 存储实际的公式计算结果
  }

  // 记录测试结果
  logResult(testName, passed, details = '') {
    const result = {
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    };
    this.testResults.push(result);
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
  }

  // 手动计算预期结果
  calculateExpected(expression, data) {
    if (!expression) return 0;

    try {
      // 安全的表达式计算
      let safeExpression = expression
        .replace(/data1/g, `(${data.data1 || 0})`)
        .replace(/data2/g, `(${data.data2 || 0})`)
        .replace(/data3/g, `(${data.data3 || 0})`)
        .replace(/data4/g, `(${data.data4 || 0})`)
        .replace(/data5/g, `(${data.data5 || 0})`)
        .replace(/data6/g, `(${data.data6 || 0})`)
        .replace(/data7/g, `(${data.data7 || 0})`);

      const result = eval(safeExpression);
      return Math.round(result); // 四舍五入到整数
    } catch (error) {
      console.log(`❌ 计算预期结果失败: ${expression}, 错误: ${error.message}`);
      return 0;
    }
  }

  // 初始化浏览器
  async setup() {
    console.log('🚀 启动浏览器进行公式测试...');
    this.browser = await chromium.launch({
      headless: false,  // 显示浏览器界面
      slowMo: 2000,     // 慢速执行，便于观察
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const context = await this.browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    this.page = await context.newPage();

    // 监听页面日志
    this.page.on('console', msg => {
      console.log(`🌐 浏览器日志 [${msg.type()}]: ${msg.text()}`);
    });
  }

  // 访问公式配置页面
  async navigateToFormulaPage() {
    console.log('� 访问公式配置页面...');
    try {
      await this.page.goto(`${this.baseUrl}/formula-config`);
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(3000);

      // 检查页面是否正确加载
      const pageTitle = await this.page.locator('text=公式配置网格').isVisible();
      if (pageTitle) {
        console.log('✅ 公式配置页面加载成功');
        return true;
      } else {
        console.log('❌ 公式配置页面加载失败');
        return false;
      }
    } catch (error) {
      console.log(`❌ 页面访问异常: ${error.message}`);
      return false;
    }
  }



  // 在浏览器中测试公式计算
  async testFormulaCalculationInBrowser() {
    console.log('🧮 开始在浏览器中测试公式计算...\n');

    // 测试数据
    const testData = {
      data1: 100,
      data2: 2,
      data3: 50,
      data4: 1,
      data5: 25,
      data6: 80,
      data7: 3
    };

    console.log('📝 测试数据:', testData);

    // 步骤1: 输入测试数据
    await this.inputDataToBrowser(testData);

    // 步骤2: 等待计算完成
    await this.page.waitForTimeout(3000);

    // 步骤3: 获取页面中的公式表达式
    await this.getFormulasFromPage();

    // 步骤4: 获取页面计算结果
    await this.getCalculationResultsFromPage();

    // 步骤5: 验证计算准确性
    await this.verifyCalculationAccuracy(testData);

    // 测试负数情况
    console.log('\n➖ 测试负数情况...');
    const negativeTestData = {
      data1: -100,
      data2: -2,
      data3: -50,
      data4: -1,
      data5: -25,
      data6: -80,
      data7: -3
    };

    console.log('� 负数测试数据:', negativeTestData);
    await this.inputDataToBrowser(negativeTestData);
    await this.page.waitForTimeout(3000);
    await this.getCalculationResultsFromPage();
    await this.verifyCalculationAccuracy(negativeTestData);
  }

  // 在浏览器中输入测试数据
  async inputDataToBrowser(data) {
    console.log('📝 在浏览器中输入测试数据...');

    try {
      // 确保有客户行存在
      const addButton = this.page.locator('button:has-text("添加客户")').first();
      if (await addButton.isVisible()) {
        console.log('🔘 点击添加客户按钮');
        await addButton.click();
        await this.page.waitForTimeout(2000);
      }

      // 输入数据到各个字段
      for (let i = 1; i <= 7; i++) {
        const fieldName = `data${i}`;
        const value = data[fieldName];

        if (value !== undefined) {
          console.log(`📊 输入 ${fieldName}: ${value}`);

          // 尝试多种选择器策略
          const selectors = [
            `table input[placeholder*="数据${i}"]`,
            `table input[title*="数据${i}"]`,
            `table tbody tr:first-child td:nth-child(${i + 2}) input`,
            `input[data-testid="data${i}"]`,
            `table .ant-input:nth-of-type(${i})`
          ];

          let success = false;
          for (const selector of selectors) {
            try {
              const input = this.page.locator(selector).first();
              if (await input.isVisible({ timeout: 2000 })) {
                await input.clear();
                await input.fill(value.toString());
                await this.page.waitForTimeout(500);
                console.log(`  ✅ 成功输入 ${fieldName}: ${value}`);
                success = true;
                break;
              }
            } catch (e) {
              // 继续尝试下一个选择器
            }
          }

          if (!success) {
            console.log(`  ❌ 未找到 ${fieldName} 的输入框`);
          }
        }
      }

      // 触发计算
      console.log('⚡ 触发计算...');
      await this.page.keyboard.press('Tab');
      await this.page.waitForTimeout(2000);

    } catch (error) {
      console.log(`❌ 输入数据异常: ${error.message}`);
    }
  }

  // 从页面获取公式表达式
  async getFormulasFromPage() {
    console.log('📋 从页面获取公式表达式...');

    try {
      const formulas = await this.page.evaluate(() => {
        const formulaMap = {};

        // 尝试从全局变量获取
        if (window.formulaExpressions) {
          Object.assign(formulaMap, window.formulaExpressions);
        }

        // 尝试从localStorage获取
        try {
          const saved = localStorage.getItem('formula_expressions');
          if (saved) {
            Object.assign(formulaMap, JSON.parse(saved));
          }
        } catch (e) {}

        return formulaMap;
      });

      console.log('📐 获取到的公式:', formulas);
      this.formulaResults.set('expressions', formulas);
      return formulas;
    } catch (error) {
      console.log(`❌ 获取公式异常: ${error.message}`);
      return {};
    }
  }

  // 从页面获取计算结果
  async getCalculationResultsFromPage() {
    console.log('🔍 从页面获取计算结果...');

    try {
      const results = await this.page.evaluate(() => {
        const resultData = {};

        // 查找所有包含结果的元素
        const strongElements = document.querySelectorAll('strong');
        strongElements.forEach((element, index) => {
          const text = element.textContent.trim();
          if (!isNaN(text) && text !== '') {
            resultData[`strong_${index}`] = parseFloat(text);
          }
        });

        // 查找表格中的结果列
        const tables = document.querySelectorAll('table');
        tables.forEach((table, tableIndex) => {
          const rows = table.querySelectorAll('tbody tr');
          rows.forEach((row, rowIndex) => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, cellIndex) => {
              const text = cell.textContent.trim();
              if (cell.querySelector('strong') && !isNaN(text) && text !== '') {
                resultData[`table_${tableIndex}_row_${rowIndex}_col_${cellIndex}`] = parseFloat(text);
              }
            });
          });
        });

        return resultData;
      });

      console.log('📊 页面计算结果:', results);
      this.formulaResults.set('pageResults', results);
      return results;
    } catch (error) {
      console.log(`❌ 获取结果异常: ${error.message}`);
      return {};
    }
  }

  // 验证计算准确性
  async verifyCalculationAccuracy(inputData) {
    console.log('🎯 验证计算准确性...');

    const expressions = this.formulaResults.get('expressions') || {};
    const pageResults = this.formulaResults.get('pageResults') || {};

    console.log('\n📐 公式验证详情:');
    console.log('='.repeat(60));

    // 主要公式验证
    const mainFormulas = [
      { name: '公式1.1', expression: '(data1+data2+data4)+data3' },
      { name: '公式1.2', expression: '(data1+data2+data4)+data3' },
      { name: '公式2.1', expression: 'data1*data2' },
      { name: '公式2.2', expression: 'data2*data4' },
      { name: '公式3.1', expression: 'data1' },
      { name: '公式7.1', expression: 'data2 / (1 + data4) - data1' },
      { name: '公式7.2', expression: '(data2 - data1) / data1' },
      { name: '公式7.3', expression: 'data2 * (1 - data3)' }
    ];

    for (const formula of mainFormulas) {
      const expected = this.calculateExpected(formula.expression, inputData);

      console.log(`\n� ${formula.name}:`);
      console.log(`   表达式: ${formula.expression}`);
      console.log(`   输入数据: ${JSON.stringify(inputData)}`);
      console.log(`   预期结果: ${expected}`);

      // 从页面表达式中获取实际表达式
      const actualExpression = expressions[formula.name];
      if (actualExpression) {
        console.log(`   页面表达式: ${actualExpression}`);
        const actualExpected = this.calculateExpected(actualExpression, inputData);
        console.log(`   基于页面表达式的预期: ${actualExpected}`);

        if (actualExpected === expected) {
          console.log(`   ✅ 表达式匹配正确`);
        } else {
          console.log(`   ❌ 表达式不匹配！`);
        }
      } else {
        console.log(`   ⚠️ 未找到页面表达式`);
      }
    }

    console.log('\n📊 页面显示的所有结果:');
    Object.entries(pageResults).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });

    console.log('='.repeat(60));
  }

  // 测试公式编辑功能
  async testFormulaEditing() {
    console.log('✏️ 测试公式编辑功能...');
    try {
      // 点击第一个公式卡片
      const firstFormulaCard = this.page.locator('.formula-card').first();
      await firstFormulaCard.click();
      await this.page.waitForTimeout(1000);

      // 检查模态框是否打开
      const modalVisible = await this.page.locator('.ant-modal').isVisible();
      this.logResult('公式编辑模态框', modalVisible, modalVisible ? '模态框打开成功' : '模态框未打开');

      if (modalVisible) {
        // 获取当前公式表达式
        const currentExpression = await this.page.locator('textarea').inputValue();
        console.log(`当前公式表达式: ${currentExpression}`);

        // 关闭模态框
        await this.page.click('button:has-text("取消")');
        await this.page.waitForTimeout(500);

        this.logResult('公式编辑功能', true, `当前表达式: ${currentExpression}`);
      }

      return modalVisible;
    } catch (error) {
      this.logResult('公式编辑功能', false, `公式编辑测试异常: ${error.message}`);
      return false;
    }
  }



  // 执行浏览器测试
  async runBrowserTest() {
    console.log('🎯 开始执行浏览器公式计算测试...\n');

    try {
      // 步骤1: 启动浏览器
      await this.setup();

      // 步骤2: 访问公式配置页面
      await this.navigateToFormulaPage();

      // 步骤3: 在浏览器中测试公式计算
      await this.testFormulaCalculationInBrowser();

      // 步骤4: 测试公式编辑功能
      await this.testFormulaEditing();

      console.log('\n🎉 测试完成！');
      console.log('🔍 浏览器将保持打开状态，请手动检查页面结果');
      console.log('📋 查看控制台输出了解详细的测试结果');

    } catch (error) {
      console.log(`❌ 测试执行异常: ${error.message}`);
    } finally {
      // 保持浏览器打开以便手动检查
      console.log('\n⏳ 浏览器保持打开状态...');
      console.log('按 Ctrl+C 退出测试');

      // 等待用户手动关闭
      await new Promise(() => {});
    }
  }


}

// 执行测试
async function main() {
  const tester = new BrowserFormulaCalculationTester();
  await tester.runBrowserTest();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = BrowserFormulaCalculationTester;
