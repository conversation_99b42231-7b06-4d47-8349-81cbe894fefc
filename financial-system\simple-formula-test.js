// 简化的公式测试脚本
const { chromium } = require('playwright');

async function testFormulas() {
  console.log('🚀 启动简化公式测试...');
  
  let browser = null;
  try {
    // 启动浏览器
    browser = await chromium.launch({ 
      headless: false,
      slowMo: 2000
    });
    
    const context = await browser.newContext({
      viewport: { width: 1920, height: 1080 }
    });
    const page = await context.newPage();
    
    // 访问页面
    console.log('📊 访问公式配置页面...');
    await page.goto('http://localhost:3000/formula-config');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // 检查页面加载
    const pageLoaded = await page.locator('text=公式配置网格').isVisible();
    console.log(`页面加载状态: ${pageLoaded ? '✅ 成功' : '❌ 失败'}`);
    
    if (!pageLoaded) {
      console.log('❌ 页面加载失败，测试终止');
      return;
    }
    
    // 测试数据集
    const testCases = [
      {
        name: '正数测试',
        data: { data1: 100, data2: 2, data3: 50, data4: 1, data5: 25, data6: 80, data7: 3 },
        expectedFormula1_1: 153 // (100+2+1)+50 = 153
      },
      {
        name: '负数测试', 
        data: { data1: -100, data2: -2, data3: -50, data4: -1, data5: -25, data6: -80, data7: -3 },
        expectedFormula1_1: -153 // (-100+-2+-1)+(-50) = -153
      },
      {
        name: '混合测试',
        data: { data1: 100, data2: -2, data3: 50, data4: -1, data5: 25, data6: -80, data7: 3 },
        expectedFormula1_1: 147 // (100+-2+-1)+50 = 147
      }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n🧪 执行${testCase.name}...`);
      console.log(`📊 测试数据: ${JSON.stringify(testCase.data)}`);
      
      // 清空并输入数据
      await inputTestData(page, testCase.data);
      
      // 等待计算完成
      await page.waitForTimeout(3000);
      
      // 获取计算结果
      const results = await getCalculationResults(page);
      console.log(`📈 页面结果: ${JSON.stringify(results, null, 2)}`);
      
      // 验证公式1.1的结果
      let found = false;
      for (const [key, value] of Object.entries(results)) {
        if (Math.abs(value - testCase.expectedFormula1_1) < 0.01) {
          console.log(`✅ 公式1.1计算正确: 预期=${testCase.expectedFormula1_1}, 实际=${value} (在${key})`);
          found = true;
          break;
        }
      }
      
      if (!found) {
        console.log(`❌ 公式1.1计算错误: 预期=${testCase.expectedFormula1_1}, 未找到匹配结果`);
      }
    }
    
    console.log('\n🎉 测试完成！浏览器保持打开状态...');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  } finally {
    // 不关闭浏览器，让用户手动检查
  }
}

// 输入测试数据
async function inputTestData(page, data) {
  try {
    // 确保有客户行
    const addButton = page.locator('button:has-text("添加客户")').first();
    if (await addButton.isVisible()) {
      await addButton.click();
      await page.waitForTimeout(2000);
    }
    
    // 输入数据
    for (let i = 1; i <= 7; i++) {
      const fieldName = `data${i}`;
      const value = data[fieldName];
      
      if (value !== undefined) {
        // 尝试多种选择器
        const selectors = [
          `table input[placeholder*="数据${i}"]`,
          `table tbody tr:first-child td:nth-child(${i + 2}) input`,
          `table .ant-input:nth-of-type(${i})`
        ];
        
        let success = false;
        for (const selector of selectors) {
          try {
            const input = page.locator(selector).first();
            if (await input.isVisible({ timeout: 1000 })) {
              await input.clear();
              await input.fill(value.toString());
              await page.waitForTimeout(300);
              console.log(`  ✅ 输入 ${fieldName}: ${value}`);
              success = true;
              break;
            }
          } catch (e) {
            // 继续尝试
          }
        }
        
        if (!success) {
          console.log(`  ❌ 未找到 ${fieldName} 输入框`);
        }
      }
    }
    
    // 触发计算
    await page.keyboard.press('Tab');
    
  } catch (error) {
    console.log(`❌ 输入数据异常: ${error.message}`);
  }
}

// 获取计算结果
async function getCalculationResults(page) {
  try {
    const results = await page.evaluate(() => {
      const resultData = {};
      
      // 查找所有strong元素中的数字
      const strongElements = document.querySelectorAll('strong');
      strongElements.forEach((element, index) => {
        const text = element.textContent.trim();
        if (!isNaN(text) && text !== '' && text !== '0') {
          resultData[`strong_${index}`] = parseFloat(text);
        }
      });
      
      return resultData;
    });
    
    return results;
  } catch (error) {
    console.log(`❌ 获取结果异常: ${error.message}`);
    return {};
  }
}

// 执行测试
testFormulas().catch(console.error);
