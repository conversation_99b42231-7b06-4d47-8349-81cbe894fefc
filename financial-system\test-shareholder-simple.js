// 简化版股东公式映射测试
const { chromium } = require('playwright');

async function testShareholderMapping() {
  console.log('🎯 股东公式映射功能测试');
  console.log('🌐 URL: http://dlsykgzq.w1.luyouxia.net/formula-config/\n');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 2000
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 访问页面
    console.log('📊 步骤1: 访问页面...');
    await page.goto('http://dlsykgzq.w1.luyouxia.net/formula-config/', {
      timeout: 0
    });
    
    await page.waitForTimeout(5000);
    console.log('✅ 页面加载完成');
    
    // 2. 测试股东数量和公式映射
    const testCases = [
      { count: 1, expectedFormulas: ['1.31', '1.32'], description: '第1个股东应使用公式1.31&1.32' },
      { count: 2, expectedFormulas: ['1.41', '1.42'], description: '第2个股东应使用公式1.41&1.42' },
      { count: 3, expectedFormulas: ['1.51', '1.52'], description: '第3个股东应使用公式1.51&1.52' }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n🧪 测试${testCase.count}个股东的情况`);
      console.log(`📋 预期: ${testCase.description}`);
      
      // 添加股东
      for (let i = 0; i < testCase.count; i++) {
        const addButton = page.locator('button:has-text("添加客户"), button:has-text("添加")').first();
        if (await addButton.isVisible()) {
          await addButton.click();
          await page.waitForTimeout(2000);
          console.log(`   ✅ 添加第${i + 1}个股东成功`);
        }
      }
      
      // 检查页面上的公式信息
      const pageContent = await page.textContent('body');
      console.log(`   🔍 检查公式映射...`);
      
      let foundFormulas = [];
      testCase.expectedFormulas.forEach(formula => {
        if (pageContent.includes(formula)) {
          foundFormulas.push(formula);
          console.log(`   ✅ 找到公式 ${formula}`);
        } else {
          console.log(`   ❌ 未找到公式 ${formula}`);
        }
      });
      
      const mappingCorrect = foundFormulas.length === testCase.expectedFormulas.length;
      console.log(`   📊 公式映射结果: ${mappingCorrect ? '✅正确' : '❌不正确'} (${foundFormulas.length}/${testCase.expectedFormulas.length})`);
    }
    
    // 3. 测试负数输入和计算
    console.log('\n➖ 测试负数处理...');
    
    const negativeTestCases = [
      {
        name: '基础负数测试',
        data: [-100, -2, -50, -1],
        expectedResults: {
          'formula1_1': -153, // (-100+-2+-1)+(-50) = -153
          'formula2_1': 200,  // (-100)*(-2) = 200
        }
      },
      {
        name: '正负混合测试',
        data: [100, -2, 50, -1],
        expectedResults: {
          'formula1_1': 147, // (100+-2+-1)+50 = 147
          'formula2_1': -200, // 100*(-2) = -200
        }
      }
    ];
    
    for (const testCase of negativeTestCases) {
      console.log(`\n🧪 ${testCase.name}`);
      console.log(`📊 测试数据: data1=${testCase.data[0]}, data2=${testCase.data[1]}, data3=${testCase.data[2]}, data4=${testCase.data[3]}`);
      
      // 输入测试数据
      await inputTestData(page, testCase.data);
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(3000);
      
      // 获取计算结果
      const results = await getCalculationResults(page);
      console.log(`   📈 找到 ${results.length} 个计算结果`);
      
      // 验证负数处理
      const hasNegativeInputs = testCase.data.some(v => v < 0);
      const hasNegativeResults = results.some(r => r < 0);
      
      console.log(`   🔍 负数处理分析:`);
      console.log(`      输入包含负数: ${hasNegativeInputs ? '✅是' : '❌否'}`);
      console.log(`      结果包含负数: ${hasNegativeResults ? '✅是' : '❌否'}`);
      
      if (hasNegativeInputs && hasNegativeResults) {
        console.log(`      ✅ 负数处理正常`);
      } else if (hasNegativeInputs && !hasNegativeResults) {
        console.log(`      ⚠️ 警告: 输入负数但结果无负数，可能存在问题`);
      }
      
      // 验证具体计算结果
      for (const [formulaName, expected] of Object.entries(testCase.expectedResults)) {
        const found = results.find(r => Math.abs(r - expected) < 0.01);
        if (found) {
          console.log(`      ✅ ${formulaName}: 预期${expected}, 找到${found}`);
        } else {
          const closest = results.reduce((prev, curr) => 
            Math.abs(curr - expected) < Math.abs(prev - expected) ? curr : prev
          );
          console.log(`      ❌ ${formulaName}: 预期${expected}, 最接近${closest}`);
        }
      }
    }
    
    // 4. 测试复杂公式
    console.log('\n🧮 测试复杂公式计算...');
    
    const complexTestCases = [
      {
        name: '多层嵌套运算',
        data: [10, 5, 2, 3],
        formula: '((data1+data2)*data3-data4)',
        expected: ((10+5)*2-3) // = 27
      },
      {
        name: '混合运算',
        data: [20, 4, 3, 2],
        formula: 'data1*data2+data3-data4',
        expected: (20*4+3-2) // = 81
      }
    ];
    
    for (const testCase of complexTestCases) {
      console.log(`\n🧪 ${testCase.name}`);
      console.log(`📊 测试数据: ${testCase.data.join(', ')}`);
      console.log(`📐 公式: ${testCase.formula} = ${testCase.expected}`);
      
      // 输入测试数据
      await inputTestData(page, testCase.data);
      
      // 触发计算
      await page.keyboard.press('Tab');
      await page.waitForTimeout(3000);
      
      // 获取计算结果
      const results = await getCalculationResults(page);
      
      // 验证结果
      const found = results.find(r => Math.abs(r - testCase.expected) < 0.01);
      if (found) {
        console.log(`   ✅ 复杂公式计算正确: 预期${testCase.expected}, 找到${found}`);
      } else {
        console.log(`   ❌ 复杂公式计算不匹配: 预期${testCase.expected}`);
        console.log(`   📊 所有结果: ${results.join(', ')}`);
      }
    }
    
    // 5. 生成测试总结
    console.log('\n' + '='.repeat(60));
    console.log('🎯 股东公式映射测试总结');
    console.log('='.repeat(60));
    console.log('✅ 页面访问正常');
    console.log('✅ 股东添加功能正常');
    console.log('✅ 负数输入处理测试完成');
    console.log('✅ 复杂公式计算测试完成');
    console.log('='.repeat(60));
    
    console.log('\n🎉 测试完成！');
    console.log('🔍 浏览器保持打开状态，请手动验证：');
    console.log('   1. 股东公式映射规则是否正确');
    console.log('   2. 负数计算结果是否准确');
    console.log('   3. 复杂公式运算是否正确');
    console.log('按 Ctrl+C 退出测试');
    
    // 保持浏览器打开
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
  }
}

// 输入测试数据
async function inputTestData(page, data) {
  console.log('   📝 输入测试数据...');
  
  try {
    // 查找输入框
    const inputs = await page.$$('table input, input[type="text"], input[type="number"]');
    
    for (let i = 0; i < Math.min(data.length, inputs.length); i++) {
      if (data[i] !== undefined) {
        try {
          await inputs[i].click({ clickCount: 3 });
          await inputs[i].fill(data[i].toString());
          await page.waitForTimeout(300);
          
          const actualValue = await inputs[i].inputValue();
          console.log(`      data${i+1}: ${data[i]} ${actualValue === data[i].toString() ? '✅' : '❌'}`);
        } catch (e) {
          console.log(`      data${i+1}: ${data[i]} ❌ (输入失败)`);
        }
      }
    }
  } catch (error) {
    console.log(`   ❌ 输入数据异常: ${error.message}`);
  }
}

// 获取计算结果
async function getCalculationResults(page) {
  try {
    const results = await page.evaluate(() => {
      const resultData = [];
      const strongElements = document.querySelectorAll('strong');
      
      strongElements.forEach(element => {
        const text = element.textContent.trim();
        const num = parseFloat(text);
        if (!isNaN(num) && isFinite(num)) {
          resultData.push(num);
        }
      });
      
      return resultData;
    });
    
    return results;
  } catch (error) {
    console.log(`❌ 获取结果异常: ${error.message}`);
    return [];
  }
}

// 执行测试
testShareholderMapping().catch(console.error);
