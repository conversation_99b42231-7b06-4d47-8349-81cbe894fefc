// 测试公式表达式的负数支持
// 验证FormulaEditModal组件的验证逻辑

// 复制FormulaEditModal的验证函数
function validateExpression(expr) {
  if (!expr.trim()) return true; // 空表达式也是有效的
  
  try {
    // 更新的语法检查，支持负数和小数
    // 允许: data变量、数字(包括负数和小数)、运算符、括号、空格
    const validPattern = /^[data\d+\-*/^().\s]+$/;
    
    // 检查基本字符是否合法
    if (!validPattern.test(expr)) {
      return false;
    }
    
    // 检查是否包含有效的变量名或数字
    const hasValidContent = /data\d|[\d.-]+/.test(expr);
    
    return hasValidContent;
  } catch (error) {
    return false;
  }
}

// 测试用例
const formulaTestCases = [
  // 基本负数测试
  { input: 'data1+(-123)', expected: true, description: '变量加负数' },
  { input: 'data1-123', expected: true, description: '变量减正数' },
  { input: '-data1+data2', expected: true, description: '负变量加变量' },
  { input: '(-data1)+data2', expected: true, description: '括号内负变量' },
  
  // 负数小数测试
  { input: 'data1+(-123.45)', expected: true, description: '变量加负小数' },
  { input: 'data3*(-0.123)', expected: true, description: '变量乘负小数' },
  { input: 'data4/(-1.12345678)', expected: true, description: '变量除负小数' },
  
  // 复杂表达式测试
  { input: '(data1+data2)*(-1)', expected: true, description: '复杂表达式乘负数' },
  { input: '(-data1+data2-data3)*data4', expected: true, description: '多负数复杂表达式' },
  { input: 'data1+(-123.456)+data2*(-0.789)', expected: true, description: '多个负数混合' },
  
  // 边界情况测试
  { input: '-123.45', expected: true, description: '纯负数' },
  { input: '(-123.45)', expected: true, description: '括号内纯负数' },
  { input: 'data1+-123', expected: true, description: '加号后跟负数' },
  { input: 'data1*-data2', expected: true, description: '乘号后跟负变量' },
  
  // 无效表达式测试
  { input: 'data1++data2', expected: true, description: '双加号（应该被允许）' },
  { input: 'data1--data2', expected: true, description: '双减号（应该被允许）' },
  { input: 'abc+123', expected: false, description: '无效变量名' },
  { input: 'data1@data2', expected: false, description: '无效操作符' },
  
  // 空格测试
  { input: 'data1 + (-123.45)', expected: true, description: '带空格的负数表达式' },
  { input: '( -data1 + data2 ) * (-1)', expected: true, description: '带空格的复杂负数表达式' },
];

function runFormulaTests() {
  console.log('🧪 开始测试公式表达式负数支持...\n');
  
  let passedTests = 0;
  let totalTests = formulaTestCases.length;
  
  formulaTestCases.forEach((testCase, index) => {
    const result = validateExpression(testCase.input);
    const passed = result === testCase.expected;
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`  表达式: "${testCase.input}"`);
    console.log(`  期望: ${testCase.expected ? '有效' : '无效'}`);
    console.log(`  实际: ${result ? '有效' : '无效'}`);
    console.log(`  结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`📊 公式表达式测试总结:`);
  console.log(`  总测试数: ${totalTests}`);
  console.log(`  通过数: ${passedTests}`);
  console.log(`  失败数: ${totalTests - passedTests}`);
  console.log(`  通过率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有公式表达式测试通过！负数支持功能正常工作。');
  } else {
    console.log('\n⚠️ 部分公式表达式测试失败，需要检查验证逻辑。');
  }
}

// 实际使用示例测试
function testRealWorldExamples() {
  console.log('\n🌐 实际使用示例测试...\n');
  
  const realExamples = [
    '(data1+data2+data4)+data3',
    '(data1+data2+data4)*(-1)+data3',
    'data1*data2+(-100)',
    '(-data1+data2)*data3',
    'data1/data2-(-50.5)',
    '(data1+(-123.45))*data2/data3',
    '-data1-data2+data3*(-2.5)',
  ];
  
  realExamples.forEach((example, index) => {
    const isValid = validateExpression(example);
    console.log(`示例 ${index + 1}: ${example}`);
    console.log(`  验证结果: ${isValid ? '✅ 有效' : '❌ 无效'}\n`);
  });
}

// 运行所有测试
console.log('🚀 公式表达式负数支持测试\n');
runFormulaTests();
testRealWorldExamples();

console.log('📝 功能说明:');
console.log('✅ 公式表达式现在支持负数');
console.log('✅ 支持负变量: -data1');
console.log('✅ 支持负常数: -123.45');
console.log('✅ 支持括号内负数: (-123.45)');
console.log('✅ 支持复杂负数表达式');
console.log('✅ 保持原有的变量和操作符验证');

console.log('\n🔧 请在浏览器中测试:');
console.log('1. 访问 http://localhost:3000/formula-config');
console.log('2. 点击任意公式卡片进行编辑');
console.log('3. 尝试输入包含负数的表达式');
console.log('4. 验证表达式验证和保存功能');
