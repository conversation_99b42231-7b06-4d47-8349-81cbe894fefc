'use client';

import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Space, Typography, Tag, message } from 'antd';
import { EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Text, Title } = Typography;

const FormulaEditModal = ({
  open,
  formula,
  expression,
  onSave,
  onCancel
}) => {
  const [editedExpression, setEditedExpression] = useState(expression || '');
  const [isValid, setIsValid] = useState(true);

  useEffect(() => {
    setEditedExpression(expression || '');
  }, [expression]);

  // 可用变量列表
  const availableVariables = [
    'data1', 'data2', 'data3', 'data4', 'data5', 'data6', 'data7'
  ];

  // 可用操作符
  const availableOperators = [
    '+', '-', '*', '/', '^', '(', ')'
  ];

  // 验证公式表达式 - 支持负数
  const validateExpression = (expr) => {
    if (!expr.trim()) return true; // 空表达式也是有效的

    try {
      // 更新的语法检查，支持负数和小数
      // 允许: data变量、数字(包括负数和小数)、运算符、括号、空格
      const validPattern = /^[data\d+\-*/^().\s]+$/;

      // 检查基本字符是否合法
      if (!validPattern.test(expr)) {
        return false;
      }

      // 检查是否包含有效的变量名或数字
      const hasValidContent = /data\d|[\d.-]+/.test(expr);

      return hasValidContent;
    } catch (error) {
      return false;
    }
  };

  // 处理表达式变化
  const handleExpressionChange = (e) => {
    const newExpression = e.target.value;
    setEditedExpression(newExpression);
    setIsValid(validateExpression(newExpression));
  };

  // 插入变量或操作符
  const insertText = (text) => {
    setEditedExpression(prev => prev + text);
    setIsValid(validateExpression(editedExpression + text));
  };

  // 保存公式
  const handleSave = () => {
    if (!isValid) {
      message.error('公式表达式格式不正确');
      return;
    }
    
    onSave(formula, editedExpression);
    message.success(`公式 ${formula} 保存成功`);
  };

  return (
    <Modal
      title={
        <Space>
          <EditOutlined />
          <span>编辑公式 - {formula}</span>
        </Space>
      }
      open={open}
      onCancel={onCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={handleSave}
          disabled={!isValid}
          icon={<CheckOutlined />}
        >
          保存
        </Button>
      ]}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 当前公式信息 */}
        <div>
          <Text strong>公式名称：</Text>
          <Tag color="blue">{formula}</Tag>
        </div>

        {/* 公式表达式编辑器 */}
        <div>
          <Text strong>公式表达式：</Text>
          <TextArea
            value={editedExpression}
            onChange={handleExpressionChange}
            placeholder="输入公式表达式，如：(data1+data2+data4)+data3"
            rows={3}
            style={{
              marginTop: '8px',
              fontFamily: 'Courier New, monospace',
              border: isValid ? '1px solid #d9d9d9' : '1px solid #ff4d4f'
            }}
          />
          {!isValid && (
            <Text type="danger" style={{ fontSize: '12px' }}>
              公式表达式格式不正确
            </Text>
          )}
        </div>

        {/* 可用变量 */}
        <div>
          <Text strong>可用变量：</Text>
          <div style={{ marginTop: '8px' }}>
            <Space wrap>
              {availableVariables.map(variable => (
                <Tag 
                  key={variable}
                  color="green"
                  style={{ cursor: 'pointer' }}
                  onClick={() => insertText(variable)}
                >
                  {variable}
                </Tag>
              ))}
            </Space>
          </div>
        </div>

        {/* 可用操作符 */}
        <div>
          <Text strong>操作符：</Text>
          <div style={{ marginTop: '8px' }}>
            <Space wrap>
              {availableOperators.map(operator => (
                <Tag 
                  key={operator}
                  color="orange"
                  style={{ cursor: 'pointer' }}
                  onClick={() => insertText(operator)}
                >
                  {operator}
                </Tag>
              ))}
            </Space>
          </div>
        </div>

        {/* 示例 */}
        <div style={{
          background: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '6px',
          padding: '12px'
        }}>
          <Text strong style={{ color: '#52c41a' }}>示例：</Text>
          <br />
          <Text code>(data1+data2+data4)+data3</Text> - 加法运算
          <br />
          <Text code>data1*data2</Text> - 乘法运算
          <br />
          <Text code>data1/data2</Text> - 除法运算
          <br />
          <Text code>data1^2</Text> - 幂运算
          <br />
          <Text code>data1+(-123.45)</Text> - 负数运算
          <br />
          <Text code>-data1+data2</Text> - 负变量运算
        </div>
      </Space>
    </Modal>
  );
};

export default FormulaEditModal;
