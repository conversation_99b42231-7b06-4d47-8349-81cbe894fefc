// 数据库连接配置 - JavaScript版本
// 避免TypeScript版本冲突

import { PrismaClient } from '@prisma/client';

// 全局Prisma客户端实例
const globalForPrisma = globalThis;

export const prisma = globalForPrisma.prisma || new PrismaClient();

if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// 数据库连接测试
export async function testConnection() {
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// 优雅关闭数据库连接
export async function closeConnection() {
  await prisma.$disconnect();
  console.log('🔌 数据库连接已关闭');
}
