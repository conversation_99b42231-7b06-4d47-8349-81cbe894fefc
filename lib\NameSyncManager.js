'use client';

// NameSyncManager - 名称同步管理器
// 负责管理跨模块的字段名称同步

import { MODULE_CONFIGS } from '../config/moduleConfigs';

export class NameSyncManager {
  constructor() {
    this.syncHistory = [];
    this.syncErrors = [];
    this.isSync = false;
  }
  
  /**
   * 同步字段名称到所有相关模块
   * @param {string} fieldId - 字段ID (如: data1_1, formula1_1)
   * @param {string} newName - 新名称
   * @param {string} sourceModuleId - 源模块ID
   * @returns {Promise<Object>} 同步结果
   */
  async syncFieldName(fieldId, newName, sourceModuleId) {
    console.log(`开始同步字段 ${fieldId} 的名称: ${newName}`);
    
    this.isSync = true;
    const syncStartTime = Date.now();
    
    try {
      // 1. 获取受影响的模块列表
      const affectedModules = this.getAffectedModules(fieldId, sourceModuleId);
      console.log('受影响的模块:', affectedModules);
      
      // 2. 创建同步任务
      const syncTasks = affectedModules.map(moduleId => 
        this.updateModuleFieldName(moduleId, fieldId, newName)
      );
      
      // 3. 并行执行同步任务
      const results = await Promise.allSettled(syncTasks);
      
      // 4. 处理同步结果
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failedCount = results.filter(r => r.status === 'rejected').length;
      
      // 5. 记录同步历史
      const syncRecord = {
        fieldId,
        newName,
        sourceModuleId,
        affectedModules,
        successCount,
        failedCount,
        duration: Date.now() - syncStartTime,
        timestamp: new Date(),
        status: failedCount === 0 ? 'success' : 'partial'
      };
      
      this.syncHistory.push(syncRecord);
      
      // 6. 处理失败的同步
      if (failedCount > 0) {
        const errors = results
          .filter(r => r.status === 'rejected')
          .map(r => r.reason);
        
        this.syncErrors.push({
          fieldId,
          errors,
          timestamp: new Date()
        });
        
        console.error('同步过程中出现错误:', errors);
      }
      
      console.log(`同步完成: 成功 ${successCount}, 失败 ${failedCount}`);
      
      return {
        success: failedCount === 0,
        successCount,
        failedCount,
        affectedModules,
        duration: syncRecord.duration
      };
      
    } catch (error) {
      console.error('同步失败:', error);
      
      this.syncErrors.push({
        fieldId,
        error: error.message,
        timestamp: new Date()
      });
      
      return {
        success: false,
        error: error.message
      };
    } finally {
      this.isSync = false;
    }
  }
  
  /**
   * 获取受影响的模块列表
   * @param {string} fieldId - 字段ID
   * @param {string} sourceModuleId - 源模块ID
   * @returns {Array<string>} 模块ID列表
   */
  getAffectedModules(fieldId, sourceModuleId) {
    const affectedModules = new Set();
    
    // 1. 从源模块配置中获取同步目标
    const sourceConfig = MODULE_CONFIGS[sourceModuleId];
    if (sourceConfig?.syncConfig?.syncTargets?.[fieldId]) {
      sourceConfig.syncConfig.syncTargets[fieldId].forEach(target => {
        affectedModules.add(target);
      });
    }
    
    // 2. 根据字段类型确定默认同步目标
    const fieldType = this.getFieldType(fieldId);
    const fieldIndex = this.getFieldIndex(fieldId);
    
    if (fieldType === 'data') {
      // 数据字段需要同步到所有相关模块
      Object.keys(MODULE_CONFIGS).forEach(moduleId => {
        if (moduleId !== sourceModuleId) {
          // 检查目标模块是否有相同索引的字段
          const targetConfig = MODULE_CONFIGS[moduleId];
          if (targetConfig?.fields?.[fieldId]) {
            affectedModules.add(moduleId);
          }
        }
      });
      
      // 添加报表模块
      affectedModules.add('weekly_report');
      affectedModules.add('monthly_report');
      affectedModules.add('summary_table');
    }
    
    if (fieldType === 'formula') {
      // 公式字段主要同步到汇总表
      affectedModules.add('summary_table');
    }
    
    return Array.from(affectedModules);
  }
  
  /**
   * 更新目标模块的字段名称
   * @param {string} moduleId - 目标模块ID
   * @param {string} fieldId - 字段ID
   * @param {string} newName - 新名称
   * @returns {Promise<boolean>} 更新结果
   */
  async updateModuleFieldName(moduleId, fieldId, newName) {
    try {
      console.log(`更新模块 ${moduleId} 的字段 ${fieldId} 名称为: ${newName}`);
      
      // 1. 特殊模块处理
      if (moduleId === 'summary_table') {
        return await this.updateSummaryTable(fieldId, newName);
      }
      
      if (moduleId === 'weekly_report' || moduleId === 'monthly_report') {
        return await this.updateReportTable(moduleId, fieldId, newName);
      }
      
      // 2. 普通模块处理
      const moduleConfig = MODULE_CONFIGS[moduleId];
      if (!moduleConfig) {
        throw new Error(`模块 ${moduleId} 不存在`);
      }
      
      // 3. 更新模块配置中的自定义名称
      if (!moduleConfig.customNames) {
        moduleConfig.customNames = { formulas: {}, dataFields: {} };
      }
      
      const fieldType = this.getFieldType(fieldId);
      const targetSection = fieldType === 'formula' ? 'formulas' : 'dataFields';
      
      if (!moduleConfig.customNames[targetSection]) {
        moduleConfig.customNames[targetSection] = {};
      }
      
      // 4. 设置自定义名称
      moduleConfig.customNames[targetSection][fieldId] = {
        defaultName: moduleConfig.fields?.[fieldId]?.title || fieldId,
        customName: newName,
        lastModified: new Date().toISOString(),
        syncedFrom: 'auto_sync'
      };
      
      console.log(`成功更新模块 ${moduleId} 的字段 ${fieldId}`);
      return true;
      
    } catch (error) {
      console.error(`更新模块 ${moduleId} 失败:`, error);
      throw error;
    }
  }
  
  /**
   * 更新汇总表
   * @param {string} fieldId - 字段ID
   * @param {string} newName - 新名称
   * @returns {Promise<boolean>}
   */
  async updateSummaryTable(fieldId, newName) {
    try {
      // 这里应该调用API更新汇总表的列名
      // 暂时使用localStorage模拟
      const summaryConfig = JSON.parse(localStorage.getItem('summaryTableConfig') || '{}');
      
      if (!summaryConfig.customFieldNames) {
        summaryConfig.customFieldNames = {};
      }
      
      summaryConfig.customFieldNames[fieldId] = newName;
      localStorage.setItem('summaryTableConfig', JSON.stringify(summaryConfig));
      
      console.log(`汇总表字段 ${fieldId} 更新为: ${newName}`);
      return true;
    } catch (error) {
      console.error('更新汇总表失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新报表
   * @param {string} reportType - 报表类型
   * @param {string} fieldId - 字段ID
   * @param {string} newName - 新名称
   * @returns {Promise<boolean>}
   */
  async updateReportTable(reportType, fieldId, newName) {
    try {
      const reportConfig = JSON.parse(localStorage.getItem(`${reportType}Config`) || '{}');
      
      if (!reportConfig.customFieldNames) {
        reportConfig.customFieldNames = {};
      }
      
      reportConfig.customFieldNames[fieldId] = newName;
      localStorage.setItem(`${reportType}Config`, JSON.stringify(reportConfig));
      
      console.log(`${reportType} 字段 ${fieldId} 更新为: ${newName}`);
      return true;
    } catch (error) {
      console.error(`更新 ${reportType} 失败:`, error);
      throw error;
    }
  }
  
  /**
   * 获取字段类型
   * @param {string} fieldId - 字段ID
   * @returns {string} 字段类型 ('data' | 'formula')
   */
  getFieldType(fieldId) {
    if (fieldId.startsWith('data')) {
      return 'data';
    } else if (fieldId.startsWith('formula') || fieldId.startsWith('result')) {
      return 'formula';
    }
    return 'unknown';
  }
  
  /**
   * 获取字段索引
   * @param {string} fieldId - 字段ID
   * @returns {string} 字段索引
   */
  getFieldIndex(fieldId) {
    const match = fieldId.match(/(\d+)_(\d+)$/);
    return match ? `${match[1]}_${match[2]}` : '';
  }
  
  /**
   * 获取同步历史
   * @param {number} limit - 限制数量
   * @returns {Array} 同步历史记录
   */
  getSyncHistory(limit = 50) {
    return this.syncHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }
  
  /**
   * 获取同步错误
   * @param {number} limit - 限制数量
   * @returns {Array} 错误记录
   */
  getSyncErrors(limit = 20) {
    return this.syncErrors
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }
  
  /**
   * 清除同步历史
   */
  clearSyncHistory() {
    this.syncHistory = [];
    this.syncErrors = [];
  }
  
  /**
   * 获取同步状态
   * @returns {Object} 同步状态信息
   */
  getSyncStatus() {
    return {
      isSync: this.isSync,
      historyCount: this.syncHistory.length,
      errorCount: this.syncErrors.length,
      lastSyncTime: this.syncHistory.length > 0 
        ? this.syncHistory[this.syncHistory.length - 1].timestamp 
        : null
    };
  }
}

// 创建全局实例
export const nameSyncManager = new NameSyncManager();

// 默认导出
export default NameSyncManager;
