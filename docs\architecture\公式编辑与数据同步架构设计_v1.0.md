# 公式编辑与数据同步功能 - 技术架构设计

## 1. 文档信息
- **项目名称**: 财务管理系统 - 公式编辑与数据同步功能
- **版本**: v1.0
- **创建日期**: 2025年7月29日
- **负责人**: Bob (架构师)
- **技术栈**: Next.js 14 + React 18 + Prisma + SQLite

## 2. 架构概览

### 2.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (React)                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  公式编辑器组件   │  │  字段名称编辑器   │  │   数据同步管理   │ │
│  │ FormulaEditor   │  │ FieldNameEditor │  │  SyncManager   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Services)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   公式管理服务    │  │   字段同步服务    │  │   汇总表服务     │ │
│  │ FormulaService  │  │  FieldSyncSvc   │  │ SummaryService │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Prisma)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   公式配置表     │  │   字段配置表     │  │    汇总数据表    │ │
│  │ FormulaConfig   │  │  FieldConfig    │  │  SummaryData   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 3. 数据库设计

### 3.1 新增数据表

#### 3.1.1 公式配置表 (FormulaConfig)
```sql
CREATE TABLE FormulaConfig (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  moduleId TEXT NOT NULL,           -- 模块ID (如 module1_1)
  formulaKey TEXT NOT NULL,         -- 公式键名 (如 公式1.1)
  customName TEXT,                  -- 用户自定义名称
  expression TEXT NOT NULL,         -- 公式表达式
  description TEXT,                 -- 公式描述
  isActive BOOLEAN DEFAULT true,    -- 是否启用
  isDefault BOOLEAN DEFAULT false,  -- 是否为默认公式
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(moduleId, formulaKey)
);
```

#### 3.1.2 字段配置表 (FieldConfig)
```sql
CREATE TABLE FieldConfig (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  moduleId TEXT NOT NULL,           -- 模块ID
  fieldKey TEXT NOT NULL,           -- 字段键名 (如 data1)
  defaultName TEXT NOT NULL,        -- 默认名称
  customName TEXT,                  -- 用户自定义名称
  fieldType TEXT NOT NULL,          -- 字段类型
  isActive BOOLEAN DEFAULT true,    -- 是否启用
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(moduleId, fieldKey)
);
```

#### 3.1.3 数据同步日志表 (SyncLog)
```sql
CREATE TABLE SyncLog (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  sourceModule TEXT NOT NULL,       -- 源模块
  targetModule TEXT NOT NULL,       -- 目标模块
  fieldKey TEXT NOT NULL,           -- 字段键名
  oldValue TEXT,                    -- 旧值
  newValue TEXT,                    -- 新值
  syncType TEXT NOT NULL,           -- 同步类型 (field_name, formula)
  status TEXT DEFAULT 'success',    -- 同步状态
  errorMessage TEXT,                -- 错误信息
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 现有表结构扩展

#### 3.2.1 汇总表增强 (SummaryData)
```sql
-- 在现有 FinancialData 表基础上增加字段
ALTER TABLE FinancialData ADD COLUMN customFieldNames TEXT; -- JSON格式存储自定义字段名
ALTER TABLE FinancialData ADD COLUMN customFormulas TEXT;   -- JSON格式存储自定义公式
ALTER TABLE FinancialData ADD COLUMN lastSyncTime DATETIME; -- 最后同步时间
```

## 4. 核心组件设计

### 4.1 公式编辑器组件 (FormulaEditor)

#### 4.1.1 组件接口
```javascript
// components/FormulaEditor.js
export default function FormulaEditor({
  moduleId,                    // 模块ID
  formulas,                   // 当前公式配置
  onFormulaChange,            // 公式变更回调
  onFormulaSave,              // 公式保存回调
  readOnly = false            // 是否只读
}) {
  // 组件实现
}
```

#### 4.1.2 核心功能
- 公式名称编辑 (支持中文、英文)
- 公式表达式编辑 (语法高亮)
- 实时语法验证
- 计算结果预览
- 默认公式重置

### 4.2 字段名称编辑器 (FieldNameEditor)

#### 4.2.1 组件接口
```javascript
// components/FieldNameEditor.js
export default function FieldNameEditor({
  moduleId,                   // 模块ID
  fieldConfigs,              // 字段配置
  onFieldNameChange,         // 字段名变更回调
  onFieldNameSave,           // 字段名保存回调
  syncEnabled = true         // 是否启用同步
}) {
  // 组件实现
}
```

#### 4.2.2 核心功能
- 字段名称内联编辑
- 实时预览效果
- 同步状态指示
- 批量重置功能

### 4.3 数据同步管理器 (SyncManager)

#### 4.3.1 服务接口
```javascript
// services/SyncManager.js
export class SyncManager {
  // 字段名称同步
  static async syncFieldName(sourceModule, fieldKey, newName) {
    // 1. 更新源模块字段配置
    // 2. 查找关联模块
    // 3. 批量更新关联引用
    // 4. 记录同步日志
  }
  
  // 公式同步
  static async syncFormula(moduleId, formulaKey, newExpression) {
    // 1. 验证公式语法
    // 2. 更新公式配置
    // 3. 重新计算相关数据
    // 4. 更新汇总表
  }
  
  // 汇总表数据植入
  static async plantToSummary(moduleData) {
    // 1. 数据格式转换
    // 2. 插入汇总表
    // 3. 更新统计信息
  }
}
```

## 5. 数据流设计

### 5.1 公式编辑流程
```
用户编辑公式 → 实时语法验证 → 保存到数据库 → 重新计算结果 → 更新界面显示
     ↓
触发数据同步 → 更新汇总表 → 记录操作日志
```

### 5.2 字段名称同步流程
```
用户修改字段名 → 检测关联模块 → 批量更新引用 → 界面实时刷新
     ↓
更新公式表达式 → 更新报表显示 → 记录同步日志
```

### 5.3 汇总表植入流程
```
数据保存触发 → 数据验证 → 格式转换 → 插入汇总表 → 更新统计
     ↓
发送通知 → 界面状态更新
```

## 6. API设计

### 6.1 公式管理API
```javascript
// API路由: /api/formulas
POST   /api/formulas              // 创建公式
GET    /api/formulas/:moduleId    // 获取模块公式
PUT    /api/formulas/:id          // 更新公式
DELETE /api/formulas/:id          // 删除公式
POST   /api/formulas/reset        // 重置为默认
```

### 6.2 字段配置API
```javascript
// API路由: /api/fields
POST   /api/fields                // 创建字段配置
GET    /api/fields/:moduleId      // 获取模块字段配置
PUT    /api/fields/:id            // 更新字段配置
POST   /api/fields/sync           // 触发字段同步
```

### 6.3 数据同步API
```javascript
// API路由: /api/sync
POST   /api/sync/field-name       // 字段名称同步
POST   /api/sync/formula          // 公式同步
POST   /api/sync/summary          // 汇总表同步
GET    /api/sync/logs             // 获取同步日志
```

## 7. 安全设计

### 7.1 公式安全验证
- 表达式白名单验证
- 禁止执行危险函数
- 输入长度限制
- SQL注入防护

### 7.2 数据同步安全
- 事务性操作保证
- 回滚机制
- 并发控制
- 数据完整性检查

## 8. 性能优化

### 8.1 计算性能
- 公式结果缓存
- 增量计算
- 异步处理
- 批量操作

### 8.2 同步性能
- 防抖处理
- 批量同步
- 后台队列
- 进度提示

## 9. 错误处理

### 9.1 公式错误
- 语法错误提示
- 计算异常处理
- 默认值回退
- 用户友好提示

### 9.2 同步错误
- 失败重试机制
- 错误日志记录
- 状态回滚
- 用户通知

## 10. 测试策略

### 10.1 单元测试
- 公式计算引擎测试
- 数据同步逻辑测试
- API接口测试
- 组件功能测试

### 10.2 集成测试
- 跨模块同步测试
- 数据一致性测试
- 性能压力测试
- 用户场景测试

---

**架构设计状态**: ✅ 已完成
**下一步**: 开始具体功能实现
