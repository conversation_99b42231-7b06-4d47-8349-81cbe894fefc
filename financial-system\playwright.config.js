// Playwright配置文件 - 用于自动化测试股东功能和数据持久化

const { defineConfig, devices } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './',
  testMatch: 'playwright-test.js',
  
  // 全局测试超时时间
  timeout: 30 * 1000,
  
  // 每个测试的期望超时时间
  expect: {
    timeout: 5000,
  },
  
  // 失败时重试次数
  retries: 2,
  
  // 并行运行的worker数量
  workers: 1,
  
  // 报告器配置
  reporter: [
    ['html'],
    ['list'],
    ['json', { outputFile: 'test-results.json' }]
  ],
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: 'http://localhost:3000',
    
    // 浏览器上下文选项
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 视口大小
    viewport: { width: 1280, height: 720 },
    
    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 等待策略
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },

  // 项目配置 - 不同浏览器
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // 显示浏览器窗口，慢速模式便于观察
        headless: false,
        launchOptions: {
          slowMo: 2000
        }
      },
    },
    
    // 可以添加更多浏览器测试
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
  ],

  // 开发服务器配置
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },
});
