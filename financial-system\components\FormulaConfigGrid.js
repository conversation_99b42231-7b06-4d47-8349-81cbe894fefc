'use client';

import React, { useState } from 'react';
import { Card, Row, Col, Button } from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import EditableFormulaCard from './EditableFormulaCard';
import FormulaEditor from './FormulaEditor';
import { useFormula } from '../contexts/FormulaContext';

// 公式配置网格组件
const FormulaConfigGrid = () => {
  const { formulas, updateFormula, deleteFormula, savePlant, isCalculating } = useFormula();
  const [editingFormula, setEditingFormula] = useState(null);

  // 确保有9个公式位置 (3×3网格)
  const formulaSlots = Array.from({ length: 9 }, (_, index) => {
    const row = Math.floor(index / 3) + 1;
    const col = (index % 3) + 1;
    const formulaId = `formula_${row}_${col}`;
    
    return formulas[formulaId] || { 
      id: formulaId, 
      name: `公式${row}.${col}`, 
      expression: '', 
      isValid: false,
      description: '点击编辑添加公式'
    };
  });

  // 处理公式编辑
  const handleEditFormula = (formula) => {
    setEditingFormula(formula);
  };

  // 处理公式保存
  const handleSaveFormula = (updatedFormula) => {
    updateFormula(updatedFormula);
    setEditingFormula(null);
  };

  // 处理公式删除
  const handleDeleteFormula = (formulaId) => {
    deleteFormula(formulaId);
  };

  // 处理保存植入
  const handleSavePlant = async () => {
    await savePlant();
  };

  return (
    <Card 
      title="动态公式配置" 
      className="formula-config-grid"
      style={{ marginBottom: '24px' }}
      bodyStyle={{ padding: '16px' }}
    >
      {/* 公式网格 */}
      <div className="formula-grid" style={{ marginBottom: '24px' }}>
        {[0, 1, 2].map(row => (
          <Row key={row} gutter={[16, 16]} style={{ marginBottom: '16px' }}>
            {[0, 1, 2].map(col => {
              const index = row * 3 + col;
              const formula = formulaSlots[index];
              
              return (
                <Col key={col} span={8}>
                  <EditableFormulaCard
                    formula={formula}
                    onEdit={() => handleEditFormula(formula)}
                    onDelete={() => handleDeleteFormula(formula.id)}
                  />
                </Col>
              );
            })}
          </Row>
        ))}
      </div>

      {/* 保存植入按钮 */}
      <div 
        className="save-section" 
        style={{ 
          textAlign: 'center', 
          padding: '16px 0', 
          borderTop: '1px solid #d9d9d9' 
        }}
      >
        <Button 
          type="primary" 
          size="large"
          icon={<SaveOutlined />}
          loading={isCalculating}
          onClick={handleSavePlant}
          style={{
            backgroundColor: '#FFD700',
            borderColor: '#FFD700',
            color: '#000',
            fontWeight: 'bold',
            minWidth: '120px',
            boxShadow: '0 2px 4px rgba(255, 215, 0, 0.3)'
          }}
        >
          保存植入
        </Button>
      </div>

      {/* 公式编辑器弹窗 */}
      {editingFormula && (
        <FormulaEditor
          formula={editingFormula}
          visible={!!editingFormula}
          onSave={handleSaveFormula}
          onCancel={() => setEditingFormula(null)}
        />
      )}
    </Card>
  );
};

export default FormulaConfigGrid;
