# 7模块财务管理系统产品需求文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-07-28
- **负责人**: Emma (产品经理)
- **项目**: 7模块综合财务管理系统扩展

## 2. 背景与问题陈述

### 2.1 当前状态
- 现有系统仅支持单一模块（Module 1.1）
- 包含7个数据输入字段 + 5个计算结果字段
- 已实现完善的数据编辑、公式计算、实时同步功能
- 百分比字段已统一为小数格式

### 2.2 业务需求
用户需要一个综合性的财务管理系统，能够处理7个不同的业务模块，每个模块有不同的数据结构和计算需求，但需要在统一界面中管理。

## 3. 目标与成功指标

### 3.1 项目目标
1. **功能扩展**: 从1个模块扩展到7个模块
2. **界面统一**: 所有模块集成在单一界面中
3. **数据隔离**: 各模块数据独立管理，互不干扰
4. **体验一致**: 保持现有的优秀用户体验

### 3.2 成功指标
- ✅ 7个模块全部实现并正常运行
- ✅ 模块切换流畅，响应时间<500ms
- ✅ 数据隔离完整，无交叉污染
- ✅ 界面布局合理，用户操作直观

## 4. 模块规格详述

### 4.1 模块配置表

| 模块 | 数据输入字段 | 计算结果字段 | 总字段数 | 特殊说明 |
|------|-------------|-------------|----------|----------|
| Module 1.1 | 7个 | 5个 | 12个 | 当前已实现 |
| Module 2.1 | 7个 | 5个 | 12个 | 与1.1结构相同 |
| Module 3.1 | 4个 | 1个 | 5个 | 简化模块 |
| Module 4.1 | 7个 | 5个 | 12个 | 标准模块 |
| Module 5.1 | 7个 | 5个 | 12个 | 标准模块 |
| Module 6.1 | 7个 | 4个 | 11个 | 少一个结果字段 |
| Module 7.1 | 7个 | 4个 | 11个 | 少一个结果字段 |

### 4.2 字段类型定义

**数据输入字段类型**:
- `data1`: 千万整数 (格式化显示)
- `data2`: 十亿整数 (格式化显示)  
- `data3`: 小数格式 (代表百分比)
- `data4`: 高精度数字 (8位小数)
- `data5`: 百分比 (传统百分比显示)
- `data6`: 通用数值字段
- `data7`: 通用数值字段

**计算结果字段**:
- `result1-result5`: 基于公式计算的结果字段

## 5. 界面设计规格

### 5.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    页面标题 + 导航                        │
├─────────────────────────────────────────────────────────┤
│                    模块选择器                            │
├─────────────────────────────────────────────────────────┤
│                  全局公式配置区域                         │
│  [公式1.1] [公式1.2] [公式1.3] [公式1.4] [公式1.5]        │
├─────────────────────────────────────────────────────────┤
│                  数据管理操作区域                         │
│  [添加客户] [保存数据] [刷新数据]                         │
├─────────────────────────────────────────────────────────┤
│                  客户数据表格区域                         │
│  动态表格 - 根据选中模块显示对应字段                      │
├─────────────────────────────────────────────────────────┤
│                  汇总统计区域                            │
│  客户总数 | 总收入 | 总支出 | 净利润 | 利润率 | 周平均利润  │
└─────────────────────────────────────────────────────────┘
```

### 5.2 模块选择器设计
- **位置**: 页面顶部，标题下方
- **样式**: Tab标签页形式
- **功能**: 点击切换模块，保持数据状态
- **标识**: 每个模块显示名称和字段数量

### 5.3 动态表格设计
- **自适应列**: 根据选中模块动态显示对应列
- **列标题**: 根据模块配置动态生成
- **数据隔离**: 切换模块时加载对应模块的数据
- **操作一致**: 保持现有的编辑、删除、计算功能

## 6. 技术实现规格

### 6.1 数据结构设计

**模块配置对象**:
```javascript
const MODULE_CONFIGS = {
  'module1_1': {
    name: 'Module 1.1',
    inputFields: 7,
    resultFields: 5,
    fields: {
      // 字段定义
    },
    formulas: {
      // 公式定义
    }
  },
  // ... 其他模块
}
```

**客户数据结构**:
```javascript
{
  id: number,
  name: string,
  userName: string,
  agentId: number,
  moduleId: string,  // 新增：模块标识
  data1: number,
  data2: number,
  // ... 动态字段
  createdAt: Date,
  updatedAt: Date
}
```

### 6.2 状态管理设计
- **当前模块状态**: 记录用户选中的模块
- **模块数据缓存**: 缓存各模块的客户数据
- **公式状态隔离**: 每个模块独立的公式配置
- **编辑状态管理**: 支持跨模块的编辑状态保持

## 7. 开发计划

### 7.1 开发阶段
1. **阶段1**: 模块配置和数据结构设计 (10分钟)
2. **阶段2**: 界面重构和模块切换实现 (15分钟)  
3. **阶段3**: 数据管理和隔离机制 (10分钟)
4. **阶段4**: 功能测试和验证 (10分钟)

### 7.2 技术风险
- **性能风险**: 7个模块数据量可能较大
- **状态管理复杂性**: 多模块状态同步
- **界面响应性**: 模块切换的流畅度

### 7.3 风险缓解
- 实现数据懒加载和缓存机制
- 使用React状态管理最佳实践
- 优化界面渲染性能

## 8. 验收标准

### 8.1 功能验收
- [ ] 7个模块全部实现并可正常切换
- [ ] 每个模块的数据输入、编辑、删除功能正常
- [ ] 公式计算在各模块中正确执行
- [ ] 数据在模块间完全隔离，无交叉影响

### 8.2 性能验收  
- [ ] 模块切换响应时间 < 500ms
- [ ] 数据加载时间 < 1s
- [ ] 界面操作流畅，无明显卡顿

### 8.3 用户体验验收
- [ ] 界面布局合理，信息层次清晰
- [ ] 操作逻辑一致，学习成本低
- [ ] 错误处理完善，用户反馈及时
