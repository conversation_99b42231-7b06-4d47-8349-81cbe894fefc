/* 财务管理系统全局样式 - JavaScript版本 */
/* 避免TypeScript版本冲突 */

/* 重置默认样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f0f2f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: #f0f2f5;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-table {
  background: #fff;
  border-radius: 6px;
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 自定义样式 */
.total-row {
  background-color: #f0f8ff !important;
  font-weight: bold;
}

.editable-cell {
  position: relative;
}

.editable-cell-input-wrapper {
  padding: 5px 11px;
}

.editable-cell-text-wrapper {
  padding: 5px 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-table {
    font-size: 12px;
  }

  .ant-card {
    margin: 8px;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 错误提示样式 */
.error-message {
  color: #ff4d4f;
  text-align: center;
  padding: 20px;
}
