// 简化的功能测试脚本 - 测试汇总统计总和计算逻辑
// 直接测试计算函数而不依赖浏览器

const { FormulaPageConsolidationCalculations } = require('./lib/formulaPageConsolidationCalculations');

function testSummaryTotalCalculation() {
  console.log('🧮 开始测试汇总统计总和计算逻辑...');

  // 1. 准备测试数据
  console.log('📊 步骤1: 准备测试数据');
  
  const testCustomerData = {
    group1: [
      {
        key: 'customer1',
        customer: '测试客户A',
        user: '测试用户A',
        data1: 1000,
        data2: 2000,
        data3: 0.1,
        data4: 500,
        data5: 0.05,
        data6: 800,
        data7: 1200,
        result1: 3500,
        result2: 2000,
        result3: 1500,
        shareholders: [
          {
            key: 'sh1',
            name: '股东A',
            ratio: 0.6,
            result1: 100,
            result2: 200
          }
        ]
      }
    ]
  };

  const testManualInputs = {
    '测试客户A_收入数量': 10,
    '测试客户A_收入价格': 100,
    '测试客户A_付出数量': 5,
    '测试客户A_付出价格': 80,
    '测试客户A_上周余额1': 200,
    '测试客户A_上周余额2': 150,
    '测试客户A_减免1': 50,
    '测试客户A_减免2': 30
  };

  // 2. 执行数据归纳计算
  console.log('⚙️ 步骤2: 执行数据归纳计算');
  
  const consolidatedResult = FormulaPageConsolidationCalculations.consolidateFormulaPageData(
    testCustomerData,
    testManualInputs
  );

  console.log('✅ 归纳计算完成');
  console.log('📋 归纳结果:', JSON.stringify(consolidatedResult, null, 2));

  // 3. 验证计算结果
  console.log('🔍 步骤3: 验证计算结果');

  // 检查是否有客户数据
  const customerNames = Object.keys(consolidatedResult.consolidatedByUser);
  console.log('👥 客户列表:', customerNames);

  if (customerNames.length === 0) {
    console.error('❌ 错误: 没有找到客户数据');
    return false;
  }

  // 获取测试客户的数据
  const testCustomerName = '测试客户A';
  const customerRecords = consolidatedResult.consolidatedByUser[testCustomerName];
  
  if (!customerRecords) {
    console.error('❌ 错误: 没有找到测试客户数据');
    return false;
  }

  console.log('📊 客户记录:', customerRecords);

  // 4. 手动计算预期结果
  console.log('🧮 步骤4: 手动计算预期结果');

  // 计算客户总和（来自左侧表格的roundedSum）
  const 客户总和 = customerRecords.reduce((sum, record) => {
    return sum + (record.roundedSum || 0);
  }, 0);

  // 计算汇总统计金额
  const 收入金额 = testManualInputs['测试客户A_收入数量'] * testManualInputs['测试客户A_收入价格'];
  const 付出金额 = testManualInputs['测试客户A_付出数量'] * testManualInputs['测试客户A_付出价格'];
  const 上周余额金额 = testManualInputs['测试客户A_上周余额1'] + testManualInputs['测试客户A_上周余额2'];
  const 减免金额 = testManualInputs['测试客户A_减免1'] + testManualInputs['测试客户A_减免2'];
  const 汇总统计总金额 = 收入金额 + 付出金额 + 上周余额金额 + 减免金额;

  // 计算最终总和
  const 最终总和 = 客户总和 + 汇总统计总金额;

  console.log('📈 计算结果:');
  console.log('   客户总和:', 客户总和);
  console.log('   收入金额:', 收入金额, '(', testManualInputs['测试客户A_收入数量'], '×', testManualInputs['测试客户A_收入价格'], ')');
  console.log('   付出金额:', 付出金额, '(', testManualInputs['测试客户A_付出数量'], '×', testManualInputs['测试客户A_付出价格'], ')');
  console.log('   上周余额金额:', 上周余额金额, '(', testManualInputs['测试客户A_上周余额1'], '+', testManualInputs['测试客户A_上周余额2'], ')');
  console.log('   减免金额:', 减免金额, '(', testManualInputs['测试客户A_减免1'], '+', testManualInputs['测试客户A_减免2'], ')');
  console.log('   汇总统计总金额:', 汇总统计总金额);
  console.log('   最终总和:', 最终总和);

  // 5. 验证数据完整性
  console.log('✅ 步骤5: 验证数据完整性');

  let allTestsPassed = true;

  // 检查客户记录是否有roundedSum字段
  customerRecords.forEach((record, index) => {
    if (typeof record.roundedSum === 'undefined') {
      console.error(`❌ 客户记录${index}缺少roundedSum字段`);
      allTestsPassed = false;
    } else {
      console.log(`✓ 客户记录${index} roundedSum:`, record.roundedSum);
    }
  });

  // 检查计算逻辑
  if (收入金额 !== 1000) {
    console.error('❌ 收入金额计算错误，预期1000，实际', 收入金额);
    allTestsPassed = false;
  }

  if (付出金额 !== 400) {
    console.error('❌ 付出金额计算错误，预期400，实际', 付出金额);
    allTestsPassed = false;
  }

  if (上周余额金额 !== 350) {
    console.error('❌ 上周余额金额计算错误，预期350，实际', 上周余额金额);
    allTestsPassed = false;
  }

  if (减免金额 !== 80) {
    console.error('❌ 减免金额计算错误，预期80，实际', 减免金额);
    allTestsPassed = false;
  }

  if (汇总统计总金额 !== 1830) {
    console.error('❌ 汇总统计总金额计算错误，预期1830，实际', 汇总统计总金额);
    allTestsPassed = false;
  }

  if (allTestsPassed) {
    console.log('🎉 所有计算逻辑测试通过！');
    console.log('📊 功能实现正确，可以在界面中正常显示总和计算');
    return true;
  } else {
    console.log('❌ 部分测试失败，需要检查计算逻辑');
    return false;
  }
}

// 运行测试
if (require.main === module) {
  const success = testSummaryTotalCalculation();
  process.exit(success ? 0 : 1);
}

module.exports = { testSummaryTotalCalculation };
