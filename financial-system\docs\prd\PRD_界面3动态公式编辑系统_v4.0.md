# 界面3动态公式编辑系统 PRD v4.0

## 1. 产品概述

### 1.1 产品定位
界面3是一个**动态公式编辑和计算系统**，允许用户自定义输入、编辑和管理财务计算公式，支持实时计算和多客户数据管理。

### 1.2 核心价值
- **灵活性**：用户可以自由定义和修改任何计算公式
- **实时性**：公式修改后立即重新计算所有相关数据
- **可扩展性**：支持任意数量的客户和公式组合
- **易用性**：直观的界面设计，简单的操作流程

## 2. 功能需求详述

### 2.1 动态公式编辑功能

#### 2.1.1 公式配置区域
**布局**：3行×3列 = 9个公式编辑卡片

**每个公式卡片包含**：
- **公式名称**：可编辑的公式标题（如"公式1.1"）
- **公式表达式**：可编辑的计算表达式输入框
- **编辑按钮**：点击进入公式编辑模式
- **删除按钮**：删除当前公式
- **验证状态**：显示公式是否有效

#### 2.1.2 公式编辑器
**触发方式**：点击公式卡片或编辑按钮

**编辑器功能**：
- **表达式输入框**：支持数学表达式输入
- **变量提示**：显示可用变量（data1-data7）
- **语法验证**：实时验证表达式语法
- **预览计算**：显示当前表达式的计算结果
- **保存/取消**：确认或取消修改

#### 2.1.3 支持的表达式语法
```
变量：data1, data2, data3, data4, data5, data6, data7
运算符：+, -, *, /, (, )
函数：暂不支持复杂函数
示例：(data1+data2*data4)*data3
```

### 2.2 数据字段扩展

#### 2.2.1 数据输入字段
- **字段范围**：data1 到 data7（7个数据字段）
- **数据类型**：数值型（支持整数和小数）
- **输入验证**：非空验证、数值格式验证

#### 2.2.2 详细数据表格列定义
```
| 客户 | 数据1 | 数据2 | 数据3 | 数据4 | 数据5 | 数据6 | 数据7 | 计算值 | 结果 | 结果5 |
```

**列说明**：
- **客户**：客户名称（白色背景）
- **数据1-7**：输入数据（白色背景，可编辑）
- **计算值**：中间计算结果（蓝色背景）
- **结果**：最终计算结果（橙色背景）
- **结果5**：第5个公式的计算结果（橙色背景）

### 2.3 客户管理功能

#### 2.3.1 添加客户功能
**位置**：公式配置区域下方
**按钮样式**：主要按钮，绿色背景
**功能**：
- 点击弹出客户信息输入框
- 输入客户名称
- 自动为新客户初始化默认数据值
- 更新所有公式计算表格

#### 2.3.2 删除客户功能
**位置**：每个客户行的操作列
**按钮样式**：危险按钮，红色背景
**功能**：
- 确认删除对话框
- 删除客户及其所有数据
- 更新所有公式计算表格

#### 2.3.3 客户数据编辑
**编辑方式**：双击单元格进入编辑模式
**实时计算**：数据修改后立即重新计算所有公式
**数据验证**：确保输入的是有效数值

### 2.4 公式管理功能

#### 2.4.1 添加公式
**触发**：点击空的公式卡片或"添加公式"按钮
**流程**：
1. 输入公式名称
2. 输入公式表达式
3. 验证表达式有效性
4. 保存并更新计算

#### 2.4.2 删除公式
**触发**：点击公式卡片上的删除按钮
**流程**：
1. 确认删除对话框
2. 删除公式配置
3. 移除相关计算列
4. 重新排列界面布局

#### 2.4.3 公式验证
**实时验证**：用户输入时实时检查语法
**错误提示**：显示具体的错误信息
**有效性标识**：绿色✓表示有效，红色✗表示无效

## 3. 界面设计要求

### 3.1 布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    界面3 - 动态公式编辑系统                    │
├─────────────────────────────────────────────────────────┤
│  公式配置区域 (3×3网格)                                      │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐                      │
│  │ 公式1.1  │ │ 公式1.2  │ │ 公式1.3  │                      │
│  │ 可编辑   │ │ 可编辑   │ │ 可编辑   │                      │
│  └─────────┘ └─────────┘ └─────────┘                      │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐                      │
│  │ 公式2.1  │ │ 公式2.2  │ │ 公式2.3  │                      │
│  └─────────┘ └─────────┘ └─────────┘                      │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐                      │
│  │ 公式3.1  │ │ 公式3.2  │ │ 公式3.3  │                      │
│  └─────────┘ └─────────┘ └─────────┘                      │
├─────────────────────────────────────────────────────────┤
│  客户管理区域                                               │
│  [添加客户] [保存植入]                                       │
├─────────────────────────────────────────────────────────┤
│  详细数据表格区域                                           │
│  客户 | 数据1-7 | 计算值 | 结果 | 结果5 | 操作              │
└─────────────────────────────────────────────────────────┘
```

### 3.2 颜色编码系统
- **公式名称标签**：黄色背景 (#FFD700)
- **公式表达式**：浅黄色背景 (#FFF8DC)
- **客户名称**：白色背景 (#FFFFFF)
- **输入数据**：白色背景 (#FFFFFF)
- **计算过程**：蓝色背景 (#87CEEB)
- **结果数据**：橙色背景 (#FFA500)
- **错误状态**：红色边框 (#FF0000)
- **成功状态**：绿色边框 (#00FF00)

### 3.3 交互设计
- **悬停效果**：公式卡片悬停时轻微上浮
- **编辑状态**：编辑中的元素有明显的视觉反馈
- **加载状态**：计算过程中显示加载动画
- **错误提示**：友好的错误信息提示

## 4. 技术实现要求

### 4.1 公式解析引擎
- **表达式解析**：安全的数学表达式解析器
- **变量替换**：动态替换data1-data7变量
- **错误处理**：捕获和处理计算错误
- **性能优化**：缓存计算结果，避免重复计算

### 4.2 数据管理
- **状态管理**：使用React Context管理全局状态
- **数据持久化**：本地存储公式配置和客户数据
- **实时更新**：数据变更时自动重新计算
- **数据验证**：输入数据的格式和范围验证

### 4.3 组件架构
```
DynamicFormulaSystem (主组件)
├── FormulaConfigGrid (公式配置网格)
│   ├── EditableFormulaCard (可编辑公式卡片)
│   └── FormulaEditor (公式编辑器)
├── CustomerManagement (客户管理)
│   ├── AddCustomerButton (添加客户按钮)
│   └── CustomerList (客户列表)
└── DetailedDataTables (详细数据表格)
    ├── DataTable (数据表格)
    └── CalculationEngine (计算引擎)
```

## 5. 用户操作流程

### 5.1 编辑公式流程
1. 点击公式卡片进入编辑模式
2. 修改公式名称或表达式
3. 系统实时验证语法
4. 点击保存确认修改
5. 系统重新计算所有相关数据

### 5.2 管理客户流程
1. 点击"添加客户"按钮
2. 输入客户名称
3. 系统创建新客户行
4. 用户输入客户数据
5. 系统自动计算所有公式结果

### 5.3 数据编辑流程
1. 双击数据单元格
2. 输入新的数值
3. 按回车确认或点击其他地方
4. 系统立即重新计算相关公式
5. 更新显示结果

## 6. 验收标准

### 6.1 功能验收
- ✅ 支持9个公式的动态编辑
- ✅ 支持data1-data7数据字段
- ✅ 支持添加/删除客户功能
- ✅ 实时计算和结果更新
- ✅ 公式语法验证和错误提示

### 6.2 性能验收
- ✅ 公式编辑响应时间 < 100ms
- ✅ 数据计算响应时间 < 200ms
- ✅ 支持至少50个客户同时计算
- ✅ 界面操作流畅无卡顿

### 6.3 用户体验验收
- ✅ 界面布局清晰直观
- ✅ 操作流程简单易懂
- ✅ 错误提示友好明确
- ✅ 颜色编码系统一致

---

**Emma完成时间**: 2025年7月30日  
**下一步**: 提交给Bob进行技术架构设计  
**核心目标**: 实现完全动态的公式编辑系统
