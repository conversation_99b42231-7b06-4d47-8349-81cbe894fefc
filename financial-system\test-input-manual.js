// 手动测试输入验证功能
// 这个脚本模拟用户输入，验证我们的验证逻辑

// 复制我们的验证函数
function validateNumberInput(value, field = 'default') {
  // 移除所有非数字字符（除了小数点）
  const cleanValue = value.replace(/[^0-9.]/g, '');

  // 确保只有一个小数点
  const parts = cleanValue.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }

  // 根据字段设置不同的小数位限制
  let maxDecimalPlaces = 2; // 默认2位小数
  if (field === 'data3') {
    maxDecimalPlaces = 3; // 数据3允许3位小数
  } else if (field === 'data4') {
    maxDecimalPlaces = 8; // 数据4允许8位小数
  }

  // 限制小数点后的位数
  if (parts.length === 2 && parts[1].length > maxDecimalPlaces) {
    return parts[0] + '.' + parts[1].substring(0, maxDecimalPlaces);
  }

  // 确保不以小数点开头
  if (cleanValue.startsWith('.')) {
    return '0' + cleanValue;
  }

  return cleanValue;
}

// 测试用例
const testInputs = [
  // 数据3测试（3位小数）
  { input: '123.4567', field: 'data3', description: '数据3: 输入123.4567' },
  { input: '0.123', field: 'data3', description: '数据3: 输入0.123' },
  { input: '.456', field: 'data3', description: '数据3: 输入.456' },
  { input: '-123.456', field: 'data3', description: '数据3: 输入-123.456（负数）' },
  
  // 数据4测试（8位小数）
  { input: '123.123456789', field: 'data4', description: '数据4: 输入123.123456789' },
  { input: '0.12345678', field: 'data4', description: '数据4: 输入0.12345678' },
  { input: '.87654321', field: 'data4', description: '数据4: 输入.87654321' },
  { input: '-123.12345678', field: 'data4', description: '数据4: 输入-123.12345678（负数）' },
  
  // 数据1测试（2位小数）
  { input: '123.456', field: 'data1', description: '数据1: 输入123.456' },
  { input: '0.12', field: 'data1', description: '数据1: 输入0.12' },
  { input: '-123.45', field: 'data1', description: '数据1: 输入-123.45（负数）' },
  
  // 特殊情况测试
  { input: 'abc123.456def', field: 'data3', description: '包含字母: abc123.456def' },
  { input: '123..456', field: 'data1', description: '多个小数点: 123..456' },
  { input: '', field: 'data1', description: '空字符串' },
];

console.log('🧪 手动输入验证测试\n');

testInputs.forEach((test, index) => {
  const result = validateNumberInput(test.input, test.field);
  console.log(`测试 ${index + 1}: ${test.description}`);
  console.log(`  输入: "${test.input}"`);
  console.log(`  输出: "${result}"`);
  console.log(`  字段: ${test.field}`);
  
  // 分析结果
  if (test.input.includes('-') && !result.includes('-')) {
    console.log(`  ✅ 负号被正确移除`);
  }
  
  if (test.field === 'data3' && result.includes('.')) {
    const decimalPart = result.split('.')[1];
    if (decimalPart && decimalPart.length <= 3) {
      console.log(`  ✅ 数据3小数位限制正确 (${decimalPart.length}位)`);
    } else if (decimalPart && decimalPart.length > 3) {
      console.log(`  ❌ 数据3小数位超出限制 (${decimalPart.length}位)`);
    }
  }
  
  if (test.field === 'data4' && result.includes('.')) {
    const decimalPart = result.split('.')[1];
    if (decimalPart && decimalPart.length <= 8) {
      console.log(`  ✅ 数据4小数位限制正确 (${decimalPart.length}位)`);
    } else if (decimalPart && decimalPart.length > 8) {
      console.log(`  ❌ 数据4小数位超出限制 (${decimalPart.length}位)`);
    }
  }
  
  if (test.field === 'data1' && result.includes('.')) {
    const decimalPart = result.split('.')[1];
    if (decimalPart && decimalPart.length <= 2) {
      console.log(`  ✅ 数据1小数位限制正确 (${decimalPart.length}位)`);
    } else if (decimalPart && decimalPart.length > 2) {
      console.log(`  ❌ 数据1小数位超出限制 (${decimalPart.length}位)`);
    }
  }
  
  if (/[^0-9.]/.test(test.input) && !/[^0-9.]/.test(result)) {
    console.log(`  ✅ 非数字字符被正确移除`);
  }
  
  console.log('');
});

console.log('📋 测试总结:');
console.log('- 数据3应该支持最多3位小数');
console.log('- 数据4应该支持最多8位小数');
console.log('- 数据1和数据2应该支持最多2位小数');
console.log('- 所有字段都应该阻止负数输入');
console.log('- 非数字字符应该被自动过滤');

console.log('\n🔧 如果测试通过，请在浏览器中测试实际输入功能:');
console.log('1. 访问 http://localhost:3000/test-input');
console.log('2. 访问 http://localhost:3000/formula-config');
console.log('3. 尝试在数据3字段输入: 123.4567 (应该变为 123.456)');
console.log('4. 尝试在数据4字段输入: 123.123456789 (应该变为 123.12345678)');
console.log('5. 尝试输入负数，应该被阻止');
