'use client';

// 主页面 - 重定向到登录页
// JavaScript版本，避免TypeScript版本冲突

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Spin } from 'antd';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // 延迟执行，避免无限循环
    const timer = setTimeout(() => {
      // 检查登录状态
      const isLoggedIn = localStorage.getItem('isLoggedIn');

      if (isLoggedIn) {
        // 已登录，跳转到仪表盘
        router.push('/dashboard');
      } else {
        // 未登录，跳转到登录页
        router.push('/login');
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []); // 移除router依赖，避免无限循环

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      backgroundColor: '#f0f2f5'
    }}>
      <Spin size="large" tip="正在加载..." />
    </div>
  );
}
