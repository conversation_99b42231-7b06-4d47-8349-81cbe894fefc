// 客户归纳汇总表API路由
// 处理客户归纳汇总数据的获取和保存

import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';

// 获取客户归纳汇总数据
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    
    if (!agentId) {
      return NextResponse.json(
        { error: '代理商ID不能为空' }, 
        { status: 400 }
      );
    }

    // 获取代理商的所有客户数据
    const customers = await prisma.customer.findMany({
      where: { agentId: parseInt(agentId) },
      include: {
        agent: true,
        financialData: true,
        // 包含7个模块的数据
        module1: true,
        module2: true,
        module3: true,
        module4: true,
        module5: true,
        module6: true,
        module7: true,
      },
      orderBy: {
        name: 'asc'
      }
    });

    // 获取股东数据（从公式配置页面的数据库）
    const shareholderData = await getShareholderData(agentId);

    // 获取手动输入数据
    const manualInputs = await getManualInputs(agentId);

    return NextResponse.json({
      customers,
      shareholderData,
      manualInputs,
      metadata: {
        totalCustomers: customers.length,
        agentId: parseInt(agentId),
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error('获取客户归纳汇总数据失败:', error);
    return NextResponse.json(
      { error: '获取数据失败' }, 
      { status: 500 }
    );
  }
}

// 保存手动输入数据
export async function POST(request) {
  try {
    const data = await request.json();
    const { agentId, manualInputs } = data;
    
    if (!agentId || !manualInputs) {
      return NextResponse.json(
        { error: '代理商ID和手动输入数据不能为空' }, 
        { status: 400 }
      );
    }

    // 保存或更新手动输入数据
    const savedData = await saveManualInputs(agentId, manualInputs);

    return NextResponse.json({
      success: true,
      data: savedData,
      message: '手动输入数据保存成功'
    });
  } catch (error) {
    console.error('保存手动输入数据失败:', error);
    return NextResponse.json(
      { error: '保存数据失败' }, 
      { status: 500 }
    );
  }
}

// 获取股东数据的辅助函数
async function getShareholderData(agentId) {
  try {
    // 这里需要根据实际的股东数据存储方式来实现
    // 如果股东数据存储在SQLite数据库中，可以这样查询：
    
    // 假设有一个shareholders表
    // const shareholders = await prisma.shareholder.findMany({
    //   where: { 
    //     customer: {
    //       agentId: parseInt(agentId)
    //     }
    //   },
    //   include: {
    //     customer: true
    //   }
    // });
    
    // 暂时返回空数组，后续可以根据实际需求实现
    return [];
  } catch (error) {
    console.warn('获取股东数据失败:', error);
    return [];
  }
}

// 获取手动输入数据的辅助函数
async function getManualInputs(agentId) {
  try {
    // 查找是否有保存的手动输入数据
    // 可以创建一个专门的表来存储这些数据
    
    // 假设有一个manual_inputs表
    // const manualData = await prisma.manualInput.findFirst({
    //   where: { agentId: parseInt(agentId) }
    // });
    
    // if (manualData) {
    //   return JSON.parse(manualData.data);
    // }
    
    // 返回默认值
    return {
      上周余额: 0,
      减免: 0
    };
  } catch (error) {
    console.warn('获取手动输入数据失败:', error);
    return {
      上周余额: 0,
      减免: 0
    };
  }
}

// 保存手动输入数据的辅助函数
async function saveManualInputs(agentId, manualInputs) {
  try {
    // 这里可以实现将手动输入数据保存到数据库
    // 例如创建或更新manual_inputs表的记录
    
    // const savedData = await prisma.manualInput.upsert({
    //   where: { agentId: parseInt(agentId) },
    //   update: {
    //     data: JSON.stringify(manualInputs),
    //     updatedAt: new Date()
    //   },
    //   create: {
    //     agentId: parseInt(agentId),
    //     data: JSON.stringify(manualInputs),
    //     createdAt: new Date(),
    //     updatedAt: new Date()
    //   }
    // });
    
    // 暂时只返回输入的数据
    return {
      agentId: parseInt(agentId),
      data: manualInputs,
      savedAt: new Date()
    };
  } catch (error) {
    console.error('保存手动输入数据失败:', error);
    throw error;
  }
}

// 导出客户归纳汇总数据
export async function PUT(request) {
  try {
    const data = await request.json();
    const { agentId, exportFormat = 'excel' } = data;
    
    if (!agentId) {
      return NextResponse.json(
        { error: '代理商ID不能为空' }, 
        { status: 400 }
      );
    }

    // 获取完整的归纳汇总数据
    const response = await GET(request);
    const responseData = await response.json();
    
    if (!response.ok) {
      throw new Error(responseData.error);
    }

    // 准备导出数据
    const exportData = prepareExportData(responseData, exportFormat);

    return NextResponse.json({
      success: true,
      exportData,
      filename: `客户归纳汇总表_${agentId}_${new Date().toISOString().split('T')[0]}.${exportFormat}`,
      message: '导出数据准备完成'
    });
  } catch (error) {
    console.error('准备导出数据失败:', error);
    return NextResponse.json(
      { error: '准备导出数据失败' }, 
      { status: 500 }
    );
  }
}

// 准备导出数据的辅助函数
function prepareExportData(data, format) {
  const { customers, shareholderData, manualInputs } = data;
  
  // 这里可以根据不同的导出格式准备数据
  if (format === 'excel') {
    return {
      sheets: [
        {
          name: '客户归纳汇总',
          data: customers.map(customer => ({
            '客户名': customer.name,
            '用户名': customer.userName,
            '数据1': customer.data1 || 0,
            '数据2': customer.data2 || 0,
            '数据6': customer.data6 || 0,
            '数据7': customer.data7 || 0,
            '结果1': customer.result1 || 0,
            '结果2': customer.result2 || 0,
            '结果3': customer.result3 || 0
          }))
        },
        {
          name: '手动输入数据',
          data: [
            { '项目': '上周余额', '数值': manualInputs.上周余额 || 0 },
            { '项目': '减免', '数值': manualInputs.减免 || 0 }
          ]
        }
      ]
    };
  }
  
  return data;
}
