'use client';

// PlantToSummaryButton 组件 - 保存植入用按钮
// 用于将当前模块数据保存并植入到汇总表

import React, { useState } from 'react';
import { Button, message, Modal, Progress, Space, Typography, List } from 'antd';
import { 
  SaveOutlined, 
  UploadOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  LoadingOutlined 
} from '@ant-design/icons';

const { Text, Title } = Typography;

const PlantToSummaryButton = ({
  agentId,                    // 代理商ID
  moduleId,                   // 模块ID
  customerData = [],          // 客户数据数组
  onPlantSuccess,             // 植入成功回调
  onPlantError,               // 植入失败回调
  disabled = false,           // 是否禁用
  size = 'default',           // 按钮尺寸
  type = 'primary',           // 按钮类型
  style = {},                 // 自定义样式
  className = '',             // 自定义类名
  showProgress = true,        // 是否显示进度
  autoClose = true            // 是否自动关闭进度弹窗
}) => {
  // 状态管理
  const [isPlanting, setIsPlanting] = useState(false);
  const [plantProgress, setPlantProgress] = useState(0);
  const [plantResults, setPlantResults] = useState(null);
  const [showProgressModal, setShowProgressModal] = useState(false);
  
  // 验证数据
  const validateData = () => {
    if (!agentId) {
      message.error('代理商ID不能为空');
      return false;
    }
    
    if (!moduleId) {
      message.error('模块ID不能为空');
      return false;
    }
    
    if (!customerData || customerData.length === 0) {
      message.error('没有可植入的客户数据');
      return false;
    }
    
    // 验证每个客户数据的完整性
    for (let i = 0; i < customerData.length; i++) {
      const customer = customerData[i];
      
      if (!customer.name || customer.name.trim().length === 0) {
        message.error(`第${i + 1}行客户名称不能为空`);
        return false;
      }
      
      // 检查必要的数据字段
      const requiredFields = ['data1', 'data2', 'data3', 'data4', 'data5'];
      for (const field of requiredFields) {
        if (customer[field] === undefined || customer[field] === null || customer[field] === '') {
          message.error(`客户"${customer.name}"的${field}不能为空`);
          return false;
        }
        
        if (typeof customer[field] !== 'number' || isNaN(customer[field])) {
          message.error(`客户"${customer.name}"的${field}必须是有效数字`);
          return false;
        }
      }
    }
    
    return true;
  };
  
  // 执行植入操作
  const handlePlant = async () => {
    // 数据验证
    if (!validateData()) {
      return;
    }
    
    // 确认对话框
    Modal.confirm({
      title: '确认植入数据',
      content: `即将植入 ${customerData.length} 条客户数据到汇总表，此操作不可撤销。是否继续？`,
      okText: '确认植入',
      cancelText: '取消',
      icon: <ExclamationCircleOutlined />,
      onOk: async () => {
        await performPlant();
      }
    });
  };
  
  // 执行植入
  const performPlant = async () => {
    setIsPlanting(true);
    setPlantProgress(0);
    setPlantResults(null);
    
    if (showProgress) {
      setShowProgressModal(true);
    }
    
    try {
      // 调用植入API
      const response = await fetch('/api/summary/plant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          agentId: parseInt(agentId),
          moduleId,
          customerData
        })
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || '植入失败');
      }
      
      // 更新进度到100%
      setPlantProgress(100);
      setPlantResults(result.data);
      
      // 显示结果
      const { summary } = result.data;
      if (summary.failed === 0) {
        message.success(`植入成功！共处理 ${summary.total} 条数据`);
      } else {
        message.warning(`部分植入成功：成功 ${summary.success} 条，失败 ${summary.failed} 条`);
      }
      
      // 调用成功回调
      if (onPlantSuccess) {
        onPlantSuccess(result.data);
      }
      
      // 自动关闭进度弹窗
      if (autoClose && showProgress) {
        setTimeout(() => {
          setShowProgressModal(false);
        }, 2000);
      }
      
    } catch (error) {
      console.error('植入失败:', error);
      message.error('植入失败: ' + error.message);
      
      // 调用失败回调
      if (onPlantError) {
        onPlantError(error);
      }
      
      // 关闭进度弹窗
      if (showProgress) {
        setShowProgressModal(false);
      }
    } finally {
      setIsPlanting(false);
    }
  };
  
  // 模拟进度更新（实际应用中可以通过WebSocket或轮询获取真实进度）
  React.useEffect(() => {
    if (isPlanting && showProgress) {
      const interval = setInterval(() => {
        setPlantProgress(prev => {
          if (prev >= 90) {
            clearInterval(interval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 200);
      
      return () => clearInterval(interval);
    }
  }, [isPlanting, showProgress]);
  
  // 渲染进度弹窗
  const renderProgressModal = () => (
    <Modal
      title="数据植入进度"
      open={showProgressModal}
      footer={plantResults ? [
        <Button key="close" onClick={() => setShowProgressModal(false)}>
          关闭
        </Button>
      ] : null}
      closable={!isPlanting}
      maskClosable={false}
      width={600}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 进度条 */}
        <Progress
          percent={Math.round(plantProgress)}
          status={isPlanting ? 'active' : plantResults?.summary.failed === 0 ? 'success' : 'exception'}
          strokeColor={plantResults?.summary.failed === 0 ? '#52c41a' : '#faad14'}
        />
        
        {/* 状态信息 */}
        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
          {isPlanting ? (
            <Space>
              <LoadingOutlined />
              <Text>正在植入数据到汇总表...</Text>
            </Space>
          ) : plantResults ? (
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <Text>植入完成</Text>
            </Space>
          ) : null}
        </div>
        
        {/* 结果详情 */}
        {plantResults && (
          <div>
            <Title level={5}>植入结果</Title>
            <div style={{ 
              padding: '12px', 
              backgroundColor: '#f6ffed', 
              border: '1px solid #b7eb8f',
              borderRadius: '6px',
              marginBottom: '16px'
            }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text>总数据量: <strong>{plantResults.summary.total}</strong></Text>
                <Text>成功植入: <strong style={{ color: '#52c41a' }}>{plantResults.summary.success}</strong></Text>
                {plantResults.summary.failed > 0 && (
                  <Text>植入失败: <strong style={{ color: '#ff4d4f' }}>{plantResults.summary.failed}</strong></Text>
                )}
              </Space>
            </div>
            
            {/* 错误详情 */}
            {plantResults.errors && plantResults.errors.length > 0 && (
              <div>
                <Title level={5} style={{ color: '#ff4d4f' }}>失败详情</Title>
                <List
                  size="small"
                  dataSource={plantResults.errors}
                  renderItem={error => (
                    <List.Item>
                      <Text type="danger">
                        {error.customerName}: {error.error}
                      </Text>
                    </List.Item>
                  )}
                  style={{
                    maxHeight: '200px',
                    overflow: 'auto',
                    border: '1px solid #ffccc7',
                    borderRadius: '6px',
                    padding: '8px'
                  }}
                />
              </div>
            )}
          </div>
        )}
      </Space>
    </Modal>
  );
  
  return (
    <>
      <Button
        type={type}
        size={size}
        icon={isPlanting ? <LoadingOutlined /> : <SaveOutlined />}
        onClick={handlePlant}
        loading={isPlanting}
        disabled={disabled || isPlanting || customerData.length === 0}
        style={{
          backgroundColor: type === 'primary' ? '#fa8c16' : undefined,
          borderColor: type === 'primary' ? '#fa8c16' : undefined,
          ...style
        }}
        className={className}
      >
        {isPlanting ? '植入中...' : '保存植入用'}
        {!isPlanting && <UploadOutlined style={{ marginLeft: '4px' }} />}
      </Button>
      
      {/* 进度弹窗 */}
      {showProgress && renderProgressModal()}
      
      {/* 样式 */}
      <style jsx>{`
        .plant-button-success {
          background-color: #52c41a !important;
          border-color: #52c41a !important;
        }
        
        .plant-button-success:hover {
          background-color: #73d13d !important;
          border-color: #73d13d !important;
        }
      `}</style>
    </>
  );
};

export default PlantToSummaryButton;
