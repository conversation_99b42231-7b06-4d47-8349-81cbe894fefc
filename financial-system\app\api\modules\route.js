import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import ModuleCalculationEngine from '../../../lib/moduleCalculations.js';

const prisma = new PrismaClient();

// GET - 获取所有模块数据
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customerId');
    const moduleNumber = searchParams.get('module');
    
    if (!customerId) {
      return NextResponse.json({ error: '缺少客户ID' }, { status: 400 });
    }
    
    let result = {};
    
    if (moduleNumber) {
      // 获取特定模块数据
      const modelName = `module${moduleNumber}`;
      const data = await prisma[modelName].findMany({
        where: { customerId: parseInt(customerId) }
      });
      result[`module${moduleNumber}`] = data;
    } else {
      // 获取所有模块数据
      const [module1, module2, module3, module4, module5, module6, module7] = await Promise.all([
        prisma.module1.findMany({ where: { customerId: parseInt(customerId) } }),
        prisma.module2.findMany({ where: { customerId: parseInt(customerId) } }),
        prisma.module3.findMany({ where: { customerId: parseInt(customerId) } }),
        prisma.module4.findMany({ where: { customerId: parseInt(customerId) } }),
        prisma.module5.findMany({ where: { customerId: parseInt(customerId) } }),
        prisma.module6.findMany({ where: { customerId: parseInt(customerId) } }),
        prisma.module7.findMany({ where: { customerId: parseInt(customerId) } })
      ]);
      
      result = {
        module1, module2, module3, module4, module5, module6, module7
      };
    }
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('获取模块数据失败:', error);
    return NextResponse.json({ error: '获取数据失败' }, { status: 500 });
  }
}

// POST - 创建新的模块数据
export async function POST(request) {
  try {
    const body = await request.json();
    const { customerId, moduleNumber, data } = body;
    
    if (!customerId || !moduleNumber || !data) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }
    
    // 计算结果
    const calculations = ModuleCalculationEngine.calculate(moduleNumber, data);
    const completeData = { ...data, ...calculations, customerId: parseInt(customerId) };
    
    // 保存到对应的模块表
    const modelName = `module${moduleNumber}`;
    const result = await prisma[modelName].create({
      data: completeData
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('创建模块数据失败:', error);
    return NextResponse.json({ error: '创建数据失败' }, { status: 500 });
  }
}

// PATCH - 更新模块数据
export async function PATCH(request) {
  try {
    const body = await request.json();
    const { id, moduleNumber, data } = body;
    
    if (!id || !moduleNumber || !data) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }
    
    // 重新计算结果
    const calculations = ModuleCalculationEngine.calculate(moduleNumber, data);
    const updateData = { ...data, ...calculations };
    
    // 更新对应的模块表
    const modelName = `module${moduleNumber}`;
    const result = await prisma[modelName].update({
      where: { id: parseInt(id) },
      data: updateData
    });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('更新模块数据失败:', error);
    return NextResponse.json({ error: '更新数据失败' }, { status: 500 });
  }
}

// DELETE - 删除模块数据
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const moduleNumber = searchParams.get('module');
    
    if (!id || !moduleNumber) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }
    
    // 删除对应模块的数据
    const modelName = `module${moduleNumber}`;
    await prisma[modelName].delete({
      where: { id: parseInt(id) }
    });
    
    return NextResponse.json({ message: '删除成功' });
  } catch (error) {
    console.error('删除模块数据失败:', error);
    return NextResponse.json({ error: '删除数据失败' }, { status: 500 });
  }
}
