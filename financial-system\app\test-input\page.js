'use client';

import React, { useState } from 'react';
import { Card, Input, Space, Typography, Row, Col } from 'antd';

const { Title, Text } = Typography;

export default function TestInputPage() {
  const [values, setValues] = useState({
    data1: '',
    data2: '',
    data3: '',
    data4: '',
  });

  // 数字输入验证函数 - 支持正数和负数，根据字段设置不同的小数位限制
  const validateNumberInput = (value, field = 'default') => {
    // 移除所有非数字字符（除了小数点和负号）
    const cleanValue = value.replace(/[^0-9.-]/g, '');

    // 处理负号：确保负号只能在开头
    let processedValue = cleanValue;
    const hasNegative = cleanValue.includes('-');
    if (hasNegative) {
      // 移除所有负号
      const withoutNegative = cleanValue.replace(/-/g, '');
      // 如果原来有负号，在开头添加一个
      processedValue = cleanValue.startsWith('-') ? '-' + withoutNegative : withoutNegative;
    }

    // 确保只有一个小数点
    const parts = processedValue.split('.');
    if (parts.length > 2) {
      // 只保留第一个小数点，合并后面的数字部分
      const mergedDecimal = parts.slice(1).join('');
      processedValue = parts[0] + '.' + mergedDecimal;
    }

    // 根据字段设置不同的小数位限制
    let maxDecimalPlaces = 2; // 默认2位小数
    if (field === 'data3') {
      maxDecimalPlaces = 3; // 数据3允许3位小数
    } else if (field === 'data4') {
      maxDecimalPlaces = 8; // 数据4允许8位小数
    }

    // 重新分割处理后的值来限制小数位
    const finalParts = processedValue.split('.');
    if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
      processedValue = finalParts[0] + '.' + finalParts[1].substring(0, maxDecimalPlaces);
    }

    // 确保不以小数点开头（考虑负号）
    if (processedValue.startsWith('.')) {
      processedValue = '0' + processedValue;
    } else if (processedValue.startsWith('-.')) {
      processedValue = '-0' + processedValue.substring(1);
    }

    return processedValue;
  };

  const handleInputChange = (field, value) => {
    const validatedValue = validateNumberInput(value, field);
    setValues(prev => ({
      ...prev,
      [field]: validatedValue
    }));
  };

  // 不再阻止负号输入

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>输入验证测试页面</Title>
      
      <Card title="数据输入测试" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>数据1 (最多2位小数)</Text>
              <Input
                placeholder="例如: 123.45"
                value={values.data1}
                onChange={(e) => handleInputChange('data1', e.target.value)}
                style={{ textAlign: 'right' }}
              />
              <Text type="secondary">当前值: {values.data1}</Text>
            </Space>
          </Col>
          
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>数据2 (最多2位小数)</Text>
              <Input
                placeholder="例如: 123.45"
                value={values.data2}
                onChange={(e) => handleInputChange('data2', e.target.value)}
                style={{ textAlign: 'right' }}
              />
              <Text type="secondary">当前值: {values.data2}</Text>
            </Space>
          </Col>
          
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>数据3 (最多3位小数)</Text>
              <Input
                placeholder="例如: 123.456"
                value={values.data3}
                onChange={(e) => handleInputChange('data3', e.target.value)}
                style={{ textAlign: 'right' }}
              />
              <Text type="secondary">当前值: {values.data3}</Text>
            </Space>
          </Col>
          
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>数据4 (最多8位小数)</Text>
              <Input
                placeholder="例如: 123.12345678"
                value={values.data4}
                onChange={(e) => handleInputChange('data4', e.target.value)}
                style={{ textAlign: 'right' }}
              />
              <Text type="secondary">当前值: {values.data4}</Text>
            </Space>
          </Col>
        </Row>
      </Card>

      <Card title="测试说明">
        <Space direction="vertical">
          <Text>请尝试以下测试：</Text>
          <Text>• 数据1: 输入 "123.456" 应该变为 "123.45"</Text>
          <Text>• 数据2: 输入 "123.456" 应该变为 "123.45"</Text>
          <Text>• 数据3: 输入 "123.4567" 应该变为 "123.456"</Text>
          <Text>• 数据4: 输入 "123.123456789" 应该变为 "123.12345678"</Text>
          <Text>• 所有字段: 输入 "-123.45" 应该保持为 "-123.45" (支持负数)</Text>
          <Text>• 所有字段: 输入 "abc123.45def" 应该变为 "123.45"</Text>
        </Space>
      </Card>
    </div>
  );
}
