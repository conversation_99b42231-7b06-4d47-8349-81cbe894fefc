'use client';

// 公司年利润表页面 - JavaScript版本
// 避免TypeScript版本冲突

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Layout, 
  Table, 
  Button, 
  Card, 
  DatePicker, 
  Space,
  Statistic,
  Row,
  Col,
  message
} from 'antd';
import { 
  ArrowLeftOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAgentStore } from '../../../store/agentStore';
import { useCustomerStore } from '../../../store/customerStore';
import { FormulaEngine } from '../../../lib/calculations';
import dayjs from 'dayjs';

const { Header, Content } = Layout;

export default function YearlyProfitPage() {
  const router = useRouter();
  const { agents, fetchAgents } = useAgentStore();
  const { customers, fetchCustomers } = useCustomerStore();
  
  const [loading, setLoading] = useState(false);
  const [selectedYear, setSelectedYear] = useState(dayjs());
  const [reportData, setReportData] = useState([]);
  const [summary, setSummary] = useState({});

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    
    // 加载基础数据
    loadData();
  }, [router]);

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      await fetchAgents();
      await fetchCustomers(); // 获取所有客户数据
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成报表数据
  useEffect(() => {
    if (agents.length > 0 && customers.length > 0) {
      generateReportData();
    }
  }, [agents, customers, selectedYear]);

  const generateReportData = () => {
    const data = agents.map(agent => {
      // 获取该代理商的客户
      const agentCustomers = customers.filter(c => c.agentId === agent.id);
      
      // 计算年度数据
      let balance = 0; // 余额
      let reduction = 0; // 减免
      let weeklyProfit = 0; // 本周利润
      
      agentCustomers.forEach(customer => {
        // 余额计算：基于数据1和数据2
        balance += (customer.data1 || 0) + (customer.data2 || 0);
        
        // 减免计算：基于数据3和数据5的百分比
        reduction += ((customer.data1 || 0) * (customer.data3 || 0) / 100) + 
                    ((customer.data2 || 0) * (customer.data5 || 0) / 100);
        
        // 本周利润：基于计算结果
        weeklyProfit += (customer.data6 || 0) + (customer.data7 || 0) + (customer.data8 || 0);
      });
      
      // 合计 = 余额 + 减免 + 本周利润
      const total = balance + reduction + weeklyProfit;
      
      return {
        key: agent.id,
        agentName: agent.name,
        customerCount: agentCustomers.length,
        balance,
        reduction,
        weeklyProfit,
        total,
      };
    });
    
    setReportData(data);
    
    // 计算汇总
    const totalSummary = {
      totalAgents: data.length,
      totalCustomers: data.reduce((sum, item) => sum + item.customerCount, 0),
      totalBalance: data.reduce((sum, item) => sum + item.balance, 0),
      totalReduction: data.reduce((sum, item) => sum + item.reduction, 0),
      totalWeeklyProfit: data.reduce((sum, item) => sum + item.weeklyProfit, 0),
      grandTotal: data.reduce((sum, item) => sum + item.total, 0),
    };
    
    setSummary(totalSummary);
  };

  // 表格列定义
  const columns = [
    {
      title: '代理名/下客户',
      dataIndex: 'agentName',
      width: 200,
      fixed: 'left',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            客户数: {record.customerCount}
          </div>
        </div>
      ),
    },
    {
      title: '余额',
      dataIndex: 'balance',
      width: 150,
      render: (value) => (
        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>
          {FormulaEngine.formatData.formatData1(value)}
        </span>
      ),
    },
    {
      title: '减免',
      dataIndex: 'reduction',
      width: 150,
      render: (value) => (
        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
          {FormulaEngine.formatData.formatData1(value)}
        </span>
      ),
    },
    {
      title: '本周利润',
      dataIndex: 'weeklyProfit',
      width: 150,
      render: (value) => (
        <span style={{ 
          color: value >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {FormulaEngine.formatData.formatData1(value)}
        </span>
      ),
    },
    {
      title: '合计',
      dataIndex: 'total',
      width: 150,
      render: (value) => (
        <span style={{ 
          color: value >= 0 ? '#1890ff' : '#ff4d4f',
          fontWeight: 'bold',
          fontSize: '16px'
        }}>
          {FormulaEngine.formatData.formatData1(value)}
        </span>
      ),
    },
  ];

  // 添加总计行
  const dataWithTotal = [
    ...reportData,
    {
      key: 'total',
      agentName: '总计',
      customerCount: summary.totalCustomers,
      balance: summary.totalBalance,
      reduction: summary.totalReduction,
      weeklyProfit: summary.totalWeeklyProfit,
      total: summary.grandTotal,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ 
        background: '#fff', 
        padding: '0 24px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => router.push('/dashboard')}
            style={{ marginRight: '16px' }}
          >
            返回
          </Button>
          <h1 style={{ margin: 0, fontSize: '20px' }}>
            公司年利润表
          </h1>
        </div>
        <Space>
          <DatePicker 
            picker="year"
            value={selectedYear}
            onChange={setSelectedYear}
            format="YYYY年"
          />
          <Button 
            icon={<ReloadOutlined />}
            onClick={loadData}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => message.info('导出功能开发中...')}
          >
            导出
          </Button>
        </Space>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        {/* 汇总统计 */}
        <Card title="年度汇总统计" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={4}>
              <Statistic title="代理商总数" value={summary.totalAgents || 0} />
            </Col>
            <Col span={4}>
              <Statistic title="客户总数" value={summary.totalCustomers || 0} />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总余额" 
                value={summary.totalBalance || 0}
                valueStyle={{ color: '#1890ff' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总减免" 
                value={summary.totalReduction || 0}
                valueStyle={{ color: '#52c41a' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总利润" 
                value={summary.totalWeeklyProfit || 0}
                valueStyle={{ color: (summary.totalWeeklyProfit || 0) >= 0 ? '#3f8600' : '#cf1322' }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
            <Col span={4}>
              <Statistic 
                title="总合计" 
                value={summary.grandTotal || 0}
                valueStyle={{ 
                  color: (summary.grandTotal || 0) >= 0 ? '#1890ff' : '#cf1322',
                  fontSize: '20px',
                  fontWeight: 'bold'
                }}
                formatter={(value) => FormulaEngine.formatData.formatData1(value)}
              />
            </Col>
          </Row>
        </Card>

        {/* 年利润表 */}
        <Card title={`年利润表 (${selectedYear?.format('YYYY')}年)`}>
          <Table 
            columns={columns} 
            dataSource={dataWithTotal}
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
            scroll={{ x: 800 }}
            size="middle"
            bordered
            rowClassName={(record) => record.key === 'total' ? 'total-row' : ''}
            loading={loading}
          />
        </Card>

        {/* 详细说明 */}
        <Card title="计算说明" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <h4>计算公式：</h4>
              <ul>
                <li><strong>余额</strong> = 数据1 + 数据2</li>
                <li><strong>减免</strong> = (数据1 × 数据3%) + (数据2 × 数据5%)</li>
                <li><strong>本周利润</strong> = 数据6 + 数据7 + 数据8</li>
                <li><strong>合计</strong> = 余额 + 减免 + 本周利润</li>
              </ul>
            </Col>
            <Col span={12}>
              <h4>数据说明：</h4>
              <ul>
                <li><strong>数据1-2</strong>：基础金额数据</li>
                <li><strong>数据3,5</strong>：百分比数据</li>
                <li><strong>数据6-8</strong>：系统计算结果</li>
                <li><strong>总计行</strong>：所有代理商数据汇总</li>
              </ul>
            </Col>
          </Row>
        </Card>
      </Content>
    </Layout>
  );
}
