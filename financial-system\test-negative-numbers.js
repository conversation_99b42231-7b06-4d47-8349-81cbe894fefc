// 测试负数支持功能
// 验证所有数据字段都支持负数输入

// 复制更新后的验证函数
function validateNumberInput(value, field = 'default') {
  // 移除所有非数字字符（除了小数点和负号）
  const cleanValue = value.replace(/[^0-9.-]/g, '');

  // 处理负号：确保负号只能在开头
  let processedValue = cleanValue;
  const hasNegative = cleanValue.includes('-');
  if (hasNegative) {
    // 移除所有负号
    const withoutNegative = cleanValue.replace(/-/g, '');
    // 如果原来有负号，在开头添加一个
    processedValue = cleanValue.startsWith('-') ? '-' + withoutNegative : withoutNegative;
  }

  // 确保只有一个小数点
  const parts = processedValue.split('.');
  if (parts.length > 2) {
    // 只保留第一个小数点，合并后面的数字部分
    const mergedDecimal = parts.slice(1).join('');
    processedValue = parts[0] + '.' + mergedDecimal;
  }

  // 根据字段设置不同的小数位限制
  let maxDecimalPlaces = 2; // 默认2位小数
  if (field === 'data3') {
    maxDecimalPlaces = 3; // 数据3允许3位小数
  } else if (field === 'data4') {
    maxDecimalPlaces = 8; // 数据4允许8位小数
  }

  // 重新分割处理后的值来限制小数位
  const finalParts = processedValue.split('.');
  if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
    processedValue = finalParts[0] + '.' + finalParts[1].substring(0, maxDecimalPlaces);
  }

  // 确保不以小数点开头（考虑负号）
  if (processedValue.startsWith('.')) {
    processedValue = '0' + processedValue;
  } else if (processedValue.startsWith('-.')) {
    processedValue = '-0' + processedValue.substring(1);
  }

  return processedValue;
}

// 负数测试用例
const negativeTestCases = [
  // 基本负数测试
  { input: '-123.45', field: 'data1', expected: '-123.45', description: '数据1: 基本负数' },
  { input: '-123.45', field: 'data2', expected: '-123.45', description: '数据2: 基本负数' },
  { input: '-123.456', field: 'data3', expected: '-123.456', description: '数据3: 负数3位小数' },
  { input: '-123.12345678', field: 'data4', expected: '-123.12345678', description: '数据4: 负数8位小数' },
  
  // 负数小数位截断测试
  { input: '-123.456', field: 'data1', expected: '-123.45', description: '数据1: 负数小数位截断' },
  { input: '-123.4567', field: 'data3', expected: '-123.456', description: '数据3: 负数小数位截断' },
  { input: '-123.123456789', field: 'data4', expected: '-123.12345678', description: '数据4: 负数小数位截断' },
  
  // 负号位置测试
  { input: '123-45', field: 'data1', expected: '12345', description: '中间负号应被移除' },
  { input: '123.45-', field: 'data1', expected: '123.45', description: '末尾负号应被移除' },
  { input: '--123.45', field: 'data1', expected: '-123.45', description: '多个负号应合并为一个' },
  
  // 负数特殊情况
  { input: '-.123', field: 'data3', expected: '-0.123', description: '负数小数点开头' },
  { input: '-0.123', field: 'data3', expected: '-0.123', description: '负零小数' },
  { input: '-0', field: 'data1', expected: '-0', description: '负零' },
  
  // 混合字符测试
  { input: 'abc-123.45def', field: 'data1', expected: '-123.45', description: '包含字母的负数' },
  { input: '-abc123.45def', field: 'data1', expected: '-123.45', description: '负号开头包含字母' },
  
  // 边界情况
  { input: '-', field: 'data1', expected: '-', description: '只有负号' },
  { input: '-.', field: 'data1', expected: '-0.', description: '负号加小数点' },
];

function runNegativeTests() {
  console.log('🧪 开始测试负数支持功能...\n');
  
  let passedTests = 0;
  let totalTests = negativeTestCases.length;
  
  negativeTestCases.forEach((testCase, index) => {
    const result = validateNumberInput(testCase.input, testCase.field);
    const passed = result === testCase.expected;
    
    console.log(`测试 ${index + 1}: ${testCase.description}`);
    console.log(`  输入: "${testCase.input}" (字段: ${testCase.field})`);
    console.log(`  期望: "${testCase.expected}"`);
    console.log(`  实际: "${result}"`);
    console.log(`  结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`📊 负数测试总结:`);
  console.log(`  总测试数: ${totalTests}`);
  console.log(`  通过数: ${passedTests}`);
  console.log(`  失败数: ${totalTests - passedTests}`);
  console.log(`  通过率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有负数测试通过！负数支持功能正常工作。');
  } else {
    console.log('\n⚠️ 部分负数测试失败，需要检查验证逻辑。');
  }
}

// 综合测试（包括正数和负数）
function runComprehensiveTests() {
  console.log('\n🔄 运行综合测试（正数 + 负数）...\n');
  
  const comprehensiveTests = [
    // 正数测试
    { input: '123.45', field: 'data1', expected: '123.45', description: '正数基本测试' },
    { input: '123.456', field: 'data3', expected: '123.456', description: '正数3位小数' },
    { input: '123.12345678', field: 'data4', expected: '123.12345678', description: '正数8位小数' },
    
    // 负数测试
    { input: '-123.45', field: 'data1', expected: '-123.45', description: '负数基本测试' },
    { input: '-123.456', field: 'data3', expected: '-123.456', description: '负数3位小数' },
    { input: '-123.12345678', field: 'data4', expected: '-123.12345678', description: '负数8位小数' },
    
    // 零值测试
    { input: '0', field: 'data1', expected: '0', description: '零值测试' },
    { input: '-0', field: 'data1', expected: '-0', description: '负零测试' },
    { input: '0.000', field: 'data3', expected: '0.000', description: '零值小数测试' },
  ];
  
  let passed = 0;
  comprehensiveTests.forEach((test, index) => {
    const result = validateNumberInput(test.input, test.field);
    const success = result === test.expected;
    console.log(`${index + 1}. ${test.description}: ${success ? '✅' : '❌'} (${test.input} → ${result})`);
    if (success) passed++;
  });
  
  console.log(`\n综合测试结果: ${passed}/${comprehensiveTests.length} 通过`);
}

// 运行所有测试
console.log('🚀 负数支持功能测试\n');
runNegativeTests();
runComprehensiveTests();

console.log('\n📝 功能说明:');
console.log('✅ 所有数据字段现在都支持负数输入');
console.log('✅ 数据1, 数据2, 数据5, 数据6, 数据7: 最多2位小数');
console.log('✅ 数据3: 最多3位小数');
console.log('✅ 数据4: 最多8位小数');
console.log('✅ 负号只能出现在数字开头');
console.log('✅ 非数字字符会被自动过滤（保留负号和小数点）');
console.log('✅ 以小数点开头会自动添加0（包括负数）');

console.log('\n🔧 请在浏览器中测试:');
console.log('1. 访问 http://localhost:3000/test-input');
console.log('2. 访问 http://localhost:3000/formula-config');
console.log('3. 尝试输入负数，如: -123.45');
console.log('4. 验证小数位限制仍然有效');
