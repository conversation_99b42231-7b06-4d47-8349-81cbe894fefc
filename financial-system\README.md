# 财务管理系统 v1.0

这是一个基于 Next.js 和 Ant Design 的财务管理系统，支持动态公式配置、股东信息管理和数据持久化。

## ✨ 最新功能 (v1.0)

### 🎯 股东信息手动输入功能
- **完全自定义股东名称** - 支持中文、英文、数字等各种字符
- **智能同步保护** - 保护用户手动输入，避免意外覆盖
- **股东比例验证** - 自动限制0-1范围，最多2位小数
- **数据持久化** - 自动保存到SQLite3数据库
- **多股东管理** - 支持添加、编辑、删除多个股东

### 🔧 技术优化
- **API错误修复** - 解决ES6模块导入问题
- **输入验证增强** - 负数防护、小数位限制
- **数据库集成** - SQLite3 + localStorage双重保障
- **UI组件更新** - 修复Antd废弃API警告

## 🚀 技术栈

- **前端**: Next.js 14, React 18, Ant Design 5
- **数据库**: SQLite3 + better-sqlite3
- **状态管理**: React Hooks + localStorage
- **样式**: CSS Modules + Ant Design
- **测试**: Playwright (已清理)

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
