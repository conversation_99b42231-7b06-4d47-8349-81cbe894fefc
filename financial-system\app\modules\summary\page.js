'use client';

import { useState, useEffect } from 'react';
import { Layout, Card, Table, Button, Space, Typography, Row, Col, Statistic, Tabs } from 'antd';
import { useRouter } from 'next/navigation';
import { Bar<PERSON>hartOutlined, PieChartOutlined, LineChartOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;
// const { TabPane } = Tabs; // 移除过时的TabPane

export default function ModulesSummaryPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [allModulesData, setAllModulesData] = useState({});
  const [activeTab, setActiveTab] = useState('overview');

  // 检查登录状态
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }
    fetchAllModulesData();
  }, []);

  // 获取所有模块数据
  const fetchAllModulesData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/modules?customerId=1');
      const data = await response.json();
      setAllModulesData(data);
    } catch (error) {
      console.error('获取汇总数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算汇总统计
  const calculateSummaryStats = () => {
    const stats = {
      totalRecords: 0,
      totalModules: 7,
      activeModules: 0,
      totalValue: 0
    };

    Object.keys(allModulesData).forEach(moduleKey => {
      const moduleData = allModulesData[moduleKey] || [];
      if (moduleData.length > 0) {
        stats.activeModules++;
        stats.totalRecords += moduleData.length;
        
        // 计算总价值（以第一个结果字段为准）
        moduleData.forEach(record => {
          const firstResultKey = Object.keys(record).find(key => key.startsWith('result'));
          if (firstResultKey && record[firstResultKey]) {
            stats.totalValue += record[firstResultKey];
          }
        });
      }
    });

    return stats;
  };

  // 生成模块对比表格数据
  const generateComparisonData = () => {
    const comparisonData = [];
    
    for (let i = 1; i <= 7; i++) {
      const moduleKey = `module${i}`;
      const moduleData = allModulesData[moduleKey] || [];
      
      const moduleNames = {
        1: '基础财务数据',
        2: '成本分析',
        3: '简化报表',
        4: '投资回报',
        5: '风险评估',
        6: '现金流',
        7: '预算对比'
      };

      // 计算模块汇总值
      let totalValue = 0;
      let avgValue = 0;
      
      if (moduleData.length > 0) {
        const firstResultKey = Object.keys(moduleData[0]).find(key => key.startsWith('result'));
        if (firstResultKey) {
          totalValue = moduleData.reduce((sum, record) => sum + (record[firstResultKey] || 0), 0);
          avgValue = totalValue / moduleData.length;
        }
      }

      comparisonData.push({
        key: i,
        moduleId: i,
        moduleName: moduleNames[i],
        recordCount: moduleData.length,
        totalValue: totalValue.toFixed(2),
        avgValue: avgValue.toFixed(2),
        status: moduleData.length > 0 ? '有数据' : '无数据'
      });
    }

    return comparisonData;
  };

  // 模块对比表格列定义
  const comparisonColumns = [
    {
      title: '模块编号',
      dataIndex: 'moduleId',
      width: 100,
      render: (value) => `模块${value}.1`
    },
    {
      title: '模块名称',
      dataIndex: 'moduleName',
      width: 150
    },
    {
      title: '数据行数',
      dataIndex: 'recordCount',
      width: 100,
      render: (value) => (
        <span style={{ color: value > 0 ? '#52c41a' : '#999' }}>
          {value}
        </span>
      )
    },
    {
      title: '总计值',
      dataIndex: 'totalValue',
      width: 120,
      render: (value) => `¥${value}`
    },
    {
      title: '平均值',
      dataIndex: 'avgValue',
      width: 120,
      render: (value) => `¥${value}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (value) => (
        <span style={{ 
          color: value === '有数据' ? '#52c41a' : '#999',
          fontWeight: 'bold'
        }}>
          {value}
        </span>
      )
    },
    {
      title: '操作',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button 
            size="small" 
            type="primary"
            onClick={() => router.push(`/modules/${record.moduleId}`)}
          >
            查看详情
          </Button>
        </Space>
      )
    }
  ];

  const summaryStats = calculateSummaryStats();
  const comparisonData = generateComparisonData();

  return (
    <Layout style={{ minHeight: '100vh', background: '#f0f2f5' }}>
      <Content style={{ padding: '24px' }}>
        {/* 页面标题 */}
        <div style={{ marginBottom: '24px' }}>
          <Space>
            <Button onClick={() => router.push('/modules')}>返回模块列表</Button>
            <Title level={2} style={{ margin: 0 }}>
              7组数据汇总明细表
            </Title>
          </Space>
          <Text type="secondary" style={{ display: 'block', marginTop: '8px' }}>
            查看所有数据模块的汇总统计、对比分析和详细数据
          </Text>
        </div>

        {/* 汇总统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总模块数"
                value={summaryStats.totalModules}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃模块数"
                value={summaryStats.activeModules}
                prefix={<PieChartOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总数据行数"
                value={summaryStats.totalRecords}
                prefix={<LineChartOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总计算价值"
                value={summaryStats.totalValue}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 标签页内容 */}
        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <Tabs.TabPane tab="模块概览" key="overview">
              <Table
                columns={comparisonColumns}
                dataSource={comparisonData}
                loading={loading}
                pagination={false}
                size="middle"
              />
            </Tabs.TabPane>

            <Tabs.TabPane tab="详细数据" key="details">
              <Space direction="vertical" style={{ width: '100%' }}>
                {[1, 2, 3, 4, 5, 6, 7].map(moduleId => {
                  const moduleData = allModulesData[`module${moduleId}`] || [];
                  const moduleNames = {
                    1: '基础财务数据',
                    2: '成本分析', 
                    3: '简化报表',
                    4: '投资回报',
                    5: '风险评估',
                    6: '现金流',
                    7: '预算对比'
                  };

                  return (
                    <Card 
                      key={moduleId}
                      title={`模块${moduleId}.1 - ${moduleNames[moduleId]}`}
                      size="small"
                      extra={
                        <Button 
                          size="small"
                          onClick={() => router.push(`/modules/${moduleId}`)}
                        >
                          查看详情
                        </Button>
                      }
                    >
                      {moduleData.length > 0 ? (
                        <div>
                          <Text>数据行数: {moduleData.length}</Text>
                          <br />
                          <Text type="secondary">
                            最近更新: {new Date(moduleData[0]?.updatedAt).toLocaleString()}
                          </Text>
                        </div>
                      ) : (
                        <Text type="secondary">暂无数据</Text>
                      )}
                    </Card>
                  );
                })}
              </Space>
            </Tabs.TabPane>

            <Tabs.TabPane tab="数据导出" key="export">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text strong>数据导出功能</Text>
                <Space>
                  <Button type="primary">导出Excel</Button>
                  <Button>导出PDF</Button>
                  <Button>导出CSV</Button>
                </Space>
                <Text type="secondary">
                  支持导出所有模块的汇总数据和详细数据，可选择导出格式和数据范围。
                </Text>
              </Space>
            </Tabs.TabPane>
          </Tabs>
        </Card>
      </Content>
    </Layout>
  );
}
