{"testTime": "2025-07-30T06:55:27.791Z", "summary": {"totalTests": 42, "passedTests": 42, "failedTests": 0, "successRate": 100}, "detailedResults": [{"testSet": "测试数据集1", "formulaId": "formula1_1", "formulaName": "公式1.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(25656+3.22*900)*1200", "result": 34264800, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula1_2", "formulaName": "公式1.2", "expression": "(data1+data2*data4)", "calculationExpression": "(25656+3.22*900)", "result": 28554, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula2_1", "formulaName": "公式2.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(25656+3.22*900)*1200", "result": 34264800, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula2_2", "formulaName": "公式2.2", "expression": "(data1+data2*data4)*data3*data5", "calculationExpression": "(25656+3.22*900)*1200*1100", "result": 37691280000, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula3_1", "formulaName": "公式3.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(25656+3.22*900)*1200", "result": 34264800, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula3_2", "formulaName": "公式3.2", "expression": "(data1+data2*data4)", "calculationExpression": "(25656+3.22*900)", "result": 28554, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula4_1", "formulaName": "公式4.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(25656+3.22*900)*1200", "result": 34264800, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula4_2", "formulaName": "公式4.2", "expression": "(data1+data2*data4)", "calculationExpression": "(25656+3.22*900)", "result": 28554, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula5_1", "formulaName": "公式5.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(25656+3.22*900)*1200", "result": 34264800, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula5_2", "formulaName": "公式5.2", "expression": "(data1+data2*data4)", "calculationExpression": "(25656+3.22*900)", "result": 28554, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula6_1", "formulaName": "公式6.1", "expression": "data1", "calculationExpression": "25656", "result": 25656, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula6_2", "formulaName": "公式6.2", "expression": "data2*data4", "calculationExpression": "3.22*900", "result": 2898, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula7_1", "formulaName": "公式7.1", "expression": "data1", "calculationExpression": "25656", "result": 25656, "status": "PASS"}, {"testSet": "测试数据集1", "formulaId": "formula7_2", "formulaName": "公式7.2", "expression": "data2*data4", "calculationExpression": "3.22*900", "result": 2898, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula1_1", "formulaName": "公式1.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(1000+2.5*1.2)*3", "result": 3009, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula1_2", "formulaName": "公式1.2", "expression": "(data1+data2*data4)", "calculationExpression": "(1000+2.5*1.2)", "result": 1003, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula2_1", "formulaName": "公式2.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(1000+2.5*1.2)*3", "result": 3009, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula2_2", "formulaName": "公式2.2", "expression": "(data1+data2*data4)*data3*data5", "calculationExpression": "(1000+2.5*1.2)*3*2", "result": 6018, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula3_1", "formulaName": "公式3.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(1000+2.5*1.2)*3", "result": 3009, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula3_2", "formulaName": "公式3.2", "expression": "(data1+data2*data4)", "calculationExpression": "(1000+2.5*1.2)", "result": 1003, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula4_1", "formulaName": "公式4.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(1000+2.5*1.2)*3", "result": 3009, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula4_2", "formulaName": "公式4.2", "expression": "(data1+data2*data4)", "calculationExpression": "(1000+2.5*1.2)", "result": 1003, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula5_1", "formulaName": "公式5.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(1000+2.5*1.2)*3", "result": 3009, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula5_2", "formulaName": "公式5.2", "expression": "(data1+data2*data4)", "calculationExpression": "(1000+2.5*1.2)", "result": 1003, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula6_1", "formulaName": "公式6.1", "expression": "data1", "calculationExpression": "1000", "result": 1000, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula6_2", "formulaName": "公式6.2", "expression": "data2*data4", "calculationExpression": "2.5*1.2", "result": 3, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula7_1", "formulaName": "公式7.1", "expression": "data1", "calculationExpression": "1000", "result": 1000, "status": "PASS"}, {"testSet": "测试数据集2", "formulaId": "formula7_2", "formulaName": "公式7.2", "expression": "data2*data4", "calculationExpression": "2.5*1.2", "result": 3, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula1_1", "formulaName": "公式1.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(50000+1.8*0.9)*2.5", "result": 125004.05, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula1_2", "formulaName": "公式1.2", "expression": "(data1+data2*data4)", "calculationExpression": "(50000+1.8*0.9)", "result": 50001.62, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula2_1", "formulaName": "公式2.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(50000+1.8*0.9)*2.5", "result": 125004.05, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula2_2", "formulaName": "公式2.2", "expression": "(data1+data2*data4)*data3*data5", "calculationExpression": "(50000+1.8*0.9)*2.5*3.2", "result": 400012.96, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula3_1", "formulaName": "公式3.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(50000+1.8*0.9)*2.5", "result": 125004.05, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula3_2", "formulaName": "公式3.2", "expression": "(data1+data2*data4)", "calculationExpression": "(50000+1.8*0.9)", "result": 50001.62, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula4_1", "formulaName": "公式4.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(50000+1.8*0.9)*2.5", "result": 125004.05, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula4_2", "formulaName": "公式4.2", "expression": "(data1+data2*data4)", "calculationExpression": "(50000+1.8*0.9)", "result": 50001.62, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula5_1", "formulaName": "公式5.1", "expression": "(data1+data2*data4)*data3", "calculationExpression": "(50000+1.8*0.9)*2.5", "result": 125004.05, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula5_2", "formulaName": "公式5.2", "expression": "(data1+data2*data4)", "calculationExpression": "(50000+1.8*0.9)", "result": 50001.62, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula6_1", "formulaName": "公式6.1", "expression": "data1", "calculationExpression": "50000", "result": 50000, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula6_2", "formulaName": "公式6.2", "expression": "data2*data4", "calculationExpression": "1.8*0.9", "result": 1.62, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula7_1", "formulaName": "公式7.1", "expression": "data1", "calculationExpression": "50000", "result": 50000, "status": "PASS"}, {"testSet": "测试数据集3", "formulaId": "formula7_2", "formulaName": "公式7.2", "expression": "data2*data4", "calculationExpression": "1.8*0.9", "result": 1.62, "status": "PASS"}], "formulaStatistics": {"公式1.1": {"pass": 3, "fail": 0, "total": 3}, "公式1.2": {"pass": 3, "fail": 0, "total": 3}, "公式2.1": {"pass": 3, "fail": 0, "total": 3}, "公式2.2": {"pass": 3, "fail": 0, "total": 3}, "公式3.1": {"pass": 3, "fail": 0, "total": 3}, "公式3.2": {"pass": 3, "fail": 0, "total": 3}, "公式4.1": {"pass": 3, "fail": 0, "total": 3}, "公式4.2": {"pass": 3, "fail": 0, "total": 3}, "公式5.1": {"pass": 3, "fail": 0, "total": 3}, "公式5.2": {"pass": 3, "fail": 0, "total": 3}, "公式6.1": {"pass": 3, "fail": 0, "total": 3}, "公式6.2": {"pass": 3, "fail": 0, "total": 3}, "公式7.1": {"pass": 3, "fail": 0, "total": 3}, "公式7.2": {"pass": 3, "fail": 0, "total": 3}}}