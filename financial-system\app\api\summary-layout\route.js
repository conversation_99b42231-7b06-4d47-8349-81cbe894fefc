// API路由 - 新布局汇总表数据处理
// 处理汇总表数据的保存、获取、更新操作

import { NextResponse } from 'next/server';

// 模拟数据存储（实际项目中应该使用数据库）
let summaryTableData = null;

/**
 * 获取汇总表数据
 * GET /api/summary-layout
 */
export async function GET(request) {
  try {
    console.log('获取汇总表数据请求');
    
    if (!summaryTableData) {
      return NextResponse.json(
        { 
          success: false, 
          message: '暂无汇总表数据',
          customerData: {}
        },
        { status: 200 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '获取汇总表数据成功',
      ...summaryTableData,
      retrievedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('获取汇总表数据失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: '获取汇总表数据失败',
        customerData: {}
      }, 
      { status: 500 }
    );
  }
}

/**
 * 保存汇总表数据
 * POST /api/summary-layout
 * Body: { customerData, formulaExpressions, timestamp, totalCustomers }
 */
export async function POST(request) {
  try {
    const body = await request.json();
    console.log('保存汇总表数据请求:', {
      totalCustomers: body.totalCustomers,
      timestamp: body.timestamp
    });

    // 验证输入数据
    if (!body.customerData) {
      return NextResponse.json(
        { success: false, error: '客户数据不能为空' },
        { status: 400 }
      );
    }

    // 数据处理和验证
    const processedData = {
      customerData: body.customerData,
      formulaExpressions: body.formulaExpressions || {},
      timestamp: body.timestamp || new Date().toISOString(),
      totalCustomers: body.totalCustomers || 0,
      savedAt: new Date().toISOString()
    };

    // 保存数据（实际项目中应该保存到数据库）
    summaryTableData = processedData;

    console.log('汇总表数据保存成功:', {
      totalCustomers: processedData.totalCustomers,
      savedAt: processedData.savedAt
    });

    return NextResponse.json({
      success: true,
      message: '汇总表数据保存成功',
      data: {
        totalCustomers: processedData.totalCustomers,
        savedAt: processedData.savedAt
      }
    });

  } catch (error) {
    console.error('保存汇总表数据失败:', error);
    return NextResponse.json(
      { success: false, error: '保存汇总表数据失败' }, 
      { status: 500 }
    );
  }
}

/**
 * 更新汇总表数据
 * PUT /api/summary-layout
 * Body: { customerData, formulaExpressions, updateType }
 */
export async function PUT(request) {
  try {
    const body = await request.json();
    console.log('更新汇总表数据请求:', body.updateType);

    if (!summaryTableData) {
      return NextResponse.json(
        { success: false, error: '没有找到要更新的数据' },
        { status: 404 }
      );
    }

    // 根据更新类型处理数据
    switch (body.updateType) {
      case 'customerData':
        summaryTableData.customerData = { ...summaryTableData.customerData, ...body.customerData };
        break;
      case 'formulaExpressions':
        summaryTableData.formulaExpressions = { ...summaryTableData.formulaExpressions, ...body.formulaExpressions };
        break;
      case 'full':
        summaryTableData = {
          ...summaryTableData,
          ...body,
          updatedAt: new Date().toISOString()
        };
        break;
      default:
        return NextResponse.json(
          { success: false, error: '无效的更新类型' },
          { status: 400 }
        );
    }

    summaryTableData.lastUpdated = new Date().toISOString();

    return NextResponse.json({
      success: true,
      message: '汇总表数据更新成功',
      data: {
        updateType: body.updateType,
        updatedAt: summaryTableData.lastUpdated
      }
    });

  } catch (error) {
    console.error('更新汇总表数据失败:', error);
    return NextResponse.json(
      { success: false, error: '更新汇总表数据失败' }, 
      { status: 500 }
    );
  }
}

/**
 * 删除汇总表数据
 * DELETE /api/summary-layout
 */
export async function DELETE(request) {
  try {
    console.log('删除汇总表数据请求');

    if (!summaryTableData) {
      return NextResponse.json(
        { success: false, error: '没有找到要删除的数据' },
        { status: 404 }
      );
    }

    // 清空数据
    summaryTableData = null;

    return NextResponse.json({
      success: true,
      message: '汇总表数据删除成功',
      deletedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('删除汇总表数据失败:', error);
    return NextResponse.json(
      { success: false, error: '删除汇总表数据失败' }, 
      { status: 500 }
    );
  }
}
